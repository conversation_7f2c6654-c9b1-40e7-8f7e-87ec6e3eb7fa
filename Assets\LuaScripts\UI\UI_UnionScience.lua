local UI_UnionScience = Class(BaseView)

function UI_UnionScience:OnInit()
    self.tabId = -1;
    self.isFirstIn = true;
    self.itemList = {};

    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
end

function UI_UnionScience:OnCreate(param)
    -- 刷新登录红点
    if LeagueManager.checkScienceRed then
        LeagueManager.checkScienceRed = false;
        UI_UPDATE(UIDefine.UI_MainFace, 50);
        UI_UPDATE(UIDefine.UI_Union, 1, 4);
        UI_UPDATE(UIDefine.UI_UnionMain, 2);
    end

    local btnInfo = {};
    local config = ConfigMgr:GetData(ConfigDefine.ID.union_tech_tag);
    for k, v in pairs(config) do
        if v.is_open == 1 then
            table.insert(btnInfo, v);
        end
    end

    self.btnList = {};
    for i = 1, #btnInfo do
        local data = btnInfo[i];
        local obj = CreateGameObjectWithParent(self.ui.m_goTog, self.ui.m_goTogList);
        SetActive(obj, true);
        local item = {root = obj, data = data,
            nameTxt = GetChild(obj, "bg/nameTxt", UEUI.Text),
            selectTxt = GetChild(obj, "selectBg/selectTxt", UEUI.Text),
            redImg = GetChild(obj, "redImg", UEUI.Image)};
        table.insert(self.btnList, item);
        
        item.nameTxt.text = LangMgr:GetLang(data.techtag_name);
        item.selectTxt.text = LangMgr:GetLang(data.techtag_name);

        AddUIComponentEventCallback(item.root, UEUI.Toggle, function(go, param)
            if go.isOn == false then
                SetUISize(go, 458, 118);
            else
                SetUISize(go, 484, 118);
                self:OnSelectType(data.id)
            end
        end);
    end
    UIRefreshLayout(self.ui.m_goTogList);
end

function UI_UnionScience:OnRefresh(types, param)
    if types == 1 then
        self:OnUpdateUI();
    elseif types == 2 then
        self:OnGuide(param)
    end
end

function UI_UnionScience:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
end

function UI_UnionScience:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "rankBtn" then
        UI_SHOW(UIDefine.UI_LeagueRank, 2);
    elseif name == "tipBtn" then
        UI_SHOW(UIDefine.UI_UnionScienceTip);
    end
end

function UI_UnionScience:OnSelectType(types)
    if self.tabId == types then return end
    self.tabId = types;
    self.fristGroupId = -1;
    
    TimeMgr:DestroyTimer(self.uiName)
    SetActive(self.ui.m_goGuide, false);
    
    self.ui.m_scrollview:StopMovement();
    SetUIPos(self.ui.m_scrollview.content, 0, 0);

    self:OnUpdateUI();

    local recommend = LeagueManager:GetScienceRecommend(self.tabId);
    if recommend > 0 then
        self.isFirstIn = false;
        self:OnShowRecommend(recommend);
    else
        self:OnShowFirst();
    end
end

function UI_UnionScience:OnUpdateUI()
    self.ui.m_txtDaily.text = LeagueManager.today_score;

    local rank = LeagueManager.week_rank;
    if rank == 0 then
        self.ui.m_txtWeek.text = "";
        self.ui.m_txtRank.text = LangMgr:GetLang(9382);
    else
        self.ui.m_txtWeek.text = rank;
        self.ui.m_txtRank.text = "";
    end

    local list = ConfigMgr:GetDataByKey(ConfigDefine.ID.union_tech_show, "type_id", self.tabId, true);
    table.sort(list, function(a, b)
        return a.sort < b.sort;
    end);

    local item;
    local index = 0;
    local rootTrans = self.ui.m_scrollview.content;
    local maxY = 0;
    for k, v in pairs(list) do
        index = index + 1;
        item = self.itemList[index];
        if item == nil then
            item = CreateGameObjectWithParent(self.ui.m_goItem, rootTrans);
            table.insert(self.itemList, item);
        end

        local line = GetChild(item, "line", UEUI.Image);
        local bg = GetChild(item, "bg");
        local icon = GetChild(item, "icon", UEUI.Image);
        local signSp = GetChild(item, "signSp", UEUI.Image);
        local levelTxt = GetChild(item, "levelTxt", UEUI.Text);
        local nameTxt = GetChild(item, "nameTxt", UEUI.Text);

        local data = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, LeagueManager:GetScienceId(v.id));

        local state;
        local unlockList = {};
        local recommend = LeagueManager:GetScienceRecommend(self.tabId);
        if data.lv == 0 then
            if data.pre_id then
                local idList = string.split(data.pre_id, ",");
                local len = #idList;
                local preConfig;
                local unlockNum = 0;
                for i = 1, len do
                    preConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, idList[i]);
                    if LeagueManager:GetScienceLv(preConfig.group_id) >= preConfig.lv then
                        unlockNum = unlockNum + 1;
                        table.insert(unlockList, preConfig.group_id);
                    end
                end
                state = unlockNum >= len and 1 or 0;
            else
                state = 1;
            end
        elseif data.lv == data.max_lv then
            state = 2;
        else
            state = 1;
        end

        if self.fristGroupId == -1 and state == 1 then
            self.fristGroupId = data.group_id;
        end

        if state == 2 then
            levelTxt.text = GetStrRichColor(LangMgr:GetLang(9379), "55ff26");
        else
            if data.lv == 0 then
                levelTxt.text = GetStrRichColor(data.lv, "ff8888") .. "/" .. data.max_lv;
            else
                levelTxt.text = data.lv .. "/" .. data.max_lv;
            end
        end
        nameTxt.text = LangMgr:GetLang(data.tech_name);

        SetImageSprite(icon, data.tech_icon, false);
        SetUIImageGray(icon, state == 0);

        local isRecommend = data.group_id == recommend;
        SetActive(signSp, state == 1 and isRecommend);
        
        RemoveUIComponentEventCallback(bg ,UEUI.Button);
        AddUIComponentEventCallback(bg, UEUI.Button, function(arg1,arg2)
            UI_SHOW(UIDefine.UI_UnionScienceUpgrade, v.id);
        end);

        local myPos = string.split(v.pos, "|");
        local offsetX = v2n(myPos[1]) - 1;
        local offsetY = v2n(myPos[2]) - 1;
        SetLocalPositionTrans(item.transform, -230 + offsetX * 230, -120 - offsetY * 350);
        SetUIIndex(item, 0);

        if v2n(myPos[2]) > maxY then
            maxY = v2n(myPos[2]);
        end

        local hasConnect = v.pre_connect ~= nil;
        if hasConnect then
            local connectList = string.split(v.pre_connect, "|");
            if #connectList > 1 then
                if state == 0 then
                    if unlockList[1] == nil then
                        SetImageSprite(line, "Sprite/ui_lianmeng/kejishu_xian3.png", true);
                        line.transform:SetLocalScale(1, -1, 1);
                    else
                        SetImageSprite(line, "Sprite/ui_lianmeng/kejishu_xian2.png", true);

                        local preConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_show, connectList[1]);
                        local prePos = string.split(preConfig.pos, "|");
                        if unlockList[1] == connectList[1] then
                            if v2n(myPos[1]) < v2n(prePos[1]) then
                                line.transform:SetLocalScale(-1, -1, 1);
                            else
                                line.transform:SetLocalScale(1, -1, 1);
                            end
                        else
                            if v2n(myPos[1]) < v2n(prePos[1]) then
                                line.transform:SetLocalScale(1, -1, 1);
                            else
                                line.transform:SetLocalScale(-1, -1, 1);
                            end
                        end
                    end
                else
                    SetImageSprite(line, "Sprite/ui_lianmeng/kejishu_xian1.png", true);
                    line.transform:SetLocalScale(1, -1, 1);
                end
                SetUIPos(line, 0, 170);
            else
                local preConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_show, connectList[1]);
                local prePos = string.split(preConfig.pos, "|");
                local imgStr;
                if v2n(myPos[1]) == v2n(prePos[1]) then
                    imgStr = state == 0 and "Sprite/ui_lianmeng/kejishu_xian4.png" or "Sprite/ui_lianmeng/kejishu_xian5.png";
                    SetImageSprite(line, imgStr, true);
                    line.transform:SetLocalScale(1, 1, 1);
                    SetUIPos(line, 0, 170);
                else
                    imgStr = state == 0 and "Sprite/ui_lianmeng/kejishu_xian7.png" or "Sprite/ui_lianmeng/kejishu_xian6.png";
                    SetImageSprite(line, imgStr, true);
                    if v2n(myPos[1]) > v2n(prePos[1]) then
                        line.transform:SetLocalScale(-1, 1, 1);
                        SetUIPos(line, -229, 170);
                    else
                        line.transform:SetLocalScale(1, 1, 1);
                        SetUIPos(line, 229, 170);
                    end
                end
            end
        end
        SetActive(line, hasConnect);
        SetActive(item, true);
    end

    local height = (maxY - 1) * 350 + 310;
    if height < 1118 then
        height = 1118;
    end
    SetUISize(rootTrans, 928, height);

    local num = #self.itemList;
    local count = table.count(list);
    if num > count then
        for i = count + 1, num do
            SetActive(self.itemList[i], false);
        end
    end
end

function UI_UnionScience:OnMoveTo(groundId, isShowGuide)
    if not groundId or groundId <= 0 then return end

    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_show, groundId);
    if config then
        local rootTrans = self.ui.m_scrollview.content;
        local dic = string.split(config.pos, "|");
        local height = 1118;
        local posY = (v2n(dic[2]) - 1) * 350;
        local curPos = GetUIPos(rootTrans);
        if posY < height or (curPos.y < posY or curPos.y > -(posY - height)) then
            SetUIPos(rootTrans, 0, -posY);
        end

        if isShowGuide then
            local posX = -230 + (v2n(dic[1]) - 1) * 230;
            SetUIPos(self.ui.m_goGuide, posX, posY);
            SetActive(self.ui.m_goGuide, true);
        end
    end
end

function UI_UnionScience:OnGuide(groundId)
    self:OnMoveTo(groundId, true);
    self:CreateScheduleFun(function()
        UI_SHOW(UIDefine.UI_UnionScienceUpgrade, groundId);
        SetActive(self.ui.m_goGuide, false);
    end, 1, 1);
end

function UI_UnionScience:OnShowRecommend(groundId)
    self:OnMoveTo(groundId, true);
    self:CreateScheduleFun(function()
        SetActive(self.ui.m_goGuide, false);
    end, 2, 1);
end

function UI_UnionScience:OnShowFirst()
    self:OnMoveTo(self.fristGroupId);

    if self.isFirstIn then
        self.isFirstIn = false;
        if self.fristGroupId > 0 and not UIMgr:GetOnly("UI_UnionScience", true) then
            local myDuty = LeagueManager:GetMyLeagueDuty();
            local pos_config = LeagueManager:GetUnionPosById(myDuty);
            if pos_config and pos_config.tech_recommend == 1 then
                UI_SHOW(UIDefine.UI_TipsOnly, "UI_UnionScience", LangMgr:GetLang(9456), function()
                    UI_SHOW(UIDefine.UI_UnionScienceUpgrade, self.fristGroupId);
                end, nil, nil, nil, nil, nil, true);
            end
        end
    end
end

return UI_UnionScience