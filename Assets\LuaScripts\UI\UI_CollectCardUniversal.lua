local UI_CollectCardUniversal = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local SeriesItem = Class(ItemBase)

local CurrentSelectCard  -- 当前选中的卡牌
local effectObj          --特效预制体
local HasSeriesEmpty

function UI_CollectCardUniversal:OnInit()
    CollectCardManager:SetMaskSize(self.ui.m_goEffect,self.ui.m_goEffectMask)
    effectObj = self.ui.m_goEffect
    SetActive(self.ui.m_goEffect, false)
end

function UI_CollectCardUniversal:OnCreate(_)
    self:InitPanel()
    self:RefreshPanel()
    self:RefreshScroll()
end

function UI_CollectCardUniversal:OnRefresh(type)
    self:RefreshPanel()
    -- 刷新滚动视图
    if type == 1 then
        self:RefreshScroll()
    -- 刷新滚动视图（不重置列表）
    elseif type == 2 then
        self:RefreshScroll(true)
    end
end

function UI_CollectCardUniversal:onDestroy()
    Tween.Kill("AutoMoveFunc")
    if self.seriesList then
        for i = 1, #self.seriesList do
            self.seriesList[i]:onDestroy()
        end
    end
    -- 清理 SeriesItem 映射表
    if self.seriesItemMap then
        for _, cellData in pairs(self.seriesItemMap) do
            if cellData.seriesItem and cellData.seriesItem.onDestroy then
                cellData.seriesItem:onDestroy()
            end
        end
        self.seriesItemMap = nil
    end
    self.seriesList = nil
    self.seriesSlider = nil
    self.seriesData = nil
    CurrentSelectCard = nil
    HasSeriesEmpty = nil
    UI_UPDATE(UIDefine.UI_CollectCardView)
end

function UI_CollectCardUniversal:onUIEventClick(go,_)
    local name = go.name
    -- 关闭
    if name == "m_btnClose" then
        UI_UPDATE(UIDefine.UI_CollectCardView)
        self:Close()
    -- 点击万能卡资源栏
    elseif name == "m_goCard" then
        UI_SHOW(UIDefine.UI_ItemTips, 60007)
    -- 点击黄金万能卡资源栏
    elseif name == "m_goGoldCard" then
        UI_SHOW(UIDefine.UI_ItemTips, 60008)
    end
end

--- 初始化界面
function UI_CollectCardUniversal:InitPanel()
    -- 初始化 TableViewD
    self:InitTableViewD()
end



--- 刷新界面
function UI_CollectCardUniversal:RefreshPanel()
    -- 万能卡资源数量
    local universalCardNum = NetCollectCardData:GetUniversalCardNum()
    self.ui.m_txtCard.text = universalCardNum
    local universalGoldCardNum = NetCollectCardData:GetUniversalGoldCardNum()
    self.ui.m_txtGoldCard.text = universalGoldCardNum
end

--- 刷新滚动视图
function UI_CollectCardUniversal:RefreshScroll(isNotReset)
    local data = NetCollectCardData:GetNotHaveCardSeries()
    if data then
        self.seriesData = data
        -- 不重置列表时，只更新数据
        if isNotReset then
            -- 有收藏册内容为空，刷新列表
            if HasSeriesEmpty then
                self.ui.m_TableViewD:ReloadData(false)
                HasSeriesEmpty = false
            else
                -- 更新现有的 cell 数据
                self.ui.m_TableViewD:updateTableView()
            end
        else
            -- 重置列表
            self.ui.m_TableViewD:ReloadData(true)
        end
    end
end

function SeriesItem:OnInit(transform)
    self.trans            = transform
    self.bg               = GetChild(transform, "bg", UE.RectTransform)
    self.title            = GetChild(transform, "bg/Image/label/text", UEUI.Text)
    self.imgSeries        = GetChild(transform, "bg/Image/Image/imgSeries", UEUI.Image)
    self.sliderProgess    = GetChild(transform, "bg/Image/Image/normal/SliderBg/sliderProgess", UEUI.Slider)
    self.txtProgress      = GetChild(transform, "bg/Image/Image/normal/SliderBg/txtProgress", UEUI.Text)
    self.rewardContent    = GetChild(transform, "bg/reward/scrollview/viewport/content")
    self.rewardItem       = GetChild(transform, "bg/reward/rewardItem")
    self.cardContent      = GetChild(transform, "bg/card/scrollview/viewport/content")
    self.cardItem         = GetChild(transform, "bg/card/cardItem")
    self.littleShow       = GetChild(transform, "bg/reward/LittleShow")
    self.rewardScroll    = GetChild(transform, "bg/reward/scrollview")
end

function SeriesItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index

    local config = CollectCardManager:GetCardCollectionItemConfig(data.id)
    if config then
        self.title.text = LangMgr:GetLang(config.lang_id)
    end

    SetUIImage(self.imgSeries, data.picture, true)

    local currentNum, totalNum = NetCollectCardData:GetCardNumOfSeries(data.id)
    self.sliderProgess.value = currentNum / totalNum
    self.txtProgress.text = string.format("%s/%s", currentNum, totalNum)

    -- 生成收藏册奖励
    local rewardStr = NetSeasonActivity:GetChangeItemId(data.reward)
    local rewardList = string.split(rewardStr, ";")
    
    self:HideAllChild(self.rewardContent.transform)
    self:HideAllChild(self.littleShow.transform)
    
    local rewardSum = #rewardList
    local isOver4 = rewardSum > 4
    local rewardParent = (not isOver4) and self.littleShow.transform or self.rewardContent.transform
    SetActive(self.rewardScroll,isOver4)
    SetActive(self.littleShow,not isOver4)
    local rewardItemCount = rewardParent.childCount

    -- 再根据奖励列表显示
    for key, value in ipairs(rewardList) do
        local rewardTable = string.split(value, "|")
        local itemID = v2n(rewardTable[1])
        local itemNum = v2n(rewardTable[2])

        local item
        -- 有可用的 item 直接获取
        if key <= rewardItemCount then
            item = rewardParent:GetChild(key - 1)
            -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(self.rewardItem, rewardParent)
        end
        
        -- 图标
        local icon = GetChild(item, "icon", UEUI.Image)
        local iconPath = ItemConfig:GetIcon(itemID)
        SetUIImage(icon, iconPath, false)
        -- 数量
        local textNum = GetChild(item, "num", UEUI.Text)
        textNum.text = "x" .. itemNum
        -- 按钮
        local button = GetChild(item, "icon", UEUI.Button)
        --button.onClick:AddListener(function ()
        --    UI_SHOW(UIDefine.UI_ItemTips, itemID)
        --end)
        RemoveUIComponentEventCallback(button, UEUI.Button)
        AddUIComponentEventCallback(button, UEUI.Button, function()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)
        
        SetActive(item, true)
    end

    -- 生成卡牌
    local cardList = string.split(data.card, "|")
    local cardItemCount = self.cardContent.transform.childCount
    -- 先全部隐藏
    for i = 1, cardItemCount, 1 do
        local item = self.cardContent.transform:GetChild(i - 1)
        SetActive(item, false)
    end
    local cardIndex = 1

    local height = 560
    local cardCount = 0

    -- 再根据卡牌列表显示
    for _, value in ipairs(cardList) do
        local cardID = v2n(value)
        local count = NetCollectCardData:GetCardNum(cardID)
        local hasCard = count > 0
        local isGold = NetCollectCardData:IsGoldCard(cardID)
        if not hasCard then
            cardCount = cardCount + 1
            local cardItem
            -- 有可用的 item 直接获取
            if cardIndex <= cardItemCount then
                cardItem = self.cardContent.transform:GetChild(cardIndex - 1)
            -- item 不够用，创建新的
            else
                cardItem = CreateGameObjectWithParent(self.cardItem, self.cardContent)
            end
            cardIndex = cardIndex + 1

            local item = GetChild(cardItem, "item")
            -- 背景
            local bg = GetChild(item, "bg", UEUI.Image)
            local titleBg = GetChild(item, "titleBg", UEUI.Image)
            -- 标题
            local title = GetChild(item, "titleBg/title", UEUI.Text)
            title.text = ItemConfig:GetLangByID(cardID)
            -- 图标
            local icon = GetChild(item, "icon", UEUI.Image)
            local iconPath = ItemConfig:GetIcon(cardID)
            SetUIImage(icon, iconPath, false)
            -- 星级
            local starIcon = GetChild(item, "starIcon", UEUI.Image)
            CollectCardManager:SetStar(cardID,starIcon)
            
            -- 数量
            local goNum = GetChild(item, "Num")
            local txtNum = GetChild(item, "Num/txtNum", UEUI.Text)
            txtNum.text = "x".. count
            -- 有重复卡牌
            local hasRepeatCard = count > 1
            SetActive(goNum, hasRepeatCard)
            -- 红点
            local redPoint = GetChild(item, "redPoint", UEUI.Image)
            local hasRedPoint = NetCollectCardData:GetRedPoint(cardID)
            if hasRedPoint then
                SetActive(redPoint, true)
            else
                SetActive(redPoint, false)
            end

            -- 刷新置灰状态
            if hasCard then
                SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title.png", false)
                SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new.png", false)
                icon.color = Color.New(1, 1, 1, 1)
                title.color = GetColorByHex("A24C04")
            else
                SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title_hui.png", false)
                SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new_hui.png", false)
                icon.color = Color.New(1, 1, 1, 0)
                title.color = GetColorByHex("575757")
            end

            CollectCardManager:ShowCardBg(bg,isGold,hasCard)
            SetUIImageGray(starIcon,not hasCard)
            
            -- 按钮
            local button = GetChild(item, "icon", UEUI.Button)
            button.onClick:RemoveAllListeners()
            button.onClick:AddListener(function ()
                if CurrentSelectCard then
                    SetActive(CurrentSelectCard.select, false)
                end
                -- 设置当前选中的卡牌
                CurrentSelectCard = {
                    cardID = cardID,
                    -- select = select
                }
                UI_SHOW(UIDefine.UI_CollectCardExchange, data.id, cardID)
            end)

            local effectRoot = GetChild(item,"effectRoot",UE.Transform)
            local childCount = effectRoot.childCount
            if isGold then
                local obj
                if childCount > 0 then
                    for i = 1,childCount-1 do
                        local child = effectRoot:GetChild(i)
                        if child then
                            UEGO.Destroy(child.gameObject)
                        end
                    end
                    obj = effectRoot:GetChild(0)
                else
                    obj = UEGO.Instantiate(effectObj,effectRoot)
                    obj.transform.localPosition = Vector3.New(0,0,0)
                end
                SetActive(obj, true)
            else
                for i = 0,childCount-1 do
                    SetActive(effectRoot:GetChild(i),false)
                end
            end
            
            SetActive(cardItem, true)
        end
    end

    height = cardCount < 6 and 560 or 790
    self:UpdateBgHeight(height)
end

function SeriesItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function SeriesItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function SeriesItem:Refresh()
    local isEmpty = true
    local cardList = string.split(self.data.card, "|")
    for _, value in ipairs(cardList) do
        local cardID = v2n(value)
        local count = NetCollectCardData:GetCardNum(cardID)
        local hasCard = count > 0
        if not hasCard then
            isEmpty = false
        end
    end
    -- 收藏册内容为空
    if isEmpty then
        HasSeriesEmpty = isEmpty
    end

    self:UpdateData(self.data, self.index)
end

function SeriesItem:onDestroy()
    UEGO.Destroy(self.trans.gameObject)
    self.trans = nil
    self.title = nil
    self.icon = nil
    self.rewardContent = nil
    self.rewardItem = nil
    self.cardContent = nil
    self.cardItem = nil
end

--隐藏该节点下所有子节点
function SeriesItem:HideAllChild(transform)
    local rewardItemCount = transform.childCount
    -- 先全部隐藏
    for i = 0, rewardItemCount-1 do
        local item = transform:GetChild(i)
        SetActive(item, false)
    end
end

function SeriesItem:UpdateBgHeight(height)
    SetUISize(self.bg, 790, height)
end

function UI_CollectCardUniversal:InitTableViewD()
    -- 创建一个表来存储每个 index 对应的 item 和 seriesItem
    self.seriesItemMap = {}

    -- 获取数据数量的回调
    self.ui.m_TableViewD.GetCellCount = function ()
        if self.seriesData then
            return #self.seriesData
        end
        return 0
    end

    -- 获取每个 cell 大小的回调
    self.ui.m_TableViewD.GetCellSize = function (_, index)
        return self:GetCellSize(index)
    end

    -- 更新 cell 内容的回调
    self.ui.m_TableViewD.UpdateCell = function (tableView, index)
        if not tableView then
            return nil
        end
        local cell = tableView:GetReusableCell()
        local item
        if not cell then
            -- 创建新的 cell
            item = UEGO.Instantiate(self.ui.m_goSeriesItem)
            SetActive(item, true)
            cell = GetAndAddComponent(item, CS.CCTableViewCell)
            item.name = v2s(index + 1)
        else
            item = cell.gameObject
        end

        -- 更新 cell 数据
        if self.seriesData and self.seriesData[index + 1] then
            -- 获取或创建 SeriesItem 实例，以 index 为 key
            local cellData = self.seriesItemMap[index + 1]
            if not cellData then
                cellData = {
                    item = item,
                    seriesItem = SeriesItem.new()
                }
                cellData.seriesItem:OnInit(item.transform)
                self.seriesItemMap[index + 1] = cellData
            else
                -- 更新 item 引用（因为 cell 可能被重用）
                cellData.item = item
                cellData.seriesItem.trans = item.transform
            end
            cellData.seriesItem:UpdateData(self.seriesData[index + 1], index + 1)
        end

        return cell
    end

    -- 更新现有 cell 数据的回调
    self.ui.m_TableViewD.UpdateCellData = function (cell, index)
        if cell and self.seriesData and self.seriesData[index + 1] then
            local cellData = self.seriesItemMap[index + 1]
            if cellData and cellData.seriesItem then
                -- 更新 item 引用
                cellData.item = cell.gameObject
                cellData.seriesItem.trans = cell.gameObject.transform
                cellData.seriesItem:UpdateData(self.seriesData[index + 1], index + 1)
            end
        end
    end

    -- 初始化完成后重新加载数据
    self.ui.m_TableViewD:ReloadData(true)
end

function UI_CollectCardUniversal:GetCellSize(index)
    local height = 560
    if self.seriesData and self.seriesData[index + 1] then
        local data = self.seriesData[index + 1]
        local cardList = string.split(data.card, "|")
        local cardCount = 0
        for _, value in ipairs(cardList) do
            local cardID = v2n(value)
            local count = NetCollectCardData:GetCardNum(cardID)
            local hasCard = count > 0
            if not hasCard then
                cardCount = cardCount + 1
            end
        end
        height = cardCount < 6 and 560 or 790
        local seriesItem, item = self:GetSeriesItemByIndex(index)
        if seriesItem then
            seriesItem:UpdateBgHeight(height)
        end
    end
    return Vector2.New(940, height)  -- 默认高度
end

-- 根据 index 获取对应的 SeriesItem 和 GameObject
function UI_CollectCardUniversal:GetSeriesItemByIndex(index)
    if self.seriesItemMap and self.seriesItemMap[index] then
        return self.seriesItemMap[index].seriesItem, self.seriesItemMap[index].item
    end
    return nil, nil
end

return UI_CollectCardUniversal