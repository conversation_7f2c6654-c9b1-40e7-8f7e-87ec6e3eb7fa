local UI_JoinUnion = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local joinItem = Class(ItemBase)
function UI_JoinUnion:OnInit()
    self.isDestory = false;
    EventMgr:Add(EventID.CLOSE_CHILD_WINDOW, self.OnCloseWin, self);
end

function UI_JoinUnion:OnCreate(param)
    if param ~= nil then
        self.sliderData = param
    else

    end
    self.lan = PlayerPrefs:GetValue(PlayerPrefsKey.LanguageID, "number")
    self.slider = require("UI.Common.SlideRect").new()
    self.slider:Init(self.uiGameObject.transform:Find("state/m_goJoinScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect)), 2)
    local itemTrans = self.ui.m_goItemJoin.transform
    local joinItems = {}
    for i = 1, 15 do
        joinItems[i] = joinItem.new()
        joinItems[i]:Init(UEGO.Instantiate(itemTrans))
    end
    self.slider:SetItems(joinItems, 10, Vector2.New(5, 10))
    --self.itemList = joinItems
    local function joinBack(data)
        local sameLan = {}
        local difLan = {}
        for k, v in pairs(data) do
            if self.lan == v2n(v.lan) then
                table.insert(sameLan, v)
            else
                table.insert(difLan, v)
            end
        end
        table.sort(sameLan, function(a, b)
            return v2n(a.members) > v2n(b.members)
        end)
        table.sort(difLan, function(a, b)
            return v2n(a.members) > v2n(b.members)
        end)
        table.insertto(sameLan, difLan)
        self.sliderData = sameLan
        if self.slider then
            self.slider:SetData(self.sliderData)
        end
        if self.ui and self.ui.m_goFind then
            SetActive(self.ui.m_goFind, true)
        end

        if self.ui and self.ui.m_goBg then
            SetActive(self.ui.m_goBg, true)
        end

        if self.ui and self.ui.m_goNot then
            SetActive(self.ui.m_goNot, #self.sliderData <= 0);
        end
    end
    LeagueManager:GetLeagueList(joinBack)

    -- local openLevel = LeagueManager:GetConfigById(2)
    -- openLevel = v2n(openLevel)
    -- local level = LevelConfig:GetLevel()
    -- SetActive(self.ui.m_goNoOpen,not (level >= openLevel))
    -- self.ui.m_txtOpenLevel.text = string.format(LangMgr:GetLang(9205),openLevel) 
end

function UI_JoinUnion:RefreshUnionList()
    local function joinBack(data)
        local sameLan = {}
        local difLan = {}
        for k, v in pairs(data) do
            if self.lan == v2n(v.lan) then
                table.insert(sameLan, v)
            else
                table.insert(difLan, v)
            end
        end
        table.sort(sameLan, function(a, b)
            return v2n(a.members) > v2n(b.members)
        end)
        table.sort(difLan, function(a, b)
            return v2n(a.members) > v2n(b.members)
        end)
        table.insertto(sameLan, difLan)
        self.sliderData = sameLan
        if self.slider then
            self.slider:SetData(self.sliderData)
        end

        if self.ui and self.ui.m_goNot then
            SetActive(self.ui.m_goNot, #self.sliderData <= 0);
        end
    end
    LeagueManager:GetLeagueList(joinBack)
end

function UI_JoinUnion:OnRefresh(param)
    if param == 1 then
        self:RefreshUnionList()
    end
end

function UI_JoinUnion:onDestroy()
    EventMgr:Remove(EventID.CLOSE_CHILD_WINDOW, self.OnCloseWin, self);
    self.isDestory = true;
    self.slider = nil
end

function UI_JoinUnion:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnFind" then
        UI_SHOW(UIDefine.UI_LeagueFind)
    end
end

function UI_JoinUnion:OnCloseWin()
    if not self.isDestory then
        self:Close();
    end
end

--------------------------

function joinItem:OnInit(transform)
    self.transform = transform.transform
end

function joinItem:UpdateData(data, index)
    --------base------------
    self.data = data
    local level = LevelConfig:GetLevel()
    local union_name = self.transform:Find("union_name"):GetComponent(typeof(UEUI.Text))
    union_name.text = data.name

    local union_img = self.transform:Find("union_img"):GetComponent(typeof(UEUI.Image))
    SetUIImage(union_img, LeagueManager:GetUnionImageById(data.pic), false)

    local btn = union_img.transform:GetComponent(typeof(UEUI.Button))
    if btn then
        RemoveUIComponentEventCallback(btn, UEUI.Button)
        AddUIComponentEventCallback(btn, UEUI.Button, function(arg1, arg2)
            LeagueManager:ShowLeagueAbout(data.id)
        end)
    end


    --部落类型
    local str, _ = LeagueManager:GetApplyStrByType(data.applyType)
    local typeTxt = GetChild(self.transform, "Type/txt", UEUI.Text)
    typeTxt.text = str

    --等级要求
    local levelTxt = GetChild(self.transform, "Level/txt", UEUI.Text)
    levelTxt.text = string.format(level >= v2n(data.applyLevel) and "<color=#3662BA>%s</color>" or "<color=#FF0800>%s</color>", "Lv." .. data.applyLevel)

    --成员数量
    local memberTxt = GetChild(self.transform, "Member/txt", UEUI.Text)
    memberTxt.text = data.members .. "/" .. data.membersMax

    local union_request = self.transform:Find("union_request"):GetComponent(typeof(UEUI.Button))
    local request_txt = self.transform:Find("union_request/request_txt"):GetComponent(typeof(UEUI.Text))
    local requestBtnBg = GetChild(union_request, "bg", UEUI.Image);
    union_request.interactable = true
    RemoveUIComponentEventCallback(union_request, UEUI.Button)
    if data.status == 0 then
        --9210
        request_txt.text = LangMgr:GetLang(9210)
        --SetUIBtnGray(union_request.gameObject, true)
        SetUIImageGray(requestBtnBg, false)
        UnifyOutline(request_txt.gameObject, "056E00")
        AddUIComponentEventCallback(union_request, UEUI.Button, function(arg1, arg2)
            if level >= v2n(data.applyLevel) then
                local function applyBack()
                    --SetUIBtnGray(union_request.gameObject, false)
                    SetUIImageGray(requestBtnBg, true)
                    UnifyOutline(request_txt.gameObject, "5D5D5D")
                    if v2n(data.applyType) == 0 then
                        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9226))
                    else
                        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9024))
                        --union_request.interactable = false
                        --request_txt.text = LangMgr:GetLang(9211)
                        --SetUIBtnGray(union_request.gameObject,false)

                        request_txt.text = LangMgr:GetLang(9024)
                        union_request.interactable = false
                    end
                    data.status = 1
                end
                LeagueManager:AppleLeague(data.id, data.applyType, data.applyLevel, applyBack, data)
            else
                UIMgr:Show(UIDefine.UI_WidgetTip, LangMgr:GetLang(9274))
            end
        end)
    else
        union_request.interactable = false
        request_txt.text = LangMgr:GetLang(9211)
        --SetUIBtnGray(union_request.gameObject, false)
        SetUIImageGray(requestBtnBg, true)
        UnifyOutline(request_txt.gameObject, "5D5D5D")
    end

    local union_lanague = self.transform:Find("union_lanague"):GetComponent(typeof(UEUI.Text))
    union_lanague.text = FriendManager:GetLanguage(data)
    --local path = 
    --name2.text = " "
    --local txt_name = self.transform:Find("txt_name"):GetComponent(typeof(UEUI.Text))
    --txt_name.text = data.name
    --local head = self.transform:Find("head/img_head"):GetComponent(typeof(UEUI.Image))
    --local HeadIcon = FriendManager:GetHeadIcon(data.headId)
    --if HeadIcon then
    --SetImageSprite(head,HeadIcon,false)
    --end

    --local rank = self.transform:Find("img_rank"):GetComponent(typeof(UEUI.Image))
    --local rankIcon = FriendManager:GetRankStageIcon(data.rankStage)
    --SetImageSprite(rank,rankIcon,false)
    --local txt_level = self.transform:Find("head/txt_level"):GetComponent(typeof(UEUI.Text))
    --txt_level.text = data.level

    --local txt_online = self.transform:Find("txt_online"):GetComponent(typeof(UEUI.Text))
    --local isOnLine,str = FriendManager:IsOnLine(data)
    --txt_online.text = str

    --local txt_lang = self.transform:Find("txt_lang"):GetComponent(typeof(UEUI.Text))
    --txt_lang.text = FriendManager:GetLanguage(data)
    ----------base------------
    --self:AddHeadBtn()
    --self:onUpdateDate(data,index)
end

function joinItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function joinItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

return UI_JoinUnion