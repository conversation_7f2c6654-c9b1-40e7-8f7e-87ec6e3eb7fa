local UI_CollectCardUniversalModel = {}

UI_CollectCardUniversalModel.config = {["name"] = "UI_CollectCardUniversal", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_CollectCardUniversalModel:Init(c)
    c.ui = {}    
    c.ui.m_goEffect = GetChild(c.uiGameObject,"bg/m_goEffect")
    c.ui.m_goExchange = GetChild(c.uiGameObject,"bg/m_goExchange")
    c.ui.m_scrollviewSeries = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_scrollviewSeries",UEUI.ScrollRect)
    c.ui.m_transSeriesList = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_scrollviewSeries/Viewport/m_transSeriesList",UE.Transform)
    c.ui.m_goSeriesItem = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_goSeriesItem")
    c.ui.m_imgTitle = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_imgTitle",UEUI.Image)
    c.ui.m_btnExchange = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnExchange",UEUI.Button)
    c.ui.m_btnNotSelect = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnNotSelect",UEUI.Button)
    c.ui.m_goSelect = GetChild(c.uiGameObject,"bg/Bottom/m_goSelect")
    c.ui.m_btnCard = GetChild(c.uiGameObject,"bg/Bottom/m_goSelect/bg/Left/m_btnCard",UEUI.Button)
    c.ui.m_btnGoldCard = GetChild(c.uiGameObject,"bg/Bottom/m_goSelect/bg/Right/m_btnGoldCard",UEUI.Button)
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    c.ui.m_goEffectMask = GetChild(c.uiGameObject,"bg/m_goEffectMask")
    c.ui.m_scrollviewSeries = GetChild(c.uiGameObject,"bg/m_scrollviewSeries",UEUI.ScrollRect)
    c.ui.m_transSeriesList = GetChild(c.uiGameObject,"bg/m_scrollviewSeries/Viewport/m_transSeriesList",UE.Transform)
    c.ui.m_TableViewD = GetChild(c.uiGameObject,"bg/TableCtrl/m_TableViewD",CS.CCTableViewController)
    c.ui.m_TableViewD_Scroll = GetChild(c.uiGameObject,"bg/TableCtrl/m_TableViewD",UEUI.ScrollRect)
    c.ui.m_goSeriesItem2 = GetChild(c.uiGameObject,"bg/m_goSeriesItem2")
    c.ui.m_goCard = GetChild(c.uiGameObject,"Top/m_goCard")
    c.ui.m_imgCardBuy = GetChild(c.uiGameObject,"Top/m_goCard/m_imgCardBuy",UEUI.Image)
    c.ui.m_doCard = GetChild(c.uiGameObject,"Top/m_goCard/m_doCard",TweenAnim)
    c.ui.m_txtCard = GetChild(c.uiGameObject,"Top/m_goCard/m_txtCard",UEUI.Text)
    c.ui.m_goGoldCard = GetChild(c.uiGameObject,"Top/m_goGoldCard")
    c.ui.m_imgGoldCardBuy = GetChild(c.uiGameObject,"Top/m_goGoldCard/m_imgGoldCardBuy",UEUI.Image)
    c.ui.m_doGoldCard = GetChild(c.uiGameObject,"Top/m_goGoldCard/m_doGoldCard",TweenAnim)
    c.ui.m_txtGoldCard = GetChild(c.uiGameObject,"Top/m_goGoldCard/m_txtGoldCard",UEUI.Text)
    c.ui.m_btnBack = GetChild(c.uiGameObject,"m_btnBack",UEUI.Button)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_CollectCardUniversalModel