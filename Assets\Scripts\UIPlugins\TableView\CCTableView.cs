using UnityEngine;
using System.Collections.Generic;
using UnityEngine.UI;
using UnityEngine.Events;
using UnityEngine.SocialPlatforms;
//using System;
using DG.Tweening;
/// <summary>
/// A reusable table for for (vertical) tables. API inspired by Cocoa's UITableView
/// Hierarchy structure should be :
/// GameObject + TableView (this) + Mask + Scroll Rect (point to child)
/// - Child GameObject + Vertical Layout Group
/// This class should be after Unity's internal UI components in the Script Execution Order
/// </summary>
// [RequireComponent(typeof(ScrollRect))]
public class CCTableView : MonoBehaviour
{

    #region Public API
    /// <summary>
    /// The data source that will feed this table view with information. Required.
    /// </summary>
    public ITableViewDataSource dataSource
    {
        get { return m_dataSource; }
        set { m_dataSource = value; m_requiresReload = true; }
    }

    [System.Serializable]
    public class CellVisibilityChangeEvent : UnityEvent<int, bool> { }
    /// <summary>
    /// This event will be called when a cell's visibility changes
    /// First param (int) is the row index, second param (bool) is whether or not it is visible
    /// </summary>
    public CellVisibilityChangeEvent onCellVisibilityChanged;

    /// <summary>
    /// Get a cell that is no longer in use for reusing
    /// </summary>
    /// <param name="reuseIdentifier">The identifier for the cell type</param>
    /// <returns>A prepared cell if available, null if none</returns>
    public CCTableViewCell GetReusableCell()
    {
        LinkedList<CCTableViewCell> cells;
        if (!m_reusableCells.TryGetValue(m_CellName, out cells))
        {
            return null;
        }
        if (cells.Count == 0)
        {
            return null;
        }

        CCTableViewCell cell = cells.First.Value;
        cells.RemoveFirst();
        return cell;
    }

    public bool isEmpty { get; private set; }
    public bool isVertical;
    /// <summary>
    /// Reload the table view. Manually call this if the data source changed in a way that alters the basic layout
    /// (number of rows changed, etc)
    /// </summary>
    public void ReloadData(bool isClear = false)
    {
        if (isClear)
        {
            ClearTableViewData();
        }

        if (m_dataSource == null) return;

        m_rowHeights = new float[m_dataSource.GetNumberOfRowsForTableView(this)];
        this.isEmpty = m_rowHeights.Length == 0;
        if (this.isEmpty)
        {
            ClearAllRows();
            return;
        }
        m_cumulativeRowHeights = new float[m_rowHeights.Length];
        m_cleanCumulativeIndex = -1;

        for (int i = 0; i < m_rowHeights.Length; i++)
        {
            m_rowHeights[i] = m_dataSource.GetSizeForRowInTableView(this, i).y;
            if (i > 0)
            {
                m_rowHeights[i] += isVertical ? m_verticalLayoutGroup.spacing : m_horizontalLayoutGroup.spacing;

            }
        }
        if (isVertical)
        {
            m_scrollRect.content.sizeDelta = new Vector2(m_scrollRect.content.sizeDelta[0],
                        GetCumulativeRowHeight(m_rowHeights.Length - 1));
        }
        else
        {
            m_scrollRect.content.sizeDelta = new Vector2(GetCumulativeRowHeight(m_rowHeights.Length - 1),
                m_scrollRect.content.sizeDelta[0]);
        }


        RecalculateVisibleRowsFromScratch();

        m_requiresReload = false;

        m_dataSource.ReloadFinished(this);

        LayoutRebuilder.ForceRebuildLayoutImmediate(m_scrollRect.content);
    }

    /// <summary>
    /// Get cell at a specific row (if active). Returns null if not.
    /// </summary>
    public CCTableViewCell GetCellAtRow(int row)
    {
        CCTableViewCell retVal = null;
        m_visibleCells.TryGetValue(row, out retVal);
        return retVal;
    }

    /// <summary>
    /// Get the range of the currently visible rows
    /// </summary>
    public Range visibleRowRange
    {
        get { return m_visibleRowRange; }
    }

    /// <summary>
    /// Notify the table view that one of its rows changed size
    /// </summary>
    public void NotifyCellDimensionsChanged(int row)
    {
        float oldHeight = m_rowHeights[row];
        m_rowHeights[row] = isVertical ? m_dataSource.GetSizeForRowInTableView(this, row).y : m_dataSource.GetSizeForRowInTableView(this, row).x;
        m_cleanCumulativeIndex = Mathf.Min(m_cleanCumulativeIndex, row - 1);
        if (m_visibleRowRange.Contains(row))
        {
            CCTableViewCell cell = GetCellAtRow(row);
            var cellLayoutElement = cell.GetComponent<LayoutElement>();
            if (isVertical)
            {
                cellLayoutElement.preferredHeight = m_rowHeights[row];
            }
            else
            {
                cellLayoutElement.preferredWidth = m_rowHeights[row];
            }
            if (row > 0)
            {
                if (isVertical)
                {
                    cellLayoutElement.preferredHeight -= m_verticalLayoutGroup.spacing;
                }
                else
                {
                    cellLayoutElement.preferredWidth -= m_horizontalLayoutGroup.spacing;
                }
            }
        }
        float heightDelta = m_rowHeights[row] - oldHeight;
        if (isVertical)
        {
            m_scrollRect.content.sizeDelta = new Vector2(m_scrollRect.content.sizeDelta.x,
                m_scrollRect.content.sizeDelta.y + heightDelta);
        }
        else
        {
            m_scrollRect.content.sizeDelta = new Vector2(m_scrollRect.content.sizeDelta.x + heightDelta,
            m_scrollRect.content.sizeDelta.y);
        }

        m_requiresRefresh = true;
    }

    /// <summary>
    /// Get the maximum scrollable height of the table. scrollY property will never be more than this.
    /// </summary>
    public float scrollableHeight
    {
        get
        {
            return m_scrollRect.content.rect.height - (this.transform as RectTransform).rect.height;
        }
    }

    public float scrollableWidth
    {
        get
        {
            return m_scrollRect.content.rect.width - (this.transform as RectTransform).rect.width;
        }
    }

    /// <summary>
    /// Get or set the current scrolling position of the table
    /// </summary>
    public float scrollY
    {
        get
        {
            return m_scrollY;
        }
        set
        {
            if (this.isEmpty)
            {
                return;
            }
            value = Mathf.Clamp(value, 0, GetScrollYForRow(m_rowHeights.Length - 1, true));
            if (m_scrollY != value)
            {
                m_scrollY = value;
                m_requiresRefresh = true;
                float relativeScroll = value / this.scrollableHeight;
                m_scrollRect.verticalNormalizedPosition = 1 - relativeScroll;
            }
        }
    }

    public float scrollX
    {
        get
        {
            return m_scrollX;
        }
        set
        {
            if (this.isEmpty)
            {
                return;
            }
            value = Mathf.Clamp(value, 0, GetScrollYForRow(m_rowHeights.Length - 1, true));
            if (m_scrollX != value)
            {
                m_scrollX = value;
                m_requiresRefresh = true;
                float relativeScroll = value / this.scrollableWidth;
                m_scrollRect.horizontalNormalizedPosition = 1 - relativeScroll;
            }
        }
    }

    /// <summary>
    /// Get the y that the table would need to scroll to to have a certain row at the top
    /// </summary>
    /// <param name="row">The desired row</param>
    /// <param name="above">Should the top of the table be above the row or below the row?</param>
    /// <returns>The y position to scroll to, can be used with scrollY property</returns>
    public float GetScrollYForRow(int row, bool above)
    {
        float retVal = GetCumulativeRowHeight(row);
        if (above)
        {
            retVal -= m_rowHeights[row];
        }
        return retVal;
    }

    #endregion

    #region Private implementation

    private ITableViewDataSource m_dataSource;
    private bool m_requiresReload;

    private VerticalLayoutGroup m_verticalLayoutGroup;
    private HorizontalLayoutGroup m_horizontalLayoutGroup;
    private ScrollRect m_scrollRect;
    private LayoutElement m_topPadding;
    private LayoutElement m_bottomPadding;

    private float[] m_rowHeights;
    //cumulative[i] = sum(rowHeights[j] for 0 <= j <= i)
    private float[] m_cumulativeRowHeights;
    private int m_cleanCumulativeIndex;

    private Dictionary<int, CCTableViewCell> m_visibleCells;

    private Range m_visibleRowRange;

    private RectTransform m_reusableCellContainer;
    private Dictionary<string, LinkedList<CCTableViewCell>> m_reusableCells;

    private float m_scrollY;
    private float m_scrollX;

    private bool m_requiresRefresh;
    private const string m_CellName = "CCTableViewCell";
    private int m_recordIndex = -1;

    private void ScrollViewValueChanged(Vector2 newScrollValue)
    {
        if (isVertical)
        {
            float relativeScroll = 1 - newScrollValue.y;
            m_scrollY = relativeScroll * scrollableHeight;
        }
        else
        {
            float relativeScroll = newScrollValue.x;
            m_scrollX = relativeScroll * scrollableWidth;
        }
        m_requiresRefresh = true;
        //Debug.Log(m_scrollY.ToString(("0.00")));
    }

    private void RecalculateVisibleRowsFromScratch()
    {
        ClearAllRows();
        SetInitialVisibleRows();
    }

    private void ClearAllRows()
    {
        while (m_visibleCells.Count > 0)
        {
            HideRow(false);
        }
        m_visibleRowRange = new Range(0, 0);
    }

    void Awake()
    {
        isEmpty = true;
        m_scrollRect = GetComponent<ScrollRect>();
        m_verticalLayoutGroup = GetComponentInChildren<VerticalLayoutGroup>();
        m_horizontalLayoutGroup = GetComponentInChildren<HorizontalLayoutGroup>();
        m_topPadding = CreateEmptyPaddingElement("TopPadding");
        m_topPadding.transform.SetParent(m_scrollRect.content, false);
        m_bottomPadding = CreateEmptyPaddingElement("Bottom");
        m_bottomPadding.transform.SetParent(m_scrollRect.content, false);
        m_visibleCells = new Dictionary<int, CCTableViewCell>();

        m_reusableCellContainer = new GameObject("ReusableCells", typeof(RectTransform)).GetComponent<RectTransform>();
        m_reusableCellContainer.SetParent(this.transform, false);
        m_reusableCellContainer.gameObject.SetActive(false);
        m_reusableCells = new Dictionary<string, LinkedList<CCTableViewCell>>();

        onCellVisibilityChanged = new CellVisibilityChangeEvent();
        isVertical = m_verticalLayoutGroup != null;
    }

    void Update()
    {
        if (m_requiresReload)
        {
            ReloadData();
        }
    }

    void LateUpdate()
    {
        if (m_requiresRefresh)
        {
            RefreshVisibleRows();
        }
    }

    void OnEnable()
    {
        m_scrollRect.onValueChanged.AddListener(ScrollViewValueChanged);
    }

    void OnDisable()
    {
        m_recordIndex = -1;
        m_scrollRect.onValueChanged.RemoveListener(ScrollViewValueChanged);
    }

    private Range CalculateCurrentVisibleRowRange()
    {

        float startValue = isVertical ? m_scrollY : m_scrollX;
        float endValue = isVertical ? m_scrollY + (transform as RectTransform).rect.height : m_scrollX + (transform as RectTransform).rect.width;
        int startIndex = FindIndexOfRowAtValue(startValue);
        int endIndex = FindIndexOfRowAtValue(endValue);
        return new Range(startIndex, endIndex - startIndex + 1);
    }

    private void SetInitialVisibleRows()
    {
        Range visibleRows = CalculateCurrentVisibleRowRange();
        for (int i = 0; i < visibleRows.count; i++)
        {
            AddRow(visibleRows.from + i, true);
        }
        m_visibleRowRange = visibleRows;
        UpdatePaddingElements();
    }

    private void AddRow(int row, bool atEnd)
    {
        CCTableViewCell newCell = m_dataSource.GetCellForRowInTableView(this, row);
        newCell.transform.SetParent(m_scrollRect.content, false);
        newCell.GetComponent<RectTransform>().anchoredPosition3D = Vector3.zero;
        newCell.GetComponent<RectTransform>().sizeDelta = Vector3.zero;
        newCell.transform.localScale = Vector3.one;

        LayoutElement layoutElement = newCell.GetComponent<LayoutElement>();
        if (layoutElement == null)
        {
            layoutElement = newCell.gameObject.AddComponent<LayoutElement>();
        }

        if (isVertical)
        {
            layoutElement.preferredHeight = m_rowHeights[row];
        }
        else
        {
            layoutElement.preferredWidth = m_rowHeights[row];
        }

        if (row > 0)
        {
            if (isVertical)
            {
                layoutElement.preferredHeight -= m_verticalLayoutGroup.spacing;
            }
            else
            {
                layoutElement.preferredWidth -= m_horizontalLayoutGroup.spacing;
            }
        }

        m_visibleCells[row] = newCell;
        if (atEnd)
        {
            newCell.transform.SetSiblingIndex(m_scrollRect.content.childCount - 2); //One before bottom padding
        }
        else
        {
            newCell.transform.SetSiblingIndex(1); //One after the top padding
        }
        this.onCellVisibilityChanged.Invoke(row, true);
    }

    private void RefreshVisibleRows()
    {
        m_requiresRefresh = false;

        if (this.isEmpty)
        {
            return;
        }

        Range newVisibleRows = CalculateCurrentVisibleRowRange();
        int oldTo = m_visibleRowRange.Last();
        int newTo = newVisibleRows.Last();

        if (newVisibleRows.from > oldTo || newTo < m_visibleRowRange.from)
        {
            //We jumped to a completely different segment this frame, destroy all and recreate
            RecalculateVisibleRowsFromScratch();
            return;
        }

        //Remove rows that disappeared to the top
        for (int i = m_visibleRowRange.from; i < newVisibleRows.from; i++)
        {
            HideRow(false);
        }
        //Remove rows that disappeared to the bottom
        for (int i = newTo; i < oldTo; i++)
        {
            HideRow(true);
        }
        //Add rows that appeared on top
        for (int i = m_visibleRowRange.from - 1; i >= newVisibleRows.from; i--)
        {
            AddRow(i, false);
        }
        //Add rows that appeared on bottom
        for (int i = oldTo + 1; i <= newTo; i++)
        {
            AddRow(i, true);
        }
        m_visibleRowRange = newVisibleRows;
        UpdatePaddingElements();
    }

    private void UpdatePaddingElements()
    {
        float hiddenElementsHeightSum = 0;
        for (int i = 0; i < m_visibleRowRange.from; i++)
        {
            hiddenElementsHeightSum += m_rowHeights[i];
        }

        if (isVertical)
        {
            m_topPadding.preferredHeight = hiddenElementsHeightSum;
            m_topPadding.gameObject.SetActive(m_topPadding.preferredHeight > 0);
        }
        else
        {
            m_topPadding.preferredWidth = hiddenElementsHeightSum;
            m_topPadding.gameObject.SetActive(m_topPadding.preferredWidth > 0);
        }
        for (int i = m_visibleRowRange.from; i <= m_visibleRowRange.Last(); i++)
        {
            hiddenElementsHeightSum += m_rowHeights[i];
        }

        if (isVertical)
        {
            float bottomPaddingHeight = m_scrollRect.content.rect.height - hiddenElementsHeightSum;
            m_bottomPadding.preferredHeight = bottomPaddingHeight - m_verticalLayoutGroup.spacing;
            m_bottomPadding.gameObject.SetActive(m_bottomPadding.preferredHeight > 0);
        }
        else
        {
            float bottomPaddingWidth = m_scrollRect.content.rect.width - hiddenElementsHeightSum;
            m_bottomPadding.preferredWidth = bottomPaddingWidth - m_horizontalLayoutGroup.spacing;
            m_bottomPadding.gameObject.SetActive(m_bottomPadding.preferredWidth > 0);
        }
    }

    private void HideRow(bool last)
    {
        //Debug.Log("Hiding row at scroll y " + m_scrollY.ToString("0.00"));

        int row = last ? m_visibleRowRange.Last() : m_visibleRowRange.from;
        CCTableViewCell removedCell = m_visibleCells[row];
        StoreCellForReuse(removedCell);
        m_visibleCells.Remove(row);
        m_dataSource.HideCellForVisibleCells(removedCell, row);
        m_visibleRowRange.count -= 1;
        if (!last)
        {
            m_visibleRowRange.from += 1;
        }
        this.onCellVisibilityChanged.Invoke(row, false);
    }

    private LayoutElement CreateEmptyPaddingElement(string name)
    {
        GameObject go = new GameObject(name, typeof(RectTransform), typeof(LayoutElement));
        LayoutElement le = go.GetComponent<LayoutElement>();
        return le;
    }

    private int FindIndexOfRowAtValue(float y)
    {
        //TODO : Binary search if inside clean cumulative row height area, else walk until found.
        return FindIndexOfRowAtValue(y, 0, m_cumulativeRowHeights.Length - 1);
    }

    private int FindIndexOfRowAtValue(float v, int startIndex, int endIndex)
    {
        if (startIndex >= endIndex)
        {
            return startIndex;
        }
        int midIndex = (startIndex + endIndex) / 2;
        if (GetCumulativeRowHeight(midIndex) >= v)
        {
            return FindIndexOfRowAtValue(v, startIndex, midIndex);
        }
        else
        {
            return FindIndexOfRowAtValue(v, midIndex + 1, endIndex);
        }
    }

    private float GetCumulativeRowHeight(int row)
    {
        while (m_cleanCumulativeIndex < row)
        {
            m_cleanCumulativeIndex++;
            //Debug.Log("Cumulative index : " + m_cleanCumulativeIndex.ToString());
            m_cumulativeRowHeights[m_cleanCumulativeIndex] = m_rowHeights[m_cleanCumulativeIndex];
            if (m_cleanCumulativeIndex > 0)
            {
                m_cumulativeRowHeights[m_cleanCumulativeIndex] += m_cumulativeRowHeights[m_cleanCumulativeIndex - 1];
            }
        }
        return m_cumulativeRowHeights[row];
    }

    private void StoreCellForReuse(CCTableViewCell cell)
    {
        string reuseIdentifier = cell.reuseIdentifier;

        if (string.IsNullOrEmpty(reuseIdentifier))
        {
            transform.DetachChildren();
            return;
        }

        if (!m_reusableCells.ContainsKey(reuseIdentifier))
        {
            m_reusableCells.Add(reuseIdentifier, new LinkedList<CCTableViewCell>());
        }
        m_reusableCells[reuseIdentifier].AddLast(cell);
        cell.transform.SetParent(m_reusableCellContainer, false);
    }

    private void ClearTableViewData()
    {
        //var parent = transform.GetComponentInParent<UILuaBehaviour>().transform;
        for (int i = m_scrollRect.content.childCount - 1; i >= 0; i--)
        {
            GameObject go = m_scrollRect.content.GetChild(i).gameObject;
            GameObject.Destroy(go);
            // parent.doDestroy(go);
        }
        for (int i = m_reusableCellContainer.childCount - 1; i >= 0; i--)
        {
            GameObject go = m_reusableCellContainer.GetChild(i).gameObject;
            GameObject.Destroy(go);
            // parent.doDestroy(go);
        }

        m_topPadding = CreateEmptyPaddingElement("TopPadding");
        m_topPadding.transform.SetParent(m_scrollRect.content, false);
        m_bottomPadding = CreateEmptyPaddingElement("Bottom");
        m_bottomPadding.transform.SetParent(m_scrollRect.content, false);
        m_scrollY = 0;
        m_scrollRect.StopMovement();
        m_scrollRect.content.sizeDelta = Vector2.zero;
        m_visibleCells.Clear();
        m_reusableCells.Clear();
        m_rowHeights = null;
        m_cumulativeRowHeights = null;
        m_scrollRect.content.anchoredPosition = Vector2.zero;
    }

    private void UpdateRecordIndex(int index)
    {
        if (m_recordIndex != index)
            m_recordIndex = index;
    }

    public void ShowCenter(int index, System.Action call)
    {
        if (m_rowHeights.Length <= 0)
        {
            LogMan.Debug("Cell of number is " + m_rowHeights.Length);
            return;
        }

        if (index >= m_rowHeights.Length)
        {
            index = m_rowHeights.Length - 1;
        }
        else if (index <= 0)
        {
            index = 0;
        }
        Range visibleRows = CalculateCurrentVisibleRowRange();
        int visibleCount = visibleRows.count;
        float recordValue = m_recordIndex > 0 ? m_rowHeights[m_recordIndex] : 0;
        float recordIndex = m_recordIndex > 0 ? m_recordIndex : 0;
        if (isVertical)
        {
            float beforX = m_scrollRect.content.anchoredPosition.x;
            float lastY = m_recordIndex > 0 ? (m_rowHeights[m_recordIndex] * (m_recordIndex - visibleCount / 2)) : 0;
            float nowY = m_rowHeights[index] * (index - visibleCount / 2) - lastY;

            if (call != null)
            {
                Tween tween = m_scrollRect.content.transform.DOLocalMoveY(nowY, 0.5f).SetRelative(true);
                tween.OnComplete(() =>
                {
                    call();
                    UpdateRecordIndex(index);
                });
            }
            else
            {
                nowY = Mathf.Min(scrollableHeight, Mathf.Max(nowY, 0));
                m_scrollRect.content.anchoredPosition = new Vector2(beforX, nowY);
                m_scrollY = nowY;
                UpdateRecordIndex(index);
            }
        }
        else
        {
            float beforY = m_scrollRect.content.anchoredPosition.y;
            float lastX = m_recordIndex > 0 ? (m_rowHeights[m_recordIndex] * (m_recordIndex - visibleCount / 2)) : 0;
            float nowX = m_rowHeights[index] * (index - visibleCount / 2) - lastX;

            if (call != null)
            {
                Tween tween = m_scrollRect.content.transform.DOLocalMoveX(nowX, 0.5f).SetRelative(true);
                tween.OnComplete(() =>
                {
                    call();
                    UpdateRecordIndex(index);
                });
            }
            else
            {
                nowX = Mathf.Min(scrollableWidth, Mathf.Max(nowX, 0));
                m_scrollRect.content.anchoredPosition = new Vector2(beforY, nowX);
                m_scrollX = nowX;
                UpdateRecordIndex(index);
            }
        }


        RefreshVisibleRows();
    }

    public void ShowBottom(System.Action call)
    {
        m_scrollRect.StopMovement();
        if (isVertical)
        {
            float maxHeight = m_scrollRect.content.sizeDelta.y;
            if (maxHeight > (this.transform as RectTransform).rect.height)
            {
                float beforX = m_scrollRect.content.anchoredPosition.x;
                float nowPosY = m_scrollRect.content.anchoredPosition.y;
                if (call != null)
                {
                    float toMoveY = scrollableHeight - nowPosY;
                    Tween tween = m_scrollRect.content.transform.DOLocalMoveY(toMoveY, 0.5f).SetRelative(true);
                    tween.OnComplete(() =>
                    {
                        call();
                    });
                }
                else
                {
                    m_scrollRect.content.anchoredPosition = new Vector2(beforX, scrollableHeight);
                    m_scrollY = Mathf.Max(scrollableHeight, 0);

                    RefreshVisibleRows();
                }
            }
        }
        else
        {
            float nowPosX = m_scrollRect.content.anchoredPosition.x;
            float maxWigth = m_scrollRect.content.sizeDelta.x;
            if (maxWigth > (this.transform as RectTransform).rect.width)
            {
                if (call != null)
                {
                    float toMoveX = scrollableWidth - nowPosX;
                    Tween tween = m_scrollRect.content.transform.DOLocalMoveX(toMoveX, 0.5f).SetRelative(true);
                    tween.OnComplete(() =>
                    {
                        call();
                    });
                }
                else
                {
                    float beforY = m_scrollRect.content.anchoredPosition.y;
                    m_scrollRect.content.anchoredPosition = new Vector2(scrollableWidth, beforY);
                    m_scrollX = Mathf.Max(scrollableWidth, 0);
                    RefreshVisibleRows();
                }
            }
        }

    }

    #endregion
}

internal static class RangeExtensions
{
    public static int Last(this Range range)
    {
        if (range.count == 0)
        {
            throw new System.InvalidOperationException("Empty range has no to()");
        }
        return (range.from + range.count - 1);
    }

    public static bool Contains(this Range range, int num)
    {
        return num >= range.from && num < (range.from + range.count);
    }
}