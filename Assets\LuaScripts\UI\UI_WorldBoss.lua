local UI_WorldBoss = Class(BaseView)

local BagItem = require("UI.BagItem")

function UI_WorldBoss:OnInit()
    EventMgr:Add(EventID.UPDATE_WORLD_BOSS_INFO, self.OnUpdateInfo, self);
    EventMgr:Add(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
    
    self.toyPrefabPath = "";
    self.deltaTime = 0;
    self.rewardItemList = {};
    
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_world_boss_setting, 6);
    self.challengeNum = config and v2n(config.value) or 0;
end

function UI_WorldBoss:OnCreate(param)
    self:SortOrderAllCom(true);
    
    local canvas = GetComponent(self.ui.m_goWindow, UE.Canvas);
    canvas.sortingOrder = self:GetViewSortingOrder() + 5;

    local particleRenderer1 = GetChild(self.ui.m_goTop1, "nameTxt/battle_jingjichang_anniu3/Particle System/Particle System (1)", UE.Renderer);
    local particleRenderer2 = GetChild(self.ui.m_goTop2, "nameTxt/battle_jingjichang_anniu3/Particle System/Particle System (1)", UE.Renderer);
    local particleRenderer3 = GetChild(self.ui.m_goTop3, "nameTxt/battle_jingjichang_anniu3/Particle System/Particle System (1)", UE.Renderer);
    SetRendererOrder(particleRenderer1, SortingLayerInGame.Default, self:GetViewSortingOrder() + 6);
    SetRendererOrder(particleRenderer2, SortingLayerInGame.Default, self:GetViewSortingOrder() + 6);
    SetRendererOrder(particleRenderer3, SortingLayerInGame.Default, self:GetViewSortingOrder() + 6);
    
    self.btnPress = self.ui.m_goBoss.transform:GetComponent(typeof(CS.ButtonPressed));
    self.btnPress.buttonPressed = function()
        local monsterInfo = WorldBossManager:GetMonsterInfo();
        if monsterInfo then
            local monsterVo = HeroModule.new(monsterInfo.id);
            monsterVo:initData();
            monsterVo:SetHeroValueByKey("isMonster", true);
            monsterVo:SetHeroValueByKey("level", monsterInfo.level);
            monsterVo:SetHeroValueByKey("starLv", monsterInfo.star);
            monsterVo:SetHeroValueByKey("atk", monsterInfo.atk);
            monsterVo:SetHeroValueByKey("hp", monsterInfo.hp);
            monsterVo:SetHeroValueByKey("def", monsterInfo.def);
            monsterVo:SetHeroValueByKey("power", monsterInfo.fight);
            UI_SHOW(UIDefine.UI_SlgHeroDetailView, monsterVo);
        end
    end

    self:SetIsUpdateTick(true);
    self:OnUpdateInfo();
    self:OnTryGuide();
    
    -- 刷新排行榜数据
    WorldBossManager:OnRequestBossLoad();
end

function UI_WorldBoss:OnRefresh(param)

end

function UI_WorldBoss:onDestroy()
    EventMgr:Remove(EventID.UPDATE_WORLD_BOSS_INFO, self.OnUpdateInfo, self);
    EventMgr:Remove(EventID.RED_POINT_DIRTY, self.OnRedPointDirty, self)
end

function UI_WorldBoss:onUIEventClick(go, param)
    local name = go.name
    if name == "tipBtn" then
        local nameStr1 = LangMgr:GetLang(70001028);
        local nameStr2 = LangMgr:GetLang(70001029);
        local nameStr3 = LangMgr:GetLang(70001030);
        local numStr = self.challengeNum == -1 and LangMgr:GetLang(70001031) or self.challengeNum;
        local tipStr = LangMgr:GetLangFormat(70000749, nameStr1, nameStr2, nameStr3, numStr);
        UI_SHOW(UIDefine.UI_UnionBossRule, tipStr);
    elseif name == "rewardBtn" then
        UI_SHOW(UIDefine.UI_WorldBossReward);
    elseif name == "m_btnLog" then
        UI_SHOW(UIDefine.UI_WorldBossLog);
    elseif name == "m_btnDetail" then
        UI_SHOW(UIDefine.UI_WorldBossDetail);
    elseif name == "m_btnAchievement" then
        UI_SHOW(UIDefine.UI_WorldBossAchievement);
    elseif name == "m_btnRank" or name == "topBg" then
        UI_SHOW(UIDefine.UI_WorldBossRank);
    elseif name == "m_btnFight" then
        if self.remainTime > 0 then
            local count = WorldBossManager:GetRemainCount();
            if count == -1 or count > 0 then
                -- 打开布阵
                local monsterInfo = WorldBossManager:GetMonsterInfo()
                BattleSceneManager:GotoChooseWorldBossTeam(monsterInfo)
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000743));
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000707));
        end
    elseif string.startswith(name, "m_goSkill") then
        local index = v2n(string.gsub(name, "m_goSkill", ""));
        if index then
            local monsterVo = WorldBossManager:GetMonsterVo();
            if monsterVo then
                local skillConfig = monsterVo:GetHeroSkillConfig(index);
                UI_SHOW(UIDefine.UI_HeroSkillTips, skillConfig);
            end
        end
    end
end

function UI_WorldBoss:TickUI(deltaTime)
    self.deltaTime = self.deltaTime + deltaTime;
    if self.deltaTime >= 1 then
        self.deltaTime = self.deltaTime - 1;
        self:OnUpdateTime();
    end
end

function UI_WorldBoss:OnUpdateInfo()
    for i = 1, 3 do
        local item = self.ui["m_goTop" .. i];
        local hurtTxt = GetChild(item, "hurtTxt", UEUI.Text);
        local nameTxt = GetChild(item, "nameTxt", UEUI.Text);
        local noneTxt = GetChild(item, "m_txtAuto70000768", UEUI.Text);
        local customHeadObj = GetChild(item, "CustomHead");
        if not customHeadObj then
            customHeadObj = CreateCommonHead(item, 0.34, Vector2(-102, -1));
        end
        
        local data = WorldBossManager:GetTopInfo(i);
        local isShow = data ~= nil;
        local playerInfo;
        if isShow then
            hurtTxt.text = NumToGameString(data.value);
            
            playerInfo = data.player;
            nameTxt.text = playerInfo.name;
            SetHeadAndBorderByGo(customHeadObj, playerInfo.icon, playerInfo.border);
        end
        SetActive(hurtTxt, isShow);
        SetActive(nameTxt, isShow);
        SetActive(customHeadObj, isShow);
        SetActive(noneTxt, not isShow);

        RemoveUIComponentEventCallback(item, UEUI.Button)
        AddUIComponentEventCallback(item, UEUI.Button, function(arg1, arg2)
            if isShow then
                if v2n(playerInfo.id) ~= v2n(NetUpdatePlayerData:GetPlayerInfo().id) then
                    FriendManager:ShowPlayerById(playerInfo.id);
                end
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000742));
            end
        end);
    end
    
    local bossConfig = WorldBossManager:GetBossConfig();
    local list = ConfigMgr:GetDataByKey(ConfigDefine.ID.slg_world_boss_join_reward, "boss_type", bossConfig.boss_type, true);
    table.sort(list, function(a, b) 
        return a.id < b.id;
    end);
    
    local index = 0;
    local fightNum = WorldBossManager.fighted_times;
    for i = 1, #list do
        if list[i].times > fightNum then
            index = i;
            break;
        end
    end
    if index == 0 then
        index = #list;
    end
    local data = list[index];
    fightNum = fightNum < data.times and fightNum or data.times;
    self.ui.m_txtReward.text = LangMgr:GetLangFormat(70000706, fightNum, data.times);

    local arr = Split2(data.reward, ";", "|");
    local count = #arr;
    local num = #self.rewardItemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    for i = 1, len do
        local bagItem = self.rewardItemList[i];
        if not bagItem then
            bagItem = BagItem.new();
            bagItem:Create(rootTrans);
            bagItem:SetScale(0.71, 0.71);
            SetUISize(bagItem.go, 112, 112);
            table.insert(self.rewardItemList, bagItem);
        end
        SetActive(bagItem.go, i <= count);

        if i <= count then
            bagItem:UpdateInfo(BagItemModule.new({ id = v2n(arr[i].id), num = v2n(arr[i].count) }));
        end
    end

    if count > 0 then
        local width = count * 112 + (count - 1) * 10;
        if width < 420 then
            SetUISize(self.ui.m_scrollview, width, 112);
        else
            SetUISize(self.ui.m_scrollview, 420, 112);
        end
    end
    
    local monsterVo = WorldBossManager:GetMonsterVo();
    local monsterInfo = WorldBossManager:GetMonsterInfo();
    self.ui.m_txtLevel.text = "Lv." .. monsterInfo.level;
    self.ui.m_txtName.text = monsterVo:GetHeroName();
    SetUIImage(self.ui.m_imgHead, monsterVo:GetHeroHead(), false, function()
        SetActive(self.ui.m_imgHead, true);
    end);
    SetUIImage(self.ui.m_imgCareer, HeroManager:GetHeroCareerIcon(monsterVo:GetHeroCareer()), true);
    SetUIImage(self.ui.m_imgKind, HeroManager:GetHeroKindIcon(monsterVo:GetHeroKind()), true);

    local starNum = HeroManager:GetStarOrderByLv(monsterInfo.star);
    for i = 1, 5 do
        local starSp = GetChild(self.ui.m_goStar, "star" .. i);
        if starSp then
            SetActive(starSp, i <= starNum);
        end
    end
    
    local skillIdx = 3;
    local skillConfig = monsterVo:GetHeroSkillConfig(skillIdx);
    local skillLv = monsterVo:GetHeroSkillLv(skillIdx);
    local skillDes = HeroManager:GetHeroSkillDesc(skillConfig, skillLv, "980000");
    self.ui.m_txtDefect.text = LangMgr:GetLang(70000700) .. skillDes;

    local prefabPath = monsterVo:GetHeroToyPrefab();
    if self.toyPrefabPath ~= prefabPath then
        self.toyPrefabPath = prefabPath;
        if self.toyObj then
            UEGO.Destroy(self.toyObj);
        end
        if prefabPath then
            ResMgr:LoadAssetAsync(prefabPath, AssetDefine.LoadType.Instant, function(prefab)
                self.toyObj = CreateGameObjectWithParent(prefab, self.ui.m_goBoss);

                local transform = self.toyObj.transform;
                local dic = monsterVo:GetHeroToyOffset();
                transform.localPosition = Vector3(v2n(dic[1]), v2n(dic[2]), v2n(dic[3]));
                dic = monsterVo:GetHeroToyScale();
                transform.localScale = Vector3(v2n(dic[1]), v2n(dic[2]), v2n(dic[3]));

                GameUtil.SetLayer(self.toyObj, BloomLayer, 1);
                local render = self.toyObj.transform:GetComponentInChildren(typeof(UE.Renderer))
                SetRendererOrder(render, SortingLayerInGame.Default, self.uiSortingOrder + 2);
            end);
        end
    end

    for i = 1, 4 do
        local skillObj = self.ui["m_goSkill" .. i];
        local skillConfig = monsterVo:GetHeroSkillConfig(i);
        local isShow = skillObj and skillConfig;
        if isShow then
            local skillSp = GetChild(skillObj, "skillSp", UEUI.Image);
            SetUIImage(skillSp, skillConfig.icon, false, function()
                SetActive(skillSp, true);
            end);
        end
        SetActive(skillObj, isShow);
    end
    
    self:OnUpdateTime();
    self:OnUpdateBtn();
end

function UI_WorldBoss:OnUpdateTime()
    self.remainTime = WorldBossManager:GetRemainTime();
    if self.remainTime > 0 then
        self.ui.m_txtTime.text = LangMgr:GetLangFormat(70000306, TimeMgr:CutBuyWorkTime(self.remainTime));
    else
        self.ui.m_txtTime.text = LangMgr:GetLang(70000707);
    end
end

function UI_WorldBoss:OnUpdateBtn()
    if self.remainTime > 0 then
        self.ui.m_txtFight.text = LangMgr:GetLang(70000310);
        UnifyOutline(self.ui.m_txtFight, "792500");
        SetUIImage(self.ui.m_btnFight, "Sprite/ui_lianmeng/lianmengboss_main_button1.png", false);
        
        local count = WorldBossManager:GetRemainCount();
        if count == -1 then
            self.ui.m_txtTip.text = "";
        else
            self.ui.m_txtTip.text = LangMgr:GetLangFormat(70000701, GetStrRichColor(count, "30ff00"), self.challengeNum);
        end
    else
        self.ui.m_txtFight.text = LangMgr:GetLang(70000697);
        UnifyOutline(self.ui.m_txtFight, "202020");
        SetUIImage(self.ui.m_btnFight, "Sprite/ui_lianmeng/lianmengboss_main_button1_1.png", false);
        
        self.ui.m_txtTip.text = LangMgr:GetLang(70000697);
    end
    SetActive(self.ui.m_goFightRed, WorldBossManager:GetRedPoint());
    SetActive(self.ui.m_goAchievementRed, RedPointMgr:IsRed(ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_WorldbossTaskReward));
end

function UI_WorldBoss:OnRedPointDirty(dirtyIdSet)
    if dirtyIdSet[RedID.WorldBoss] then
        SetActive(self.ui.m_goAchievementRed, RedPointMgr:IsRed(ServerDefine.marge_topia_redpoint_RedPointType.RedPointType_WorldbossTaskReward));
    end
end

function UI_WorldBoss:OnTryGuide()
    if NetGlobalData:GetActivityGuideCache("WorldBoss") then
        return;
    end
    NetGlobalData:SetActivityGuideCache("WorldBoss");
    
    local function OnGuide3()
        local centerPos = UIRectPosFit(self.ui.m_btnFight);
        UI_SHOW(UIDefine.UI_GuideMask, {
            { 2, 0, 90 }, -- 遮罩类型和大小
            centerPos, -- 遮罩位置
            { 2, 2 }, -- 遮罩按钮大小
            0.5, -- 缩放动画的时长
            function()
                UI_CLOSE(UIDefine.UI_GuideMask);
                self:onUIEventClick({name = "m_btnFight"});
            end, -- 点击回调
            { centerPos[1] / 100, centerPos[2] / 100 + 2, 0, 0, 0 }, -- 箭头位置
            { 1, 0, 70001064 }, -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png", -- 对话框头像
            nil,
        })
    end
    
    local function OnGuide2()
        local centerPos = UIRectPosFit(self.ui.m_btnAchievement);
        UI_SHOW(UIDefine.UI_GuideMask, {
            { 2, 0, 90 }, -- 遮罩类型和大小
            centerPos, -- 遮罩位置
            { 2, 2 }, -- 遮罩按钮大小
            0.5, -- 缩放动画的时长
            function()
                UI_CLOSE(UIDefine.UI_GuideMask);
                UI_SHOW(UIDefine.UI_WorldBossAchievement, OnGuide3);
            end, -- 点击回调
            { centerPos[1] / 100, centerPos[2] / 100 + 2, 0, 0, 0 }, -- 箭头位置
            { 1, 0, 70001063 }, -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png", -- 对话框头像
            nil,
        })
    end
    
    local rewardBtn = GetChild(self.ui.m_goWindow, "rewardBg/rewardBtn");
    local centerPos = UIRectPosFit(rewardBtn);
    UI_SHOW(UIDefine.UI_GuideMask, {
        { 2, 0, 90 }, -- 遮罩类型和大小
        centerPos, -- 遮罩位置
        { 2, 2 }, -- 遮罩按钮大小
        0.5, -- 缩放动画的时长
        function()
            UI_CLOSE(UIDefine.UI_GuideMask);
            UI_SHOW(UIDefine.UI_WorldBossReward, OnGuide2);
        end, -- 点击回调
        { centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180 }, -- 箭头位置
        { 1, 0, 70001062 }, -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png", -- 对话框头像
        nil,
    })
end

return UI_WorldBoss