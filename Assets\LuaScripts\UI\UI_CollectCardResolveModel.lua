local UI_CollectCardResolveModel = {}

UI_CollectCardResolveModel.config = {["name"] = "UI_CollectCardResolve", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_CollectCardResolveModel:Init(c)
    c.ui = {}    
    c.ui.m_txtPoint = GetChild(c.uiGameObject,"bg/Image/m_txtPoint",UEUI.Text)
    c.ui.m_goPointPos = GetChild(c.uiGameObject,"bg/Image/m_txtPoint/icon/m_goPointPos")
    c.ui.m_togAutoSelect = GetChild(c.uiGameObject,"bg/m_togAutoSelect",UEUI.Toggle)
    c.ui.m_goExchange = GetChild(c.uiGameObject,"bg/m_goExchange")
    c.ui.m_scrollviewSeries = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_scrollviewSeries",UEUI.ScrollRect)
    c.ui.m_transSeriesList = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_scrollviewSeries/Viewport/m_transSeriesList",UE.Transform)
    c.ui.m_goSeriesItem = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_goSeriesItem")
    c.ui.m_imgTitle = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_imgTitle",UEUI.Image)
    c.ui.m_btnResolve = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnResolve",UEUI.Button)
    c.ui.m_txtExchangePoint = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnResolve/m_txtExchangePoint",UEUI.Text)
    c.ui.m_btnNotSelect = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnNotSelect",UEUI.Button)
    c.ui.m_btnSkip = GetChild(c.uiGameObject,"bg/m_goExchange/bg/m_btnSkip",UEUI.Button)
    c.ui.m_TableViewD = GetChild(c.uiGameObject,"bg/TableCtrl/m_TableViewD",CS.CCTableViewController)
    c.ui.m_TableViewD_Scroll = GetChild(c.uiGameObject,"bg/TableCtrl/m_TableViewD",UEUI.ScrollRect)
    c.ui.m_goEffectMask = GetChild(c.uiGameObject,"bg/TableCtrl/m_goEffectMask")
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    c.ui.m_goNoCard = GetChild(c.uiGameObject,"bg/m_goNoCard")
    c.ui.m_goOver = GetChild(c.uiGameObject,"bg/m_goOver")
    c.ui.m_btnBack = GetChild(c.uiGameObject,"m_btnBack",UEUI.Button)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_CollectCardResolveModel