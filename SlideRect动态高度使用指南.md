# SlideRect 动态高度使用指南

## 问题分析

SlideRect 的动态高度功能需要正确配置才能正常工作。常见问题包括：

1. **Content 高度计算错误**：没有正确实现 `onGetSize` 回调函数
2. **Item 位置异常**：没有正确实现 `Resize()` 方法
3. **高度属性未更新**：在 Resize 后没有更新 Item 的 height 属性

## 正确使用步骤

### 1. 初始化 SlideRect 时启用动态高度

```lua
self.slideRect = SlideRect.new()
self.slideRect:Init(
    scrollRect,        -- ScrollRect 组件
    2,                 -- 滑动类型：2=垂直
    1,                 -- syncCount
    true,              -- 启用动态高度 (dynamicSetSize)
    function(data)     -- onGetSize 回调函数
        -- 根据数据返回对应的高度
        return self:CalculateItemHeight(data)
    end
)
```

### 2. 实现 onGetSize 回调函数

```lua
function MyUI:CalculateItemHeight(data)
    if not data then return 100 end  -- 默认高度
    
    -- 根据数据内容计算高度
    if data.type == "short" then
        return 100
    elseif data.type == "medium" then
        return 150
    elseif data.type == "long" then
        return 200
    else
        return 120
    end
end
```

### 3. 在 Item 类中重写 Resize 方法

```lua
function MyItem:Resize()
    if not self.data then return end
    
    -- 计算新高度
    local newHeight = self:CalculateHeight()
    
    -- 更新 RectTransform 的高度
    self.rectTrans.sizeDelta = Vector2.New(self.rectTrans.sizeDelta.x, newHeight)
    
    -- 重要：更新 height 属性，SlideRect 依赖此值
    self.height = newHeight
    
    -- 可选：强制重新布局
    SetUIForceRebuildLayout(self.rectTrans)
end

function MyItem:CalculateHeight()
    -- 根据数据计算高度，必须与 onGetSize 返回的值一致
    if self.data.type == "short" then
        return 100
    elseif self.data.type == "medium" then
        return 150
    elseif self.data.type == "long" then
        return 200
    else
        return 120
    end
end
```

### 4. 确保 UpdateData 和 Resize 的调用顺序

```lua
function MyItem:UpdateData(data, index)
    self.data = data
    self.index = index
    
    -- 更新UI显示内容
    self:UpdateUI()
end

-- SlideRect 会在 SetItemState 后自动调用 SetItemSize (即 Resize)
```

## 关键要点

### 1. 高度一致性
- `onGetSize` 回调函数返回的高度
- `Resize()` 方法中设置的高度
- 这两个值必须完全一致

### 2. height 属性更新
```lua
-- 在 Resize() 方法中必须更新
self.height = newHeight
```

### 3. RectTransform 更新
```lua
-- 更新实际的UI尺寸
self.rectTrans.sizeDelta = Vector2.New(width, newHeight)
```

## 调试技巧

### 1. 添加日志输出
```lua
function MyItem:Resize()
    local newHeight = self:CalculateHeight()
    Log.Info("Item " .. (self.index or 0) .. " resize to height: " .. newHeight)
    
    self.rectTrans.sizeDelta = Vector2.New(self.rectTrans.sizeDelta.x, newHeight)
    self.height = newHeight
end
```

### 2. 检查 Content 高度
```lua
-- 在 SetData 后检查 Content 的总高度
Log.Info("Content height: " .. self.slideRect:GetContentHeight())
```

### 3. 验证位置计算
```lua
-- 检查每个 Item 的位置
function MyItem:UpdatePosition(vec)
    Log.Info("Item " .. (self.index or 0) .. " position: " .. vec.y)
    self.rectTrans.anchoredPosition = vec
end
```

## 常见错误

### 1. 忘记启用动态高度
```lua
-- 错误：没有传递 dynamicSetSize 参数
self.slideRect:Init(scrollRect, 2, 1)

-- 正确：启用动态高度
self.slideRect:Init(scrollRect, 2, 1, true, getSizeFunc)
```

### 2. onGetSize 返回值不正确
```lua
-- 错误：返回固定值
function(data) return 100 end

-- 正确：根据数据返回不同高度
function(data) 
    return data and self:CalculateItemHeight(data) or 100
end
```

### 3. 没有更新 height 属性
```lua
-- 错误：只更新了 sizeDelta
function MyItem:Resize()
    self.rectTrans.sizeDelta = Vector2.New(width, newHeight)
    -- 缺少这行：self.height = newHeight
end
```

## 完整示例

参考 `DynamicHeightExample.lua` 文件中的完整实现示例。

## 性能优化建议

1. **缓存高度计算结果**：避免重复计算相同数据的高度
2. **合理设置 Item 数量**：根据屏幕大小设置合适的复用 Item 数量
3. **避免频繁 Resize**：只在数据真正改变时才调用 Resize
