local UI_League_Language = Class(BaseView)

function UI_League_Language:OnInit()
    self.finishInit = false 
    self.togList = {}
    self.leagueLangID = 40

    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
end

function UI_League_Language:OnCreate(unityid)
    
    local datalist = ConfigMgr:GetData(ConfigDefine.ID.language_config)
    self.ToggleList = {}
    --self.ToggleRect = GET_UI(self.uiGameObject, "Toggle", "RectTransform")
    self.togGroup = GET_UI(self.uiGameObject, "togGroup", "RectTransform")
    
    self.group = GetComponent(self.togGroup,UEUI.ToggleGroup)
    self.group.enabled = false;
    for k, v in pairs(datalist) do
        if v.on_off == "1" then
            if self.id == v.unityid then
                self.index = k
            end
            self:AddTarget(v)
        end
    end

    self.group.enabled = false;
    if self.index == nil then
        self.index = 1
    end
    
    local count = 0;
    for k,v in pairs(self.togList)do
        v.group = self.group
        if k == unityid then
            v.isOn = true;
        end
        count = count + 1;
    end
    self.group.enabled = true;
    
    local contentSizeFitter = GetComponent(self.togGroup, UEUI.ContentSizeFitter);
    if count <= 1 then
        contentSizeFitter.enabled = false;
        self.togGroup.sizeDelta = Vector2.New(self.togGroup.sizeDelta.x, 616);
    else
        contentSizeFitter.enabled = true;
    end
end

function UI_League_Language:AddTarget(data)
    local obj = UEGO.Instantiate(self.ui.m_goToggle,self.togGroup)
    SetActive(obj, true)
    obj.name = "Toggle" .. data.id
    local txt = GetChild(obj,"m_txt",UEUI.Text)
    txt.text = data.name
    
    local tog = GetComponent(obj,UEUI.Toggle)
    tog.onValueChanged:RemoveAllListeners()
    tog.onValueChanged:AddListener(function(isOn)
        UnifyOutline(txt,isOn and "b66127" or "156DE4")
        if isOn then
            self.leagueLangID = data.unityid
        end
    end);
    self.togList[data.unityid] = tog
end

function UI_League_Language:OnRefresh(param)

end

function UI_League_Language:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
    
	for k,v in pairs(self.ToggleList) do
		if v.obj then
			UEGO.Destroy(v.obj)
		end
	end
	self.ToggleList = nil
	self.togGroup = nil
end

function UI_League_Language:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnClose" then
        EventMgr:Dispatch(EventID.REFRESH_UNION_LANGID,self.leagueLangID)
        self:Close()
    end
end

return UI_League_Language