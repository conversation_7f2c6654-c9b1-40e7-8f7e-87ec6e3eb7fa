local UI_UnionShop = Class(BaseView)

local BubbleItem = require("UI.BubbleItem");

function UI_UnionShop:OnInit()
    self.curType = 1;
    self.isCheckClose = false
    self.time = 0;
    self.addTime = 0;
    self.itemList = {};

    EventMgr:Add(EventID.UNION_ID_CHANGE, self.CheckClose, self);
end

function UI_UnionShop:OnCreate(param)
    self.bubbleItem = BubbleItem.new("UI_UnionShop");
    self.bubbleItem:Init(self.uiGameObject, {x = -350, y = 800}, function()
        self:Close();
    end);

    self:SetIsUpdateTick(true);
    self:OnSelectType(self.curType, true);
    self:CheckTogRedState();
end

function UI_UnionShop:OnRefresh(param)
    if param == 1 then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9376));
        self:OnSelectType(self.curType, true);
        self:CheckTogRedState(); 
    end
end

function UI_UnionShop:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.CheckClose, self);

    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:SetCloseCall(nil);
        self.bubbleItem:CloseImmediately();
    end
end

function UI_UnionShop:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:CheckClose();
    elseif string.startswith(name, "m_tog") then
        --if self.ui[name] then
        --    if self.ui[name].isOn == false then
        --        SetUISize(self.ui[name], 458, 118);
        --        return;
        --    end
        --    SetUISize(self.ui[name], 484, 118);
        --    UIRefreshLayout(self.ui.m_goTogList);
        --end
        local t = string.gsub(name, "m_tog", "");
        t = v2n(t);
        if t then
            self:OnSelectType(t);
        end
    end
end

function UI_UnionShop:TickUI(deltaTime)
    if self.time > 0 then
        self.addTime = self.addTime + deltaTime;
        if self.addTime >= 1 then
            self.addTime = self.addTime - 1;
            self.time = self.time - 1;
            if self.time < 0 then
                self.time = 0;
            end
            self.ui.m_txtTime.text = TimeMgr:ConverSecondToString(self.time);
        end
    end
end

function UI_UnionShop:AutoClose()
    self:CheckClose();
end

function UI_UnionShop:CheckClose()
    if not self.isCheckClose and self.bubbleItem and self.bubbleItem:IsHaveItem() then
        self.isCheckClose = true;
        self.bubbleItem:Close();
    else
        self.isCheckClose = true;
        self:Close();
    end
end

function UI_UnionShop:OnSelectType(t, isReset)
    if not isReset and self.curType == t then return end
    self.curType = t;

    self.shopArr = {};
    local list = ConfigMgr:GetDataByKey(ConfigDefine.ID.union_shop, "type", t, true);
    for i = 1, #list do
        local data = list[i]
        local isOpen = LeagueManager:CheckShopItemIsOpen(data)
        if isOpen then
            local itemDic = string.split(list[i].item, "|");
            if NetLeagueData:GetShopItemId(itemDic[1]) then
                table.insert(self.shopArr, list[i]);
            end
        end
    end
    table.sort(self.shopArr, function(a, b)
        if a.sort and b.sort then
            return a.sort < b.sort;
        end
        return false;
    end);

    local count = #self.shopArr;
    local num = #self.itemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    for i = 1, len do
        local item = self.itemList[i];
        if not item then
            item = CreateGameObjectWithParent(self.ui.m_goShopItem, rootTrans);
            table.insert(self.itemList, item);
        end
        SetActive(item, i <= count);

        if i <= count then
            self:LoadShopCellData(i, item);
        end
    end
    
    local index = 0;
    if count > 3 then
        local data;
        for i = 1, count do
            data = self.shopArr[i];
            if not data.unlock or LeagueManager:JudgeScienceLevel(data.unlock) then
                local limitNum = data.limit_num;
                if data.is_limit_up and data.is_limit_up == 1 then
                    limitNum = limitNum + LeagueManager:GetScienceAdd(SCIENCE_TYPE.SHOP_BUY);
                end
                local remainNum = limitNum - NetLeagueData:GetShopBuyNum(data.type, data.id);
                if remainNum > 0 then
                    index = i - 1;
                    break;
                end
            end
        end
    end

    self.time = NetLeagueData:GetShopTime(t);
    self.ui.m_txtTime.text = TimeMgr:ConverSecondToString(self.time);
    SetActive(self.ui.m_goTime, t ~= 3);
end

function UI_UnionShop:LoadShopCellData(idx, obj)
    local nameTxt = GetChild(obj, "nameTxt", UEUI.Text);
    local remainTxt = GetChild(obj, "remainTxt", UEUI.Text);
    local tipBtn = GetChild(obj, "tipBtn");
    local icon = GetChild(obj, "icon", UEUI.Image);
    local numTxt = GetChild(obj, "numTxt", UEUI.Text);
    local openTxt = GetChild(obj, "openTxt", UEUI.Text);
    local buyBtn = GetChild(obj, "buyBtn", UEUI.Image);
    local buyIcon = GetChild(obj, "buyBtn/buyIcon", UEUI.Image);
    local buyTxt = GetChild(obj, "buyBtn/buyTxt", UEUI.Text);
    local freeTxt = GetChild(obj, "buyBtn/m_txtAuto66", UEUI.Text);
    local soldOutBg = GetChild(obj, "soldOutBg");
    local redImg = GetChild(obj, "redImg");

    local data = self.shopArr[idx];
    local itemDic = string.split(data.item, "|");
    local itemId = NetLeagueData:GetShopItemId(itemDic[1]);
    itemId = v2n(NetSeasonActivity:GetChangeItemId(itemId));
    local itemCfg = ItemConfig:GetDataByID(itemId);
    nameTxt.text = LangMgr:GetLang(itemCfg.id_lang);
    numTxt.text = "x" .. itemDic[2];
    SetImageSync(icon, itemCfg.icon_b, false);

    local limitNum = data.limit_num;
    if data.is_limit_up and data.is_limit_up == 1 then
        limitNum = limitNum + LeagueManager:GetScienceAdd(SCIENCE_TYPE.SHOP_BUY);
    end
    local remainNum = limitNum - NetLeagueData:GetShopBuyNum(data.type, data.id);
    remainTxt.text = string.format(LangMgr:GetLang(84), remainNum);

    local costDic = string.split(data.cost, "|");
    local costId = v2n(costDic[1]);
    local price = v2n(costDic[2]);
    local isFree = costId == 0;
    if not isFree then
        buyTxt.text = price;
        SetImageSync(buyIcon, ItemConfig:GetIcon(costId), false);
        SetImageSync(buyBtn, "Sprite/ui_public/button1-blue.png", false);
    else
        SetImageSync(buyBtn, "Sprite/ui_public/button1-green.png", false);
    end

    local unlock = not data.unlock or LeagueManager:JudgeScienceLevel(data.unlock);
    if not unlock then
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, data.unlock);
        local nameStr = LangMgr:GetLang(config.tech_name);
        openTxt.text = LangMgr:GetLangFormat(9392, nameStr, config.lv);

        local preferredWidth = openTxt.preferredWidth;
        local preferredHeight = openTxt.preferredHeight;
        if preferredWidth > 300 then
            preferredWidth = 300;
        end
        if preferredHeight > 40 then
            preferredHeight = 40;
        end
        SetUISize(openTxt, preferredWidth, preferredHeight);

        RemoveUIComponentEventCallback(openTxt, UEUI.Button)
        AddUIComponentEventCallback(openTxt, UEUI.Button, function(arg1, arg2)
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(9377,  nameStr, config.lv));
        end);
    end

    SetActive(buyIcon, not isFree);
    SetActive(buyTxt, not isFree);
    SetActive(freeTxt, isFree);

    SetActive(buyBtn, remainNum > 0 and unlock);
    SetActive(redImg, remainNum > 0 and unlock and isFree);
    SetActive(openTxt, remainNum > 0 and not unlock);
    SetActive(numTxt, remainNum > 0);
    SetActive(soldOutBg, remainNum <= 0);

    RemoveUIComponentEventCallback(tipBtn, UEUI.Button)
    AddUIComponentEventCallback(tipBtn, UEUI.Button, function(arg1, arg2)
        if itemCfg.type_use == ItemUseType.ObjItemChooseBox then
            UI_SHOW(UIDefine.UI_ItemChooseBox, {m_Id = itemCfg.id}, true);
        elseif itemCfg.type_name == 5 then
            if ItemConfig:IsShowUIBoxProbShow(itemCfg.id) then
                UI_SHOW(UIDefine.UI_BoxProbShow, itemCfg)
            else
                UI_SHOW(UIDefine.UI_MarketBox, itemCfg)
            end 
        else
            UI_SHOW(UIDefine.UI_ItemTips, itemCfg.id)
        end
    end);

    RemoveUIComponentEventCallback(buyBtn, UEUI.Button)
    AddUIComponentEventCallback(buyBtn, UEUI.Button, function(arg1, arg2)
        local buyCount = NetLeagueData:GetShopBuyNum(data.type, data.id);
        if limitNum > buyCount then
            if isFree then
                NetLeagueData:SetShopBuyNum(data.type, data.id);
                self.bubbleItem:FlyItem(v2n(itemId), v2n(itemDic[2]), icon.transform.position);
                self:LoadShopCellData(idx, obj);

                if limitNum - buyCount == 1 then
                    UI_UPDATE(UIDefine.UI_MainFace, 50);
                    UI_UPDATE(UIDefine.UI_Union, 1, 4);
                    UI_UPDATE(UIDefine.UI_UnionMain, 1);
                    self:CheckTogRedState();
                end
            else
                local money = NetUpdatePlayerData:GetResourceNumByID(costId);
                if money >= price then
                    NetLeagueData:SetShopBuyNum(data.type, data.id);
                    NetUpdatePlayerData:AddResource(PlayerDefine[costId], -price, nil, nil, "UI_UnionShop");

                    self.bubbleItem:FlyItem(v2n(itemId), v2n(itemDic[2]), icon.transform.position);
                    self:LoadShopCellData(idx, obj);
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9375));
                end
            end
        end
    end);
end

function UI_UnionShop:CheckTogRedState()
    local list;
    local data;
    local state;
    for i = 1, 3 do
        local list = ConfigMgr:GetDataByKey(ConfigDefine.ID.union_shop, "type", i, true);
        state = false;

        for j = 1, #list do
            data = list[j];
            local isOpen = LeagueManager:CheckShopItemIsOpen(data)
            if isOpen then
                if not data.unlock or LeagueManager:JudgeScienceLevel(data.unlock) then
                    local costDic = string.split(data.cost, "|");
                    local limitNum = data.limit_num;
                    if data.is_limit_up and data.is_limit_up == 1 then
                        limitNum = limitNum + LeagueManager:GetScienceAdd(SCIENCE_TYPE.SHOP_BUY);
                    end
                    if v2n(costDic[1]) == 0 and limitNum > NetLeagueData:GetShopBuyNum(data.type, data.id) then
                        state = true;
                        break;
                    end
                end
            end
        end

        SetActive(self.ui["m_imgRed" .. i], state);
    end
end

return UI_UnionShop