local UI_Market = Class(BaseView)
local remainTimes = nil
local BubbleItem = require("UI.BubbleItem")

local list_cellView = {}
local LineItemNums = 3

local posTab =
{
	[1] = Vector3.New(0, 0, 0),
	[2] = Vector3.New(-40, 28, 0),
	[3] = Vector3.<PERSON>(9, -13, 0),
	[4] = Vector3.New(-30, 0, 0),
	[5] = Vector3.New(10, -28, 0),
}

function UI_Market:OnInit()

end

function UI_Market:OnCreate(param)	
	UIMgr:Refresh(UIDefine.UI_MainFace,7,false)
	UIMgr:Refresh(UIDefine.UI_CopyMainFace2,7,false)
	UIMgr:RefreshAllMainFace(12,12,{isShow = true,list={PlayerDefine.Diamond,PlayerDefine.Coin}})

	NetNotification:NotifyNormal(NotifyDefine.OpenMarket)
	
	self:SetIsUpdateTick(true)

	self.rewardTab = {}
	self.popListData = {}
	self.isclosing = false
	self.isPlaying = false
	
	list_cellView = {}
	
	self.row1 = GET_UI(self.uiGameObject, "row1", "RectTransform")
	self.row2 = GET_UI(self.uiGameObject, "row2", "RectTransform")
	SetActive(self.row2, false)
	
	self.centerObj = GET_UI(self.uiGameObject, "centerObj", "RectTransform")
	UIRefreshLayout(self.centerObj)

	local info = NetUpdatePlayerData:GetPlayerInfo()
	self.ui.m_txtDiamond.text = math.floor(info["diamond"])
	self.ui.m_txtCoin.text = math.floor(info["coin"])

	self.recordBuyCount = nil
	self.recordBuyCount = {}
	self.imageList = {}
	
	self.isPlaying = true
	self.isRefresh = true
	self.thinkStr = "mapShop"
	local curMap = NetUpdatePlayerData.playerInfo.curMap
	if curMap == MAP_ID_ZOO then
		self.thinkStr = "zooShop"
	elseif curMap ~= MAP_ID_MAIN and curMap ~= MAP_ID_SECOND then
		self.thinkStr = "LimitShop"
	end
	self:InitBubble()
	self:ControlRow()
	self.uiIsInit = false
	self.toggle_prefab = GET_UI(self.uiGameObject, "m_tog", "RectTransform")
	SetActive(self.toggle_prefab, false)
	
	self.uiIsInit = true
	
	self.toggle_perant = GET_UI(self.uiGameObject, "tog", "RectTransform")
	self.toggleTab = {}
	self:InitToggleList(param)
	
	self:InitUI()

	local curMap = NetUpdatePlayerData.playerInfo.curMap
	if param == 0 and (curMap == MAP_ID_MAIN or curMap == MAP_ID_SECOND) then
		local tog = GET_UI(self.uiGameObject, "m_tog" .. 2, TP(UEUI.Toggle))
		tog.isOn = true
	end
	if param == "Money" then
		local tog = GET_UI(self.uiGameObject, "m_tog" .. 1, TP(UEUI.Toggle))
		tog.isOn = true
	end
	if param == 0 and curMap ~= MAP_ID_MAIN and curMap ~= MAP_ID_ZOO and curMap ~= MAP_ID_SECOND then
		local tog = GET_UI(self.uiGameObject, "m_tog" .. 5, TP(UEUI.Toggle))
		tog.isOn = true
		local curMapID = NetUpdatePlayerData.playerInfo.curMap
		self:CheckFreeAutoScroll(curMapID,self.index)
	end
	EventMgr:Add(EventID.PAY_SUCCESS,self.OnPaySuccess,self)
	
	self.timerStr = UIDefine.UI_Market.."a"
end

function UI_Market:OnPaySuccess()
	self.ui.m_TableViewV:ReloadData()
end

function UI_Market:InitToggleList(param)
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local togleIdList = MarketMgr:GetTogId(curMapID)
	local indexDef = 0
	for k, v in pairs (togleIdList) do
		local togleId = string.split(v, "|")
		self.ui.m_txtTogName.text = LangMgr:GetLang(togleId[2])
		self.ui.m_txtTogName2.text = LangMgr:GetLang(togleId[2])
		local go = UEGO.Instantiate(self.toggle_prefab)
		go.transform:SetParent(self.toggle_perant)
		go.transform.localScale = Vector3.New(1, 1, 1)
		go.transform:SetLocalPosition(0, 0, 0)
		SetActive(go, true)
		
		go.name = "m_tog" .. tostring(togleId[1])
		self.toggleTab[k] =  go
		self.index = v2n(togleId[1])
		
		if indexDef == 0 then
			indexDef = indexDef + 1
		end
	
		--RemoveUIComponentEventCallback(go, UEUI.Toggle)
		AddUIComponentEventCallback(go, UEUI.Toggle, function(arg1, arg2)
			if not self.uiIsInit then
				return
			end

			self:onUIEventClick(arg1 , arg2, isLight)
		end)
	end
	UIRefreshLayout(self.toggle_perant)
end

function UI_Market:ControlRow()
	local curMap = NetUpdatePlayerData.playerInfo.curMap
	if curMap == MAP_ID_MAIN or curMap == MAP_ID_SECOND then
		local showRow = NetMarketData:get_IsFirst()
		if showRow then
			local rowPos = self.row1.anchoredPosition
			rowPos.y = 150
			DOKill(self.row1)
			self.row1.anchoredPosition = rowPos
			local endPos = Vector2.New(rowPos.x , 34)
			self.row1:DOAnchorPos(endPos, 0.6):SetEase(Ease.OutQuad):SetLoops(-1, LoopType.Yoyo)
			SetActive(self.row1, true)
		else
			SetActive(self.row1, false)
		end
	else
		SetActive(self.row1, false)
	end

	if self.index ~= 4 then
		SetActive(self.row2, false)
	end
end

function UI_Market:showFree()
	local showFree = NetMarketData:get_IsFirst()
	if showFree then
		SetActive(self.row2, true)
		local rowPos = self.row2.anchoredPosition
		rowPos.y = 306
		self.row2.anchoredPosition = rowPos
		local endPos = Vector2.New(rowPos.x, 376)
		DOKill(self.row2)
		self.row2:DOAnchorPos(endPos, 0.6):SetEase(Ease.OutQuad):SetLoops(-1, LoopType.Yoyo)
	else
		SetActive(self.row2, false)
	end
end


function UI_Market:InitUI()
	self:UpdateInfo()
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	self.marketdata = MarketMgr:GetDataWithType(self.index, curMapID)
	--self.marketdata = self:SplitMarketData(self.marketdata)

	self.ui.m_TableViewV.GetItemCount = function (num)
		local dataCount = GetTableLength(self.marketdata)
		local lineCount = Mathf.Ceil(dataCount / LineItemNums)
		return lineCount
	end

	self.ui.m_TableViewV.GetItemGo = function(obj)
		local Cellobj = self.ui.m_goItmeLine
		--local assetPath = string.format("%s%s.prefab", AssetDefine.UIPrefabPath, "List_MarketCell")
		--Cellobj = ResMgr:LoadAssetSync(assetPath, AssetDefine.LoadType.Instant)
		return Cellobj
	end

	self.ui.m_TableViewV.UpdateItemCell = function(idx, obj)
		self:LoadLineCellData(idx, obj)
	end

	self.ui.m_TableViewV:InitTableViewByIndex()

	local scrollrect = GetChild(self.uiGameObject, "bg/TbObj/m_TableViewV", UEUI.ScrollRect)
	scrollrect.onValueChanged:RemoveAllListeners()
	scrollrect.onValueChanged:AddListener(function (value)
		self.isPlaying = false
	end)

	self:UpdateRedPoint()
end

function UI_Market:GetFreeId(tIdx, marketConfig)
	local isFreeList = marketConfig.buy_type
	if not isFreeList then return end
	local isFree = SplitStringToNum(isFreeList, "|")
	if isFree[1] == MARKET_BUY_TYPE.FREE then
		return marketConfig.id
	end
end

function UI_Market:UpdateInfo()
	self.RefreshDiamond = GlobalConfig:GetNumber(1001)
	self.ui.m_txtRePrice.text = self.RefreshDiamond
end

function UI_Market:getOpenLv(tIdx, marketCfg)
	local lv = nil
	if marketCfg.show_contidion then
		local Openlv = marketCfg.show_contidion
		local lvList = string.split(Openlv, "|")
		if #lvList > 1 then
			lv = lvList[3]
		else
			lv = Openlv
		end
	else
		lv = marketCfg.zoo_level
	end
	return lv
end

function UI_Market:UpdateRedPoint()
	local curMapID = NetUpdatePlayerData.playerInfo.curMap	
	for key, value in pairs(self.toggleTab) do
		-- if curMapID == 2 and key == 2 then
		-- 	key = 5
		-- end
		if curMapID == 7 and key == 1 then
			key = 6			
		end
		if curMapID ~= MAP_ID_MAIN and curMapID ~= MAP_ID_SECOND and curMapID ~= MAP_ID_ZOO and key == 2 then
			key = 5
		end
		local PointShow,index = MarketConfig:isHaveRedPoint(curMapID, key,true)
		local red = GET_UI(value, "m_imgRedPoint", TP(UEUI.Image))
		if red and curMapID == 7 then
			--self:ZooFreeAutoScroll(index)
		end
		SetActive(red, PointShow)
	end
end

function UI_Market:InitBubble()
	self.bubble = BubbleItem.new(self.thinkStr)
	self.bubble:Init(self.uiGameObject,{x = -375, y = 800},	function() self:Close() end, false)	
end

function UI_Market:LoadLineCellData(idx, obj)
	for i = 1, LineItemNums do
		local dataIndex = idx * LineItemNums + i
		local itemTrans = GetChild(obj, "bagItemList/Viewport/Content", UE.RectTransform)
		local lineItemObj 
		if itemTrans then
			if itemTrans.transform.childCount < i then
				lineItemObj = UEGO.Instantiate(self.ui.m_goListMarketCell.transform,itemTrans)
			else
				lineItemObj = itemTrans.transform:GetChild(i - 1)
			end
		end
		if lineItemObj then
			if dataIndex <= GetTableLength(self.marketdata) then
				SetActive(lineItemObj,true)
				self:LoadCellData(dataIndex,lineItemObj)
			else
				SetActive(lineItemObj,false)
			end
		end
	end
end

function UI_Market:LoadCellData(idx, obj)
	--self.tIdx = idx + 1
	local tIdx = idx 
	
	local AnimObj          = GetChild(obj, "AnimObj", UE.RectTransform)
	local num              = GetChild(obj, "AnimObj/ImgCount/m_txtNum", UEUI.Text)
	local name             = GetChild(obj, "AnimObj/m_txtName", UEUI.Text)
	local lockObj          = GetChild(obj, "AnimObj/OnLockObj", UE.RectTransform)
	local BuyNum           = GetChild(obj, "AnimObj/BuyNum")
	local ImgCount         = GetChild(obj, "AnimObj/ImgCount", UE.RectTransform)
	local MoneyTxt         = GetChild(obj, "AnimObj/m_BtnCliam/deepList/m_txtMoney", UEUI.Text)
	local MoneyOutline 	   = GetChild(obj, "AnimObj/m_BtnCliam/deepList/m_txtMoney", UEUI.Outline)
	local buyImg1          = GetChild(obj, "AnimObj/m_BtnCliam/deepList/m_BuyImg1", UEUI.Image)
	local buyImg2          = GetChild(obj, "AnimObj/m_BtnCliam/deepList/m_BuyImg2", UEUI.Image)
	local BtnBuy           = GetChild(obj, "AnimObj/m_BtnCliam/imgCliam", UEUI.Image)
	local BtnCliam         = GetChild(obj, "AnimObj/m_BtnCliam", UEUI.Button)
	local ItemImgBg        = GetChild(obj, "AnimObj/ItemIng", UEUI.Image)
	local loackNum         = GetChild(obj, "AnimObj/OnLockObj/m_txtLock", UEUI.Text)
	local price            = GetChild(obj, "AnimObj/BuyNum/m_txtPrice", UEUI.Text)
	local coinImg          = GetChild(obj, "AnimObj/BuyNum/coin", UEUI.Image)
	local tipBtn           = GetChild(obj, "AnimObj/m_btnTip", UEUI.Button)
	local norImg           = GetChild(obj, "AnimObj/normalImg", UEUI.Image)
	local spelImg1         = GetChild(obj, "AnimObj/spelImg1", UEUI.Image)
	local spelImg2         = GetChild(obj, "AnimObj/spelImg2", UEUI.Image)
	local spelImg3         = GetChild(obj, "AnimObj/spelImg3", UEUI.Image)
	local speicalNumBg     = GetChild(obj, "AnimObj/m_imgSpecial", UEUI.Image)
	local speicalName	   = GetChild(obj, "AnimObj/m_txtNameSpe", UEUI.Text)
	local m_txtSpecialNum  = GetChild(obj, "AnimObj/BuyNum/m_txtSpecialNum", UEUI.Text)
	local freeArrow 	   = GetChild(obj, "AnimObj/rtransArrowPos/rtransArrowAct", UE.RectTransform)
	local btnSoldOut       = GetChild(obj, "AnimObj/m_BtnSoldOut", UEUI.Button)
    local soldOutMark      = GetChild(obj, "AnimObj/m_goSoldOutMark")
	
	if not list_cellView[tIdx] then
		list_cellView[tIdx] = {}
	end
	list_cellView[tIdx].m_btnTip = tipBtn
	list_cellView[tIdx].m_BtnCliam = BtnCliam
	list_cellView[tIdx].name = name
	list_cellView[tIdx].speicalName = speicalName
	list_cellView[tIdx].price = price
	list_cellView[tIdx].m_txtSpecialNum = m_txtSpecialNum
	list_cellView[tIdx].ImgCount = ImgCount
	list_cellView[tIdx].BtnBuy = BtnBuy
	list_cellView[tIdx].buyImg1 = buyImg1
	list_cellView[tIdx].buyImg2 = buyImg2
	list_cellView[tIdx].speicalNumBg = speicalNumBg
	list_cellView[tIdx].norImg = norImg
	list_cellView[tIdx].ItemImgBg = ItemImgBg
	list_cellView[tIdx].lockObj = lockObj
	list_cellView[tIdx].BuyNum = BuyNum
	list_cellView[tIdx].MoneyTxt = MoneyTxt
	list_cellView[tIdx].MoneyOutline = MoneyOutline
	list_cellView[tIdx].loackNum = loackNum
	list_cellView[tIdx].coinImg = coinImg
	list_cellView[tIdx].num = num
	list_cellView[tIdx].freeArrow = freeArrow
	list_cellView[tIdx].btnSoldOut = btnSoldOut
	list_cellView[tIdx].soldOutMark = soldOutMark
	
	self:RefreshCell(tIdx)
	
	self:AddBtnClick(obj, tIdx)

	local txtSoldOut       = GetChild(obj, "AnimObj/m_BtnSoldOut/m_txtSoldOut", UEUI.Text)
	txtSoldOut.text        = LangMgr:GetLang(53)
end

function UI_Market:RefreshCell(idx)
	local tIdx = idx
	
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local showRow = NetMarketData:get_IsFirst()

	local cfgData = ConfigMgr:GetDataByID(ConfigDefine.ID.market, self.marketdata[tIdx])
	if not cfgData then
		return
	end
	
	if cfgData.which_lv == 1 then
		self.playerlv = NetUpdatePlayerData:GetLevel()
	else
		self.playerlv = ZoolevelConfig:GetLevel()
	end

	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local freeId = self:GetFreeId(tIdx, cfgData)

	if self.isPlaying == true then
		local canvasGroup = self:GetUIComponent("CanvasGroup")
		canvasGroup:DOFade(1, 0.5)

		local function finish()
			local curMap = NetUpdatePlayerData.playerInfo.curMap
			if curMap == MAP_ID_MAIN or curMap == MAP_ID_SECOND then
				if self.index == 4 and tIdx == freeId and showRow then
					self:showFree()
				end
			end
		end
		
	end

	local Openlv = self:getOpenLv(tIdx, cfgData)
	local Price = string.split(cfgData.buy_type, "|")
	list_cellView[tIdx].name.text  = LangMgr:GetLang(cfgData["name"])
	list_cellView[tIdx].speicalName.text = LangMgr:GetLang(cfgData["name"])
	local buyCount = NetMarketData:getRecordBuyCount(self.index, cfgData.id)
	if self.index ~= 1 and cfgData.buy_num then
		list_cellView[tIdx].price.text = "x" .. cfgData.num
		list_cellView[tIdx].m_txtSpecialNum.text = "x" .. cfgData.num
		remainTimes = tonumber(cfgData.buy_num) - tonumber(buyCount)
	else
		SetActive(list_cellView[tIdx].ImgCount, false)
		list_cellView[tIdx].price.text = cfgData.num
	end

	local hex = "0069AB"
	if tonumber(Price[1]) == MARKET_BUY_TYPE.GOLD or tonumber(Price[1]) == MARKET_BUY_TYPE.DIAMOND then
		SetImageSync(list_cellView[tIdx].BtnBuy, "Sprite/ui_public/button2-blue.png", false)
		hex = "0069AB"
	else
		SetImageSync(list_cellView[tIdx].BtnBuy, "Sprite/ui_public/button2-green.png", false)
		hex = "056e00"
	end

	UnifyOutline(list_cellView[tIdx].MoneyTxt.gameObject,hex)
	
	local function isDiaOrGold()
		if tonumber(Price[1]) == MARKET_BUY_TYPE.GOLD then
			SetActive(list_cellView[tIdx].buyImg2, true)
			SetActive(list_cellView[tIdx].buyImg2, true)
			SetActive(list_cellView[tIdx].buyImg1, false)
		elseif tonumber(Price[1]) == MARKET_BUY_TYPE.DIAMOND then
			SetActive(list_cellView[tIdx].buyImg2, false)
			SetActive(list_cellView[tIdx].buyImg1, true)
		end
	end

	if NetUpdatePlayerData.playerInfo.curMap == 1 or NetUpdatePlayerData.playerInfo.curMap == 7 then
		if v2n(cfgData.random_value) ~= 1 and cfgData.zoo_sign then
			local CellitemId = cfgData.item_id
			local itemId = ConfigMgr:GetDataByID(ConfigDefine.ID.item, CellitemId)
			local qua = itemId.id_rare
			SetActive(list_cellView[tIdx].speicalNumBg, true)
			SetActive(list_cellView[tIdx].name, false)
			SetActive(list_cellView[tIdx].price, false)
		else
			SetActive(list_cellView[tIdx].speicalNumBg, false)
			SetActive(list_cellView[tIdx].speicalName, false)
			SetActive(list_cellView[tIdx].m_txtSpecialNum, false)
			SetActive(list_cellView[tIdx].price, true)
		end
	else
		SetActive(list_cellView[tIdx].speicalNumBg, false)
		SetActive(list_cellView[tIdx].speicalName, false)
		SetActive(list_cellView[tIdx].m_txtSpecialNum, false)
	end
	if  IsNilOrEmpty(cfgData.bg) then
		cfgData.bg = "Sprite/ui_shop/shoplist-bg.png"
	end
	SetImageSprite(list_cellView[tIdx].norImg,cfgData.bg,false)
	
	local bgTitle = {
		["Sprite/ui_shop/shoplist-bg.png"] = "AE4A00";
		["Sprite/ui_shop/shoplist-bg2.png"] = "2B721E";
		["Sprite/ui_shop/shoplist-bg3.png"] = "CD2F2C";
	}
	
	if bgTitle[cfgData.bg] then
		UnifyOutline(list_cellView[tIdx].name.gameObject,bgTitle[cfgData.bg])
	end
	local itemIconPath = ItemConfig:GetIcon(cfgData.item_id)
	SetImageSync(list_cellView[tIdx].ItemImgBg, itemIconPath, false)
	local count = NetMapNoteData:GetNoteCount(nil, NetMapNoteData.ID.item_has, cfgData.item_id)

	local function isOpen() --解锁
		if count ~= nil and self.playerlv >= tonumber(Openlv) or cfgData.item_id == 50002 or cfgData.item_id == 50103 or cfgData.item_id > 71003 then  --解锁状态
			isDiaOrGold()
			SetActive(list_cellView[tIdx].lockObj, false)
			SetActive(list_cellView[tIdx].ImgCount, true)
			SetActive(list_cellView[tIdx].BuyNum, true)
			SetActive(list_cellView[tIdx].tipBtn, true)
			SetActive(list_cellView[tIdx].name, true)
			SetUIBtnGrayAndEnable(list_cellView[tIdx].m_BtnCliam.gameObject, true)
			SetUIImageGray(list_cellView[tIdx].BtnBuy, false)
			list_cellView[tIdx].MoneyTxt.text = Price[2]
			
			--list_cellView[tIdx].MoneyOutline.effectColor = Color.New(0/255, 105/255, 171/255, 1)
			local isSoldOut = false
			if remainTimes == nil then
				SetUIBtnGrayAndEnable(list_cellView[tIdx].m_BtnCliam.gameObject, false)
				SetUIImageGray(list_cellView[tIdx].BtnBuy, true)
				--SetUIImageGray(list_cellView[tIdx].ItemImgBg, true)
				isSoldOut = true
			else
				isSoldOut = remainTimes <= 0
				if remainTimes <= 0 then
					SetActive(list_cellView[tIdx].buyImg2, false)
					SetActive(list_cellView[tIdx].buyImg1, false)
					list_cellView[tIdx].MoneyTxt.text = LangMgr:GetLang(53)
					--SetUIImageGray(list_cellView[tIdx].ItemImgBg, true)
					SetUIBtnGrayAndEnable(list_cellView[tIdx].m_BtnCliam.gameObject, false)
					SetUIImageGray(list_cellView[tIdx].BtnBuy, true)
				else
					--SetUIImageGray(list_cellView[tIdx].ItemImgBg, false)
					list_cellView[tIdx].ItemImgBg.color = Color.New(1, 1, 1, 1)
				end
			end

			SetActive(list_cellView[tIdx].m_BtnCliam, not isSoldOut)
			SetActive(list_cellView[tIdx].soldOutMark, isSoldOut)
			SetActive(list_cellView[tIdx].btnSoldOut, isSoldOut)
			SetActive(list_cellView[tIdx].ItemImgBg, not isSoldOut)
			SetActive(list_cellView[tIdx].BuyNum, not isSoldOut)
			SetActive(self.ui.m_imgClock, true)
			--SetActive(list_cellView[tIdx].ItemImgBg, true)
		else   --未解锁
			SetActive(list_cellView[tIdx].BuyNum, false)
			SetActive(list_cellView[tIdx].speicalNumBg, false)
			SetActive(list_cellView[tIdx].speicalName, false)
			SetActive(list_cellView[tIdx].ItemImgBg, false)
			SetActive(list_cellView[tIdx].lockObj, true)
			SetActive(list_cellView[tIdx].ImgCount, false)
			SetActive(list_cellView[tIdx].buyImg1, false)
			SetActive(list_cellView[tIdx].buyImg2, false)
			SetActive(list_cellView[tIdx].name, false)
			SetActive(list_cellView[tIdx].tipBtn, false)
			SetUIBtnGrayAndEnable(list_cellView[tIdx].m_BtnCliam.gameObject, false)
			SetUIImageGray(list_cellView[tIdx].BtnBuy, true)
			if Openlv and tonumber(Openlv) > 1 then
				list_cellView[tIdx].loackNum.text = string.format(LangMgr:GetLang(91), Openlv)
			else
				list_cellView[tIdx].loackNum.text = LangMgr:GetLang(93)
			end
			SetActive(self.ui.m_imgClock, true)
			list_cellView[tIdx].MoneyTxt.text = LangMgr:GetLang(96)
			--list_cellView[tIdx].MoneyOutline.effectColor = Color.New(85/255, 85/255, 85/255, 1)
		end
	end

	local function state()  --不可领状态
		SetActive(list_cellView[tIdx].lockObj, true)
		SetActive(list_cellView[tIdx].buyImg1, false)
		SetActive(list_cellView[tIdx].buyImg2, false)
	end

	local function state1()  --可领状态
		isDiaOrGold()
		SetActive(list_cellView[tIdx].ImgCount, false)
		SetActive(list_cellView[tIdx].BuyNum, true)
		--local itemIconPath = ItemConfig:GetIcon(cfgData.item_id)
		SetUIImage(list_cellView[tIdx].ItemImgBg, cfgData.img, false)
		SetActive(list_cellView[tIdx].lockObj, false)
		SetActive(list_cellView[tIdx].tipBtn, true)
	end

	local function state3()
		SetActive(list_cellView[tIdx].ImgCount, false)
		SetActive(self.ui.m_imgClock, false)
		SetActive(self.ui.m_imgRefreshBg, false)
		SetActive(self.ui.m_btnRefresh, false)
		SetActive(list_cellView[tIdx].coinImg, true)
		list_cellView[tIdx].MoneyTxt.text = Price[2]
		self.ui.m_txtTips.text = LangMgr:GetLang(56)
	end

	if self.index ~= 1 then  --金币不限购
		if remainTimes ~= nil and remainTimes >= 0 then
			list_cellView[tIdx].num.text = string.format(LangMgr:GetLang(84), remainTimes)
		else
			if cfgData.buy_num then
				list_cellView[tIdx].num.text = string.format(LangMgr:GetLang(84), cfgData.buy_num)
			else
				list_cellView[tIdx].num.text = ""
			end
		end

		if remainTimes and remainTimes <= 0 then
			state()
			list_cellView[tIdx].ItemImgBg.color = Color.New(1, 1, 1, 0.5)
		end
		isOpen()
		SetActive(self.ui.m_btnRefresh, true)
		SetActive(self.ui.m_imgRefreshBg, true)
		self.ui.m_txtTips.text = LangMgr:GetLang(55)
		SetActive(list_cellView[tIdx].coinImg, false)
		SetActive(self.centerObj, true)
	else
		SetActive(list_cellView[tIdx].ItemImgBg, true)
		SetActive(list_cellView[tIdx].soldOutMark, false)
		SetActive(self.centerObj, false)
		isDiaOrGold()
		state1()
		state3()
	end

	local function Free()
		SetActive(list_cellView[tIdx].buyImg1, false)
		SetActive(list_cellView[tIdx].buyImg2, false)
		SetActive(list_cellView[tIdx].tipBtn, true)

		local isSoldOut = remainTimes <= 0
		if remainTimes <= 0 then
			list_cellView[tIdx].MoneyTxt.text = LangMgr:GetLang(53)
		else
			SetActive(self.ui.m_imgRedPoint, false)
			list_cellView[tIdx].MoneyTxt.text = LangMgr:GetLang(66)
			list_cellView[tIdx].freeArrow:DOAnchorPos(Vector2.New(24, 20), 0.5):SetLoops(-1, LoopType.Yoyo)
			local level = NetUpdatePlayerData:GetLevel()
			if level < 5 then
				SetActive(list_cellView[tIdx].freeArrow.gameObject,true)
			end
		end
		SetActive(list_cellView[tIdx].m_BtnCliam, not isSoldOut)
		SetActive(list_cellView[tIdx].soldOutMark, isSoldOut)
		SetActive(list_cellView[tIdx].btnSoldOut, isSoldOut)
		SetActive(list_cellView[tIdx].ItemImgBg, not isSoldOut)
		SetActive(list_cellView[tIdx].BuyNum, not isSoldOut)
	end
	
	if tonumber(Price[1]) == MARKET_BUY_TYPE.FREE and remainTimes and remainTimes > 0 then --正常商品免费
		Free()
	else
		SetActive(list_cellView[tIdx].freeArrow.gameObject,false)
	end
end

function UI_Market:AddBtnClick(obj, idx)
	local cfgData = ConfigMgr:GetDataByID(ConfigDefine.ID.market, self.marketdata[idx])
	local isGem = string.split(cfgData.buy_type, "|")
	local itemId = cfgData.item_id
	local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemId)
	local type = itemConfig.type_name
    local type_use = itemConfig.type_use

	RemoveUIComponentEventCallback(obj, UEUI.Button)
	AddUIComponentEventCallback(obj, UEUI.Button, function(arg1,arg2)
			
			local playerDiamond = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
			local playerCoin = NetUpdatePlayerData:GetResourceNumByID(ItemID.COIN)
			
			if arg1.name == "m_btnTip" then
                if type_use == ItemUseType.CardPack then
                    UI_SHOW(UIDefine.UI_CollectCardRate, itemId)
                else
                    if type == 5 then
                        if ItemConfig:IsShowUIBoxProbShow(itemId) then
                            UI_SHOW(UIDefine.UI_BoxProbShow, itemConfig)
                        else
                            UI_SHOW(UIDefine.UI_MarketBox, itemConfig, cfgData)
                        end	
                    else
                        UI_SHOW(UIDefine.UI_ItemDetail, 2, cfgData)
                    end
                end
			elseif arg1.name == "m_BtnCliam" then
				if tonumber(isGem[1]) == MARKET_BUY_TYPE.DIAMOND then
					if playerDiamond >= tonumber(isGem[2]) then  --玩家钻石足够
                        -- 本次登录不再提示
                        if UIMgr:GetOnly("MarketUseDiamond") then
                            self:CellBtnBuy(arg1, idx, obj)
                        else
                            -- 消耗钻石数量超过 100
                            if tonumber(isGem[2]) > 100 then
                                local buyParam = {
                                    gem = tonumber(isGem[2]),    -- 消耗的钻石
                                    itemID = cfgData.item_id,    -- 物品 ID
                                    itemNum = cfgData.num,       -- 物品数量
                                    type = 1                     -- 类型
                                }
                                -- 显示二次确认弹窗
                                UI_SHOW(UIDefine.UI_TipsOnly, "MarketUseDiamond", "", function()
                                    self:CellBtnBuy(arg1, idx, obj)
                                end, nil, nil, nil, nil, buyParam)
                            else
                                self:CellBtnBuy(arg1, idx, obj)
                            end
                        end
					else
						TriggerGiftConfig:CheckPlayerDiamond()
						UI_SHOW(UIDefine.UI_ActCenter,13)
					end
				else if tonumber(isGem[1]) == MARKET_BUY_TYPE.GOLD then
						if playerCoin >= tonumber(isGem[2]) then  --玩家金币足够，直接买
							self:CellBtnBuy(arg1, idx, obj)
						else
							
							UI_SHOW(UIDefine.UI_BuyGold, tonumber(isGem[2]) - playerCoin, cfgData, 2, function()  --金币不足先买金币
									--购买成功
									self:BuySucc(idx, obj)
								end)
						end
					else
						self:CellBtnBuy(arg1, idx, obj)
						--self.ui.m_TableViewH:ReloadData()
						self:RefreshCell(idx)
					end
				end
			end
		end)
end

function UI_Market:BuySucc(idx, obj)
	local marketCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.market, self.marketdata[idx])
	self.isPlaying = false
	--市场任意购买 --4
	EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.MARKET_BUY,1)
	local useCount = tonumber(NetMarketData:getRecordBuyCount(self.index, marketCfg.id))
	NetMarketData:setRecordBuyCount(self.index, marketCfg.id, useCount + 1)
	--刷新红点儿部分

	-- MarketConfig:FreshRedPoint(self.index)
	MarketConfig:FreshRedPoint_Alltogle(self.toggleTab)
	self:UpdateRedPoint()
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	local PointShow = MarketConfig:isHaveRedPoint(curMapID, self.index)
	SetActive(self.ui.m_imgRedPoint, PointShow)
	local price = string.split(marketCfg.buy_type, "|")
	local info = NetUpdatePlayerData:GetPlayerInfo()
	local itemId = marketCfg.item_id
	self:BubbleByIndexAnim(idx, itemId, obj , tonumber(marketCfg.num))

	local tempCoin = 0
	if itemId == 3 then
		AudioMgr:Play(11)
		NetUpdatePlayerData:AddResource(PlayerDefine.Coin, marketCfg.num,nil,nil,"shop")
		self.ui.m_doCoin:DORestart()
		AudioMgr:Play(12)
		AddDOTweenNumberDelay(tonumber(marketCfg.num), info["coin"], 0.3, 0.8, function(value)
				if self.ui.m_txtCoin then
					self.ui.m_txtCoin.text = math.ceil(info["coin"])
				end
			end)
	end

	if tonumber(price[1]) == MARKET_BUY_TYPE.GOLD then
		tempCoin = NetUpdatePlayerData:GetResourceNumByID(ItemID.COIN)
		NetUpdatePlayerData:AddResource(PlayerDefine.Coin, -price[2],nil,nil,"shop")
		NetNotification:NotifyConsume(3,itemId,marketCfg.num,ItemID.COIN,price[2])

		self.ui.m_doCoin:DORestart()
		AudioMgr:Play(12)
		AddDOTweenNumberDelay(-tonumber(price[2]), info["coin"], 0.3, 0.8, function(value)
				if self.ui.m_txtCoin then
					self.ui.m_txtCoin.text = math.ceil(info["coin"])
				end
			end)
	elseif tonumber(price[1]) == MARKET_BUY_TYPE.DIAMOND then
		NetUpdatePlayerData:AddResource(PlayerDefine.Diamond, -price[2],nil,nil,"shop")
		if self.index == 1 then
			NetNotification:NotifyConsume(5,idx + 1,1,ItemID.DIAMOND,price[2],LangMgr:GetLang(marketCfg.name))
		else
			NetNotification:NotifyConsume(3,itemId,marketCfg.num,ItemID.DIAMOND,price[2])
		end
		self.ui.m_doDiamond:DORestart()
		AudioMgr:Play(10)
		AddDOTweenNumberDelay(-tonumber(price[2]), info["diamond"], 0.3, 0.8, function(value)
				if self.ui.m_txtDiamond then
					self.ui.m_txtDiamond.text = math.ceil(info["diamond"])
				end
			end)
	end

	local ratio = GlobalConfig:GetString(1107)
	if tempCoin <= 0 or tempCoin < tonumber(price[2]) then
		-- 前面扣完了金币才来计算需要扣多少钻石--
		local value = math.ceil((price[2]-tempCoin) / tonumber(ratio))
		--NetUpdatePlayerData:AddResource(PlayerDefine.Coin, -info["coin"],nil,nil,"shop")
		NetUpdatePlayerData:AddResource(PlayerDefine.Diamond, -value,nil,nil,"shop")
		self.ui.m_doDiamond:DORestart()
		AudioMgr:Play(10)
		AddDOTweenNumberDelay(-value, info["diamond"], 0.3, 0.8, function(value)
				if self.ui.m_txtDiamond then
					self.ui.m_txtDiamond.text = math.ceil(info["diamond"])
				end
			end)
	end
	
	--TimeMgr:CreateTimer(self.timerStr,function()
			--self.ui.m_TableViewH:ReloadData()
		--end,0.5,1)
	
	self:RefreshCell(idx)
end

function UI_Market:TickUI(delta)
	--处理倒计时
	local time = MarketModule:getLastTime()
	self.ui.m_txtTime.text = TimeMgr:ConverSecondToString(MarketModule:getLastTime())
end

function UI_Market:UpdateMarketTimeUI()
	self.ui.m_TableViewV:refreshByPos()
	SetActive(self.ui.m_imgRedPoint, true)
end

function UI_Market:CellBtnBuy(arg1, idx, obj)
	AudioMgr:Play(14)
	self.isPlaying = false
	if self.index ~= 1 then
		self.bubble.isAni = true
	end
	
	local marketCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.market, self.marketdata[idx])
	if marketCfg == nil then
		return
	end
	local diamond = NetUpdatePlayerData:GetResourceNumByID(ItemID.DIAMOND)
	local Coin = NetUpdatePlayerData:GetResourceNumByID(ItemID.COIN)
	local price = string.split(marketCfg.buy_type, "|")
	local gem = tonumber(price[2]) - diamond
	local itemId = marketCfg.item_id

	local useCount = tonumber(NetMarketData:getRecordBuyCount(self.index, marketCfg.id))
	NetMarketData:setRecordBuyCount(self.index, marketCfg.id, useCount + 1)
	local function ReloadData()
		-- MarketConfig:FreshRedPoint(self.index)
		MarketConfig:FreshRedPoint_Alltogle(self.toggleTab)
		self:UpdateRedPoint()
		local info = NetUpdatePlayerData:GetPlayerInfo()
		if tonumber(price[1]) == MARKET_BUY_TYPE.GOLD then
			NetUpdatePlayerData:AddResource(PlayerDefine.Coin, -price[2],nil,nil,"shop")
			NetNotification:NotifyConsume(3,itemId,marketCfg.num,ItemID.COIN,price[2])
			self.ui.m_doCoin:DORestart()
			AudioMgr:Play(12)
			AddDOTweenNumberDelay(-tonumber(price[2]), info["coin"], 0.3, 0.8, function(value)
					if self.ui.m_txtCoin then
						self.ui.m_txtCoin.text = math.floor(info["coin"])
					end
				end)
		elseif tonumber(price[1]) == MARKET_BUY_TYPE.DIAMOND then
			NetUpdatePlayerData:AddResource(PlayerDefine.Diamond, -price[2],nil,nil,"shop")
			if self.index == 1 then
				NetNotification:NotifyConsume(5,idx + 1,1,ItemID.DIAMOND,price[2],LangMgr:GetLang(marketCfg.name))
			else
				NetNotification:NotifyConsume(3,itemId,marketCfg.num,ItemID.DIAMOND,price[2])
			end
			self.ui.m_doDiamond:DORestart()
			AudioMgr:Play(10)
			AddDOTweenNumberDelay(-tonumber(price[2]), info["diamond"], 0.3, 0.8, function(value)
					if self.ui.m_txtDiamond then
						self.ui.m_txtDiamond.text = math.floor(info["diamond"])
					end
				end)
		end
		--市场任意购买 --4
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.MARKET_BUY,1)
		local itemid = marketCfg.item_id
		self:BubbleByIndexAnim(idx, itemid, obj , tonumber(marketCfg.num))
		if itemid > 5 or itemid < 1 then
			return
		end
		NetUpdatePlayerData:AddResourceNumByID(itemid, tonumber(marketCfg.num),nil,"shop")
		if itemid == 3 then
			self.ui.m_doCoin:DORestart()
			AudioMgr:Play(10)
			AddDOTweenNumberDelay(tonumber(marketCfg.num), info["coin"], 0.3, 0.8, function(value)
					if self.ui.m_txtCoin then
						self.ui.m_txtCoin.text = math.floor(info["coin"])
					end
				end)
		end
	end

	local function reward()
		SetActive(self.ui.m_imgRedPoint, false)
		self:BubbleByIndexAnim(idx, itemId, obj , tonumber(marketCfg.num))
		--市场任意购买 --4
		--EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.MARKET_BUY,1)
		--刷新红点儿部分
		-- MarketConfig:FreshRedPoint(self.index)
		MarketConfig:FreshRedPoint_Alltogle(self.toggleTab)
		self:UpdateRedPoint()
		SetActive(self.row2, false)
	end

	if tonumber(price[1]) == 3 then
		NetNotification:NotifyNormal(NotifyDefine.FreeBox)

		--买完免费的
		NetMarketData:set_IsFirst(false)
		--市场免费购买
		--Log.Info("领取了 市场里面的 免费宝箱=====")
		EventMgr:Dispatch(EventID.DAILY_TASK,DAY_TEASK_TYPE.MARKET_FREE,1)
		
		--领取成功
		reward()
	else
		if Coin ~= 0 or diamond ~= 0 then
			--直接购买  发奖励
			ReloadData()
		else
			UI_SHOW(UIDefine.UI_BuyGold, gem, itemId, 0, function()
					--购买成功
					self:BuySucc(idx, obj)
				end)
		end
	end
	
	--TimeMgr:CreateTimer(self.timerStr,function()
			--self.ui.m_TableViewH:ReloadData()
		--end,0.5,1)
	
	self:RefreshCell(idx)
end

--item飞到左上角气泡的动画
function UI_Market:BubbleByIndexAnim(idx, itemid, obj , num)
	if self.index == 1 then
		local flyPos = MapController:GetUIPosByWorld(obj.transform.position)
		MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, itemid, num , false)
		return
	end
	--气泡里面显示当前买的(除金币外都显示)
	local Cfg = ConfigMgr:GetDataByID(ConfigDefine.ID.market, self.marketdata[idx])
	self:playPopAnim(obj, Cfg)
end

function UI_Market:playPopAnim(obj, config)
	local img = GET_UI(obj, "ItemIng", TP(UEUI.Image))
	self.bubble:FlyItem(config.item_id,config.num,img.transform.position)
end

function UI_Market:RefreshData()
	UI_SHOW(UIDefine.UI_MarketbuyTip, self.RefreshDiamond, function ()
		self.isPlaying = true
		self:refreshSucc()
	end)
end

function UI_Market:refreshSucc()
	local refreshGem = GlobalConfig:GetNumber(1001)
	local curMapID = NetUpdatePlayerData.playerInfo.curMap
	self.ui.m_doDiamond:DORestart()
	NetUpdatePlayerData:AddResource(PlayerDefine.Diamond, -refreshGem,nil,nil,"shop")
	NetNotification:NotifyConsume(4,0,1,ItemID.DIAMOND,refreshGem,"Refresh")

	local info = NetUpdatePlayerData:GetPlayerInfo()
	AudioMgr:Play(12)
	AddDOTweenNumberDelay(-refreshGem, info["diamond"], 0.3, 0.8, function(value)
			if self.ui.m_txtDiamond then
				self.ui.m_txtDiamond.text = math.floor(info["diamond"])
			end
		end)

	NetMarketData:ClearAll()
	NetMarketData:SetZooRandomCfg()

	self.marketdata = MarketMgr:GetDataWithType(self.index, curMapID)
	--self.marketdata = self:SplitMarketData(self.marketdata)
	--刷新红点儿部分
	-- MarketConfig:FreshRedPoint(self.index)
	MarketConfig:FreshRedPoint_Alltogle(self.toggleTab)
	self:UpdateRedPoint()
	
	local timestamp = GlobalConfig:GetNumber(1002) + TimeMgr:GetServerTime()
	NetMarketData:setRefreshTime(timestamp)
	local time = NetMarketData:getRefreshTime() - TimeMgr:GetServerTime()
	self.ui.m_txtTime.text = TimeMgr:ConverSecondToString(time)

	self.ui.m_TableViewV:refreshByPos()
	--刷新红点儿
	local PointShow = MarketConfig:isHaveRedPoint(curMapID, self.index)
	SetActive(self.ui.m_imgRedPoint, PointShow)
end

function UI_Market:OnRefresh(param)
	if param == 1 then
		self:UpdateMarketTimeUI()
	end
end

function UI_Market:onDestroy()
	UIMgr:RefreshAllMainFace(12,12,{isShow = false,list={PlayerDefine.Diamond,PlayerDefine.Coin}})
	self.index = nil
	self.playerlv = nil
	self.recordBuyCount = nil
	self.isclosing = nil
	self.isPlaying = nil
	self.tIdx = nil
	self.imageList = nil
	self.rewardTab = nil
	self.popListData = nil
	self.isRefresh = nil
	self.RefreshDiamond = nil
	self.row2 = nil
	self.row1 = nil
	self.bubble = nil
	self.marketdata = nil
	self.uiIsInit = false
	self.toggleTab = nil
	EventMgr:Remove(EventID.PAY_SUCCESS,self.OnPaySuccess, self)
	TimeMgr:DestroyTimer(self.timerStr)
end

function UI_Market:isReward()
	local function CloseUI()
		local closeAni = GetComponent(self.uiGameObject, UE.Animation)
		local clip = closeAni:GetClip("ui_market_close")
		local length = clip.length
		closeAni:Play("ui_market_close")
		self:CreateScheduleFun(function() self:Close() end, length, 1)
	end

	if not self.bubble then
		CloseUI()
	end
	
	if self.bubble.isAni then return end
	if self.bubble:IsHaveItem() then
		self.bubble:Close()
	else 
		CloseUI()
	end	
end

function UI_Market:CheckFreeAutoScroll(curMapID,cur_index)
	for key, value in pairs(self.toggleTab) do
		if curMapID ~= MAP_ID_MAIN and curMapID ~= MAP_ID_ZOO and curMapID ~= MAP_ID_SECOND and key == 2 then
			key = 5
		end
		if key == cur_index then
			local PointShow,index = MarketConfig:isHaveRedPoint(curMapID, key)
			if index ~= nil then
				index = Mathf.Ceil(index / 3)
			end
			if PointShow and nil ~= index and index >= 3 then
				--Log.Error("content",self.ui.m_TableViewV_Scroll.content,-384*(self.index+1))
				--self.ui.m_TableViewH:scrollByItemIndex(index)
				--Log.Error("index",index,-384*(index-1))
				local targetHeight = 516*(index-1)
				local contentHeight = self.ui.m_TableViewV_Scroll.content.rect.height
				local viewportHeight = self.ui.m_TableViewV_Scroll.viewport.rect.height
				if contentHeight - viewportHeight < targetHeight then
					targetHeight = contentHeight - viewportHeight
				end
				local targetValue = Vector2.New(self.ui.m_TableViewV_Scroll.content.anchoredPosition.x,targetHeight)
				self.ui.m_TableViewV_Scroll.velocity = Vector2.zero
				DOAnchorPos(self.ui.m_TableViewV_Scroll.content,targetValue,0.5,nil,Ease.InOutSine)
			end
		end
	end
end

function UI_Market:ZooFreeAutoScroll(cur_index)
	if nil ~= cur_index then
		DOLocalMoveX(self.ui.m_TableViewV_Scroll.content,-384*(cur_index-1),0.5,nil,Ease.InOutSine)
	end
end

function UI_Market:onUIEventClick(go,param, islight)
	if self.isclosing == true then
		return
	end

	local name = go.name
	if name == "m_btnRefresh" then
		self:RefreshData()
	elseif name == "btn_close" or name == "m_btn_pop" or name == "btn_mask" then
		self:isReward()
	elseif name == "m_goDiamBuy" then
		UI_SHOW(UIDefine.UI_ActCenter,13)
	elseif string.find(name, "m_tog") > 0 then
		SetActive(self.row2, false)
		
		local index = tonumber(string.match(name, "m_tog(%d+)"))
		if index == self.index then
			if param then
				self:CheckFreeAutoScroll(NetUpdatePlayerData.playerInfo.curMap, self.index);
			end
			return
		end
		self.index = index
		self.tIdx = 0
		if go.isOn == true and self.isRefresh then
			self.isPlaying = true
			--AudioMgr:Play(5)

			local curMapID = NetUpdatePlayerData.playerInfo.curMap
			self.marketdata = MarketMgr:GetDataWithType(self.index, curMapID)
			--self.marketdata = self:SplitMarketData(self.marketdata)
			self.ui.m_TableViewV:refreshByPos()
			self:UpdateRedPoint()
			if self.index == 4 then
				SetActive(self.row1, false)
			else
				SetActive(self.row2, false)
			end
			self:CheckFreeAutoScroll(curMapID,self.index)
		end

		if go.isOn == true then
			self.isRefresh = true
		end	
	end
end

function UI_Market:onEscape()
	self:isReward()
end


return UI_Market