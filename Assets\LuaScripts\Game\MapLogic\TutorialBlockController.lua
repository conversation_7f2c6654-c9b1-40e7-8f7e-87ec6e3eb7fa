---



---

local TutorialBlockController = Class()

function TutorialBlockController:ctor()
    self.m_IsInit = false
    self.m_Go = nil
    self.m_Trans = nil
    self.m_ListGrid = nil

    self.m_cacheItemLayer = nil
    self.m_isSetOrder = nil
end

function TutorialBlockController:Init()
    local function onLoadTutorialBlock(prefab)
        local go, trans = CreateGOAndTrans(prefab)
        self.m_Go = go
        self.m_Trans = trans
        self.m_IsInit = true
        self.m_Mono = trans:GetComponent("TutorialBlock")

        trans:SetParent(MapController.m_TransFly)

        if self.targetLayer and self.targetOrder then
            self:SetLayer(self.targetLayer,self.targetOrder)
        end

        self:DoGrids()
    end
    ResMgr:LoadAssetBaseAsync("Assets/ResPackage/Prefab/Map/TutorialBlock.prefab", AssetDefine.LoadType.Instant, onLoadTutorialBlock)
end

function TutorialBlockController:Hide()
end

function TutorialBlockController:SetLayer(layer,order)
    if self.m_Go then
        SetRendererOrder(GetComponent(self.m_Go, UE.Renderer), layer, order)
        self.targetLayer = nil
        self.targetOrder = nil
    else
        self.targetLayer = layer
        self.targetOrder = order
    end
end

function TutorialBlockController:SetGrids(typeData, cPos , isSetOrder)
    local strType = type(typeData)
    self.m_isSetOrder = isSetOrder

    if not cPos then
        cPos = Vector3.New(MapController.m_CameraTrans:GetLocalPosition())
    end
    self.m_CPos = cPos

    if strType == "nil" then
        self:ResetOrder()
        SetActive(self.m_Go, false)
		MapController:ShowAllFlyItem()
        return
    elseif strType == "number" then
        self.m_ListGrid = nil
        if isSetOrder then
            MapController:HideAllFlyItem()
        end
    elseif strType == "table" then
        if isSetOrder then
            MapController:HideAllFlyItem()
        end
        local listGrids = {}
        for k, v in pairs(typeData) do
            if v.id then
                local gx, gy = GuideController:GetCacheItemGrid(v.id)
				if gx == nil then 
					local listOut = {}
					local tempItem = MapController:GetItemById(v.id,10,listOut)
					if tempItem and #tempItem ==1 then
						gx, gy = tempItem[1]:GetGrid()
					elseif #tempItem > 1 then
						for i, v in ipairs(tempItem) do
							gx, gy = v:GetGrid()
							local x, y, z = GetPosByGrid(gx, gy)
							local ox = x - cPos.x
							local oy = y - cPos.y
							local vtPos = MapController:GetUIPosByWorld(Vector3.New(ox, oy, 0), 6)
							table.insert(listGrids, { GetGridID(gx, gy), 1, vtPos.x, vtPos.y })
						end
						break
					end
				end
                v.GridX = gx or 0 
                v.GridY = gy or 0
            end
            local gridX = v.GridX
            local gridY = v.GridY
            local x, y, z = GetPosByGrid(gridX, gridY)
            local ox = x - cPos.x
            local oy = y - cPos.y
            local vtPos = MapController:GetUIPosByWorld(Vector3.New(ox, oy, 0), 11)
            table.insert(listGrids, { GetGridID(gridX, gridY), 1, vtPos.x, vtPos.y })
        end
        self.m_ListGrid = listGrids
    end
    if self.m_IsInit then
        self:DoGrids()
    else
        self:Init()
    end
end

function TutorialBlockController:DoGrids()
    SetActive(self.m_Go, true)
    self.m_Mono:SetGrids(self.m_ListGrid)
    self.m_Trans:SetLocalPosition(self.m_CPos.x, self.m_CPos.y, 0)

    if self.m_isSetOrder then
        self:SetOrder(self.m_ListGrid)
    end
end

--提层物品
function TutorialBlockController:SetOrder(listGrid)

    self:ResetOrder()

    self.m_cacheItemLayer = {}

    local colorComp = MapController:GetComponent(MapComponentId.ColorfulItemComponent,true)

    for index, value in ipairs(listGrid) do
        local gridX,gridY = GetGridByID(value[1])
        local item =  MapController:GetItemByGridUnsafe(gridX,gridY)

        if item then
            item:setOrderLayer(SortingLayerInGame.UI_TOP,600+1)
            if item.onHighLight then item:onHighLight(true) end

            if colorComp then
                colorComp:ColorfulItem(item,true)
            end

            local data = {}
            data.item = item
            data.orderLayerId = item.m_OrderLayerId
            data.orderIdx = item.m_OrderIdx
            table.insert(self.m_cacheItemLayer, data)
        end
    end
end

function TutorialBlockController:ResetOrder()
    if self.m_cacheItemLayer then
        local colorComp = MapController:GetComponent(MapComponentId.ColorfulItemComponent,true)

        for index, value in ipairs(self.m_cacheItemLayer) do
            if value.item then
                value.item:setOrderLayer(value.orderLayerId,value.orderIdx)
                if value.item.onHighLight then value.item:onHighLight(false) end

                if colorComp then
                    colorComp:CancelColorfulItem(value.item)
                end
            end
        end
        self.m_cacheItemLayer = nil
    end
end

function TutorialBlockController:IsInList(gridX, gridY)
	local gId = GetGridID(gridX, gridY)
	if self.m_ListGrid then
		for index, value in ipairs(self.m_ListGrid) do
			if gId ==  value[1] then
				return true
			end
		end
	end
	return false
end

function TutorialBlockController:Clear()

end

return TutorialBlockController