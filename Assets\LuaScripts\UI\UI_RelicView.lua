local UI_RelicView = Class(BaseView)
local TopUpdateTime = 5
local IdleSpineTime = 5
local Camera_Siz = 2
local Camera_Time = 0.8
local RankBox = require("UI.RankBox")
local Timer
local GoGiftItem = require("UI.GoGiftItem")

--local LEVEL1_MAT_ASSET = "Assets/ResPackage/Spine/pharaoh_104/shibei_Material.mat"
function UI_RelicView:OnInit()
    
end

function UI_RelicView:OnCreate(ispush)
	self.doorItemList = {}
	self.nodexItemList = {}
	self.robotItemList = {}
	self.airIndex = 1
    self.playerRank = 1  -- 玩家排名
	self.choose_bool = false
    NetRelicData.isNewRound = false
	self.isPush = ispush
    self.autoPlay = false  -- 自动操作的开关标识
	self.auto_playing = false
	self.activityItem = LimitActivityController:GetActive(ActivityTotal.Relic)
	self.activeId = self.activityItem.info.activeId
	self.nowScore = v2n(self.activityItem.info.integral)
	self.index,self.round = NetRelicData:GetIndexAndRount()
	self.maxRound = RelicManager:GetMaxRound()
	self.time = 0
	self.spineTime = 0
	--self.playingSpine = false
	self.canOpenDoor = true
    self.canShowRankBox = true  -- 能否显示排名宝箱奖励列表
    self.canContinueRankReward = true  -- 能否点击继续宝箱奖励动画
	--ResMgr:LoadAssetWithCache(LEVEL1_MAT_ASSET, AssetDefine.LoadType.Instant, function (mat)
			--Log.Error("1",mat)
		--self.level1_mat = mat
	--end)
	self:SetIsUpdateTick(true)
	self:CreateScheduleFun(function() self:Timer() end,1)	
	self:InitRobotList()
	local function InitEnd()
		self:UpdateDoor()
		self:UpdatePanel()
		self:AddBtnDoor()	
		self:UpdateRobot(nil,true)
		self:TurnPageScroll()

		if self.isPush then
			self:Guide()
		end
	end
	self:InitDoorData(InitEnd)

	--self:SortOrderAllCom(true)

	--直购礼包入口
	local function callBack(obj)
		self.directBuyGo = obj
		self.directBuyGo:SetInteractable(true)
	end
	GoGiftItem:Create(self.ui.m_transDirectBuy,ActivityTotal.Relic,self.ui.m_transRelicNumParent,callBack)
end

function UI_RelicView:Guide()
	local isGuide = not NetGlobalData:GetActivityGuideCache(ActivityTotal.Relic)
	if not isGuide then
		self:BeginRobotMatch()
		return		
	end
	NetGlobalData:SetActivityGuideCache(ActivityTotal.Relic)
	local function guide4()
        -- 排名宝箱的节点位置
        local centerPos = self:ConvertToRectPos(self.ui.m_transRankBox)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{2,5,200})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{1,0,8922})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 + 3, 0, 0, 0})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
				UI_CLOSE(UIDefine.UI_GuideMask)
                -- 新手引导之后显示匹配界面
                self:BeginRobotMatch()
			end)
	end	
	local function guide3()
        -- 机器人视图的节点位置
        local centerPos = self:ConvertToRectPos(self.ui.m_transRobot)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,1200,400})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-1.5,1.55,8920})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 + 3, 0, 0, 0})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
				guide4()
			end)
	end
	local function guide2()
        if not self.ui then
            UI_CLOSE(UIDefine.UI_GuideMask)
            return
        end
        -- 通关奖励的节点位置
        local centerPos = self:ConvertToRectPos(self.ui.m_transClearReward)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,1000,200})	
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-1.5,1,8920})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
				guide3()
			end)
	end
	local function guide1()
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter,{0,60})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,1500,620})		
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-1.5,-4,8910})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{0,4.5,0,0,0})	
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
				guide2()
		end)
	end
    -- 放大镜的节点位置
    local centerPos = self:ConvertToRectPos(self.ui.m_transRelicNumParent)
	UI_SHOW(UIDefine.UI_GuideMask, {
			{3,260,130},
            centerPos,
			{2,2},
			0.5,
			function ()
				guide1()
			end,
            {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},
			{-1.5,0,8902},
			"Sprite/new_hero/headFrame_1.png"
		})
end

--- 转换 UI 坐标
--- @param go any UI 节点
--- @return table 坐标表
function UI_RelicView:ConvertToRectPos(go)
    local cam = UIMgr:GetCamera()
    local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform,
        Vector2.New(screenPoint.x, screenPoint.y), cam)
    local posTable = {pos.x, pos.y}
    return posTable
end

function UI_RelicView:UpdatePanel()
	local function _updatePanel()
		self.ui.m_txtPlayAuto.text = LangMgr:GetLang(9317)
		self.ui.m_txtPlayAutoTip.text = LangMgr:GetLang(9322)
		self.ui.m_txtRelicNum.text = NetRelicData:GetRelicNum()
		self.ui.m_txtTitle.text = LangMgr:GetLang(8905)
		local maxIndex = RelicManager:GetMaxIndexByRound(self.round)
		self.ui.m_txtUnderDes.text = LangMgr:GetLangFormat(8906,self.index,maxIndex)

		local currentRoundCount = NetRelicData:GetCurrentRoundCount()
		-- 第 8 关及后续关卡，更换文本样式
		if currentRoundCount > 7 then
			self.ui.m_txtRankCount.text = LangMgr:GetLangFormat(8926, currentRoundCount)
		else
			self.ui.m_txtRankCount.text = LangMgr:GetLangFormat(8925, self.round, self.maxRound)
		end

		local energySch         = self:GetNowEnergySch()
		local nextScore = self.activityItem.form.exps[energySch]
		--local nowMaxScore = v2n(self.activityItem.form.exps[energySch])
		self.nowScore = v2n(self.activityItem.info.integral)
		self.ui.m_txtPassPort.text = LangMgr:GetLangFormat(8925,self.nowScore,nextScore)
		self.ui.m_slider3.value = self.nowScore/nextScore

		--节点奖励
		self.ui.m_txtRankTxt1.text = LangMgr:GetLang(8907)
		local clear_reward = RelicManager:GetStageConfigByIAndR(self.index,self.round).clear_reward
		local clear_mine = RelicManager:GetStageConfigByIAndR(self.index,self.round).clear_mine
		clear_mine = string.split(clear_mine,";")
		local rewardStr = clear_reward..";"..clear_mine[1]--策划潜规则只显第一个配置的奖励
		self:InitTopItem(self.ui.m_goRankList1,rewardStr,1)
		--章节奖励
		self.ui.m_txtRankTxt2.text = LangMgr:GetLang(8908)
		local rewardStr = RelicManager:GetClearRewardStr(self.round)
		self:InitTopItem(self.ui.m_goRankList2,rewardStr)
		SetActive(self.ui.m_goPassPortRed,NetRelicData:IsShowRelicRed())

		--金字塔状态
		for k, v in pairs(self.doorItemList) do
			local state = NetRelicData:GetDoorState(k)
			local itemImg = NetRelicData:GetDoorReward(k)
			local a = v.pre_spine
			if state == ITEM_STATE.REWARED then
				SetSpineAnim(v.pre_spine,"daiji_3",-1)

				if itemImg and itemImg > 0 then
					SetUIImage(v.itemImg,ItemConfig:GetIcon(itemImg),false)
					SetActive(v.itemImg.transform.parent, true)
					--v.itemImg.gameObject.transform:SetLocalScale(0,0,0)

					--DOScale(v.itemImg.gameObject.transform,1.2,0.5,function ()
					--DOScale(v.itemImg.gameObject.transform,1.0,0.5,function()

					--end)
					--end)
				else
					SetActive(v.itemImg.transform.parent, false)
				end

			else
				SetActive(v.itemImg.transform.parent, false)
				SetSpineAnim(v.pre_spine,"daiji_1",-1)
				--v.pre_spine:Initialize(true)
			end
		end
	end
	if self.choose_bool then
		function initeEnd()	
			_updatePanel()
			self:UpdateDoor()
		end
		self:InitDoorData(initeEnd)
	else
		_updatePanel()
	end
end

function UI_RelicView:InitTopItem(obj,rewardStr,type)
	local arr = string.split(rewardStr,";")
    local itemObjList = {}
	local itemObj
	for i = 1, 5 do
		itemObj = GetChild(obj,"goRankReward" .. i)
        itemObjList[i] = itemObj
		SetActive(itemObj,i <= table.count(arr))
	end

	for i, v in ipairs(arr) do		
		if itemObj then
			if i > table.count(arr) then
				--SetActive(obj,false)
			else
				--SetActive(obj,true)
				local arr2 = string.split(v,"|")
				local itemId = arr2[1]
                -- 季节活动宝箱转换
                itemId = v2n(NetSeasonActivity:GetChangeItemId(itemId))
				local num = v2n(arr2[2])
				if type == 1 and num >= 100 then
					num = 1
				end
                local itemImg = GetChild(itemObjList[i], "rewardIcon", UEUI.Image)
                local txtNum = GetChild(itemObjList[i], "rewardTxt", UEUI.Text)
                local button = GetChild(itemObjList[i], "rewardIcon", UEUI.Button)
                if button then
                    button.onClick:AddListener(function()
                        UI_SHOW(UIDefine.UI_ItemTips, itemId)
                    end)
                end
				SetUIImage(itemImg, ItemConfig:GetIcon(itemId), false)
				txtNum.text = "x"..num
			end
		end
	end
end

function UI_RelicView:InitDoorData(callBack)
	self.doorItemList = {}
	local stageConfig = RelicManager:GetStageConfigByIAndR(self.index,self.round)
	local index = 1
	for i = 1, 5 do
		index = i
		local t = {}
		t.obj = self.ui["m_goDoorObj"..i]
		t.obj_catch = GetChild(self.ui["m_goDoorObj"..i],"fl_catch")
		t.sp_catch = GetChild(self.ui["m_goDoorObj"..i],"fl_catch/spine",CS.Spine.Unity.SkeletonGraphic)
		t.obj_idle = GetChild(self.ui["m_goDoorObj"..i],"fl_idle")
		t.sp_idle = GetChild(self.ui["m_goDoorObj"..i],"fl_idle/spine",CS.Spine.Unity.SkeletonGraphic)
		t.pre_spine = GetChild(self.ui["m_goDoorObj"..i],"spine",CS.Spine.Unity.SkeletonGraphic)
        t.itemImg = GetChild(self.ui["m_goDoorObj"..i], "itemImgParent/itemImg", UEUI.Image)
		if stageConfig.scale then
			local arr = string.split(stageConfig.scale,"|")
			self.ui["m_goDoorObj"..i].transform.localScale = Vector3(v2n(arr[1]),v2n(arr[2]),v2n(arr[3]))
		end
		local function funcW(asset)
			t.pre_spine.skeletonDataAsset = asset
			t.pre_spine:Initialize(true)
			table.insert(self.doorItemList,t)
			if index == 5 and callBack then
				callBack()
			end
		end
		local function funcW2(mat)
			t.pre_spine.material = mat
			t.pre_spine:Initialize(true)
		end
		if t.pre_spine then
			ResMgr:LoadAssetAsync(stageConfig.mat,AssetDefine.LoadType.Instant,funcW2)
			ResMgr:LoadAssetAsync(stageConfig.stage_prefab,AssetDefine.LoadType.Instant,funcW)
		end
	end
end

--- 播放法老王动画
function UI_RelicView:FlAppearSp()
	SetActive(self.ui.m_goFL,true)
	SetSpineAnim(self.ui.m_spuiFl,"xiuxian",1)
	SetSpineAnim(self.ui.m_spuiFl, "idle", 1, true)
	local function appearEnd()
		--SetSpineAnim(self.ui.m_spuiFl,"idle",1)
		SetActive(self.ui.m_goFL,false)
		self.canOpenDoor = true
        -- 法老王动画结束后，玩家头像跳跃
        if self.choose_bool then
            self:UpdateRobot()
        end
	end
	if self.ui.m_spuiFl.AnimationState then
		SetSpineCallBack(self.ui.m_spuiFl,SPINE_CALLBACK_TYPE.Complete,appearEnd)
	end
end

function UI_RelicView:UpdateDoor()
	--------door-------------
	local stageConfig = RelicManager:GetStageConfigByIAndR()
	self.doorNum = stageConfig.door
	local pos = RelicManager:GetDoorPostionByNum(self.doorNum)
	for k, v in pairs(self.doorItemList) do
		SetActive(v.obj,self.doorNum >= k)
		if self.doorNum >= k and pos[k] then
			local doorPos = string.split(pos[k],"|")
			local posX = v2n(doorPos[1])
			local posY = 	v2n(doorPos[2])
			v.obj.transform.localPosition = Vector3(posX,posY,0)						
		end
	end

    self:ChangeBackground(stageConfig.background, stageConfig.stone)

	------------scrollItem-----------
	local roundConfig = RelicManager:GetStageConfigByRound(self.round)
	local nowNum = table.count(self.nodexItemList)
	
	for k, v in pairs(self.nodexItemList) do
		SetActive(v.go,false)
	end
	local function UpdateNodex(obj,k)
		SetActive(obj.go,true)
		SetActive(obj.line,k ~= table.count(roundConfig))
		
		if k > self.index then			
			SetUIImage(obj.bgImg,"Sprite/ui_activity_yijikaogu/yijikaogu_win_5_guanka2.png",true)
			obj.txt.text = string.format("<color=#292C29>%s</color>", k)
		elseif k == self.index then
			SetUIImage(obj.bgImg,"Sprite/ui_activity_yijikaogu/yijikaogu_win_5_guanka1.png",true)
			obj.txt.text = string.format("<color=#292C29>%s</color>", k)
		elseif k < self.index then
			SetUIImage(obj.bgImg,"Sprite/ui_activity_yijikaogu/yijikaogu_win_5_guanka3.png",true)
			obj.txt.text = string.format("<color=#7B3100>%s</color>", k)
		end
		if k == table.count(roundConfig) then
			obj.txt.text = string.format("<color=#AE2F00>%s</color>", k)
		end
	end
	for k, v in pairs(roundConfig) do
		if k > nowNum then
			--Clone
			local pre
			if k%2 == 0 then
				pre = self.ui.m_goNodeTwoBox.gameObject			
			else
				pre = self.ui.m_goNodeOneBox.gameObject
			end
			local go = UEGO.Instantiate(pre,self.ui.m_goItemParent.transform)
			if go then
				local t = {}
				t.go = go
				t.line = GetChild(go,"obj/line" ,UEUI.Image)
				t.txt = GetChild(go,"obj/text" ,UEUI.Text)
				t.bgImg = GetChild(go,"obj/bg",UEUI.Image)
				t.robotPos = GetChild(go,"robotPos")
				table.insert(self.nodexItemList,t)
				UpdateNodex(t,k)
			end
		else
			UpdateNodex(self.nodexItemList[k],k)					
		end
	end
	-------head-------
end

function UI_RelicView:InitRobotList()
	self.robotItemList = {}
	for i = 1, 5 do
		local robotItem = GetChild(self.ui.m_goPlayerJump,"player"..i)
		local headNode = GetChild(robotItem, "GameObject/headNode")
		CreateCommonHead(headNode.transform,0.55)
		local headNodeRect = GetComponent(headNode,UE.RectTransform)
		headNodeRect.anchoredPosition = Vector2.New(0,17)
		table.insert(self.robotItemList,robotItem)
	end
	NetRelicData:UpdateRobotData()
end

function UI_RelicView:UpdateRobot(jumpBack,init)
	self.robotData = NetRelicData:GetRobotData()
	--for k, v in pairs(self.robotItemList) do
		--local a
	--end
	local yPos = {}
	for k, v in pairs(self.robotData) do
        local node
        -- 玩家
        if k == 1 then
            node = self.index
        -- 机器人
        else
            node = v.NodeIndex
        end
		if node < 1 then
			node = 1
		end
		if nil == yPos[node] then
			yPos[node] = 1
		else
			yPos[node] = yPos[node] + 1
		end
		local robot = self.robotItemList[k]
		local pos = self.nodexItemList[node].robotPos.transform
		local imgHead = GetChild(self.robotItemList[k], "GameObject/imgHead",UEUI.Image)
        local customHeadObj = GetChild(robot, "GameObject/headNode/CustomHead")
		
        -- 玩家
        if k == 1 then
            if not init then
                -- 回到第一个节点时不播放跳跃动画
                if node ~= 1 then
                    self:PlayJumpAnimation(pos, yPos[node] * 10, jumpBack, true)
                end
            else
                SetParent(robot, pos)
                -- 设置节点层级顺序
                robot.transform:SetSiblingIndex(yPos[node] - 1)
                robot.transform.localPosition = Vector3(0, yPos[node] * 10, 0)
            end
			SetMyHeadAndBorderByGo(customHeadObj)
        -- 机器人
        else
            -- 机器人已经到达终点
            if v.arrive ~= nil and v.arrive <= 3 then
                local endPos = self.ui["m_goRank"..v.arrive]
                SetParent(robot, endPos)
                robot.transform.localPosition = Vector3(0, 30, 0)
            else
                SetParent(robot, pos)
                -- 设置节点层级顺序
                robot.transform:SetSiblingIndex(yPos[node] - 1)
                robot.transform.localPosition = Vector3(0, yPos[node] * 10, 0)
            end
			SetHeadAndBorderByGo(customHeadObj,v.head,v.border)
		end
		--local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, v.head)
		--SetUIImage(imgHead, headConfig.icon, false)
		
		SetActive(imgHead,false)
	end
end

function UI_RelicView:PlayJumpAnimation(pos, yPos, callBack, needScroll)
	--local Go = target
	SetParent(self.robotItemList[1],pos)
	DOLocalJump(self.robotItemList[1].transform, Vector3(0,yPos,0),-10,1,0.5, function()
		if callBack then
			callBack()
		end
        -- 机器人视图需要滚动
        if needScroll then
            self:TurnPageScroll()
        end
	end)
end

--region =========================== 自动操作 ===========================

--- 开启自动操作
function UI_RelicView:OpenAutoPlay()
    local relicNum = NetRelicData:GetRelicNum()
    if relicNum > 0 then
        self.autoPlay = not self.autoPlay
        SetActive(self.ui.m_goAutoPlaySpine, self.autoPlay)
        SetActive(self.ui.m_goGiftMask, self.autoPlay)
        SetActive(self.ui.m_goAutoPlayNormal, not self.autoPlay)
        SetActive(self.ui.m_txtPlayAutoTip, self.autoPlay)
        local langID = self.autoPlay and 9321 or 9317
        self.ui.m_txtPlayAuto.text = LangMgr:GetLang(langID)
        self.ui.m_txtPlayAutoTip.text = LangMgr:GetLang(9322)
    else
        self.autoPlay = false
        SetActive(self.ui.m_goAutoPlaySpine, self.autoPlay)
        SetActive(self.ui.m_goGiftMask, self.autoPlay)
        SetActive(self.ui.m_goAutoPlayNormal, not self.autoPlay)
        SetActive(self.ui.m_txtPlayAutoTip, self.autoPlay)
        self.ui.m_txtPlayAuto.text = LangMgr:GetLang(9317)
        self.ui.m_txtPlayAutoTip.text = LangMgr:GetLang(9322)
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8902))
    end
end

--- 每秒调用一次的计时器
function UI_RelicView:Timer()
	if self.time >= TopUpdateTime then
		self:PalyTopAni()
		self.time = 0
	else
		self.time = self.time + 1
	end

	if self.spineTime >= IdleSpineTime and self.canOpenDoor then 	--and not self.playingSpine
		self:PlayIdleSpine()
		self.spineTime = 0
		--Log.Error("play")
		--self.playingSpine = true
	else
		self.spineTime = self.spineTime + 1
	end
    -- 开启了自动操作，也可以打开金字塔
	if self.autoPlay and self.canOpenDoor then
		self:AutoPlay()
	end
end

--- 自动玩
function UI_RelicView:AutoPlay()
	if self.auto_playing then
		TimeMgr:CreateTimer(UIDefine.UI_RelicView,function ()
			self.auto_playing = false
			end,1,1)
		return
	end
	local door_index 
	local can_open_door = {}
	for k, v in pairs(self.doorItemList) do
		local state = NetRelicData:GetDoorState(k)
		if state == ITEM_STATE.CAN_REWARD and v.obj.activeSelf then
			table.insert(can_open_door,k)
		end
	end
	local tableNum = table.count(can_open_door)
	local door_index = math.random(1,tableNum)
	self:OpenDoor(can_open_door[door_index])
end

--endregion =========================== 自动操作 ===========================

function UI_RelicView:OpenDoor(door_index)
	if not self.canOpenDoor then
		return
	end
	local state = NetRelicData:GetDoorState(door_index)

	if state == ITEM_STATE.REWARED then
		if not self.autoPlay then
			UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8904))
		end
		return
	end
	--Log.Error("get",door_index,state)
	local times = NetRelicData:GetCurOpenDoor()
	--Log.Error("open",door_index,"times",times)

    local relicNum = NetRelicData:GetRelicNum()
    -- 放大镜数量不足
    if relicNum < 1 then
        -- 正在自动操作
        if self.autoPlay then
            -- 关闭自动操作
            self.autoPlay = false
            SetActive(self.ui.m_goAutoPlaySpine, self.autoPlay)
            SetActive(self.ui.m_goGiftMask, self.autoPlay)
            SetActive(self.ui.m_goAutoPlayNormal, not self.autoPlay)
            SetActive(self.ui.m_txtPlayAutoTip, self.autoPlay)
            self.ui.m_txtPlayAuto.text = LangMgr:GetLang(9317)
            self.ui.m_txtPlayAutoTip.text = LangMgr:GetLang(9322)
        end
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8902))
        return
    end

	if self.autoPlay then
		self.auto_playing = true
	end
	-------------------data-----------------
	self.old_index,self.old_round = self.index,self.round
	NetRelicData:AddRelicNum(-1)
	NetRelicData:SetDoorState(door_index,ITEM_STATE.REWARED)
	self.canOpenDoor = false
	self.choose_bool = RelicManager:GetOpenDoorDdds() 
	local choose_reward
	local remainsLv_reward = ""
	local isNext = 0
	local normalReward = RelicManager:GetNoralReward() --addRewarded
	if normalReward > 0 then
		NetRelicData:SetDoorReward(door_index,normalReward)
		remainsLv_reward = remainsLv_reward..normalReward.."|1;"
	end
	-- 打开通关门
	if self.choose_bool then
		choose_reward = RelicManager:GetClearReward() --addRewarded
		remainsLv_reward = remainsLv_reward..choose_reward..";"
		-- 提前获取玩家排名，后续排名数据会被重置
		self.playerRank = NetRelicData:GetRankValue()
		NetRelicData:AddNodeCount(1) --下一关清除数据
	end
	if not self.choose_bool then
		NetRelicData:AddCurOpenDoor(1)
	end
	if self.choose_bool and NetRelicData:IsNewRound() then
		isNext = 1
		local zj_reward,rank_reward = RelicManager:GetRoundCountReward(self.round,self.playerRank)
		--章节奖励
		if zj_reward then
			remainsLv_reward = remainsLv_reward..zj_reward..";"
		end
		--排行奖励
		if rank_reward then
			remainsLv_reward = remainsLv_reward..rank_reward..";"
		end
	end
	------------------------------------
	
	-----------------ui------------------
    -- 隐藏法老王 idle 动画
	self.directBuyGo:SetInteractable(false)
	
    for _, v in pairs(self.doorItemList) do
        SetActive(v.obj_idle, false)
    end
	self.index,self.round = NetRelicData:GetIndexAndRount()
	local function openEnd()
		Timer = TimeMgr:CreateTimer(UIDefine.UI_RelicView,function ()
				if self.choose_bool then
					self:NextNodexAni(choose_reward,normalReward,door_index)
				else
					self:NorMalAni(normalReward,door_index)
				end
			end,1,1)			
	end
	
	remainsLv_reward = string.sub(remainsLv_reward,1,string.len(remainsLv_reward) - 1)
	local thinkTable = {
		["remainsLv_chapter"] = self.old_round,
		["remainsLv_levels"] = self.old_index,
		["remainsLv_get"] = (self.choose_bool and "pass") or (normalReward > 0 and "reward") or "no",
		["remainsLv_key"] = NetRelicData:GetRelicNum(),	
		["remainsLv_reward"] = remainsLv_reward,
		["remainsLv_isend"] = isNext,
	}
	SdkHelper:ThinkingTrackEvent(ThinkingKey.remainsLv,thinkTable)
	
	
	SetSpineCallBack(self.doorItemList[door_index].pre_spine,SPINE_CALLBACK_TYPE.Start,openEnd)
	SetSpineAnim(self.doorItemList[door_index].pre_spine,"daiji_2",1)
	-----------------------------------
end

function UI_RelicView:NorMalAni(normalReward,door_index)
	local function firstEnd()
		self:CheckIsNextRound()
	end
	--数量固定1 无配置
	if normalReward > 0 then
		self:BubbleByIndexAnim(normalReward,1,door_index,firstEnd)
	else
		firstEnd()
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8903))
	end
    self:FlyScore(door_index, function ()
        if not self.ui then return end
        self.ui.m_goPassPortContent.transform:DOComplete()
        self.ui.m_goPassPortContent.transform:DOPunchScale(Vector3(0.2, 0.2, 1), 0.3)
        EffectConfig:CreateEffect(167, 0, 0, 0, self.ui.m_goPassPortContent.transform)
    end)
end

function UI_RelicView:PlayIdleSpine()
	local can_open_door = {}
	for k, v in pairs(self.doorItemList) do
		local state = NetRelicData:GetDoorState(k)
		if state == ITEM_STATE.CAN_REWARD then
			table.insert(can_open_door,v)
		end
	end
	--clear
	for k, v in pairs(self.doorItemList) do
		SetActive(v.obj_idle,false)
	end
	local tableNum = table.count(can_open_door)
	local random = math.random(1,tableNum)

	for k, v in pairs(can_open_door) do
		if random == k then
			local stageConfig = RelicManager:GetStageConfigByIAndR(self.index,self.round)
			local num = math.random(1,100)
			local random2 = (num%2 == 0 and 4) or 3
			if stageConfig.prefab_position then
				if random2 == 4 then --right
					local arr = string.split(stageConfig.prefab_position,";")
					local posStr = arr[2]
					arr = string.split(posStr,"|")
					local xPos = v2n(arr[1])
					local yPos = v2n(arr[2])
					v.obj_idle.transform.localPosition = Vector3(xPos,yPos,0)
				else
					local arr = string.split(stageConfig.prefab_position,";")
					local posStr = arr[1]
					arr = string.split(posStr,"|")
					local xPos = v2n(arr[1])
					local yPos = v2n(arr[2])
					v.obj_idle.transform.localPosition = Vector3(xPos,yPos,0)
				end
			end
			
			SetActive(v.obj_idle,true)
			
			local spineName = "idle_"..random2
			--Log.Error("xxx",num,spineName,k)
			SetSpineAnim(v.sp_idle,spineName,1)
			local function spineEnd()
				self.spineTime = 0
				SetActive(v.obj_idle,false)
				v.sp_idle:Initialize(true)
			end
			--不知道为什么会空，这次没有播idle动画,下一个五秒播
			if v.sp_idle.AnimationState then
				SetSpineCallBack(v.sp_idle,SPINE_CALLBACK_TYPE.Complete,spineEnd)
			else
				--self.playingSpine = false
			end
		end
	end
end

function UI_RelicView:ChooseFlSpine(door_index,callBack)
		
	if self.doorItemList[door_index] then
		local door = self.doorItemList[door_index]
		SetActive(door.obj_idle,false)
		SetActive(door.obj_catch,true)
		SetSpineAnim(door.sp_catch,"idle_5",1)
		door.sp_catch:Initialize(true)
		--Log.Error("catch")
		local function catchEnd()
			SetActive(door.obj_catch,false)
			if callBack then
				callBack()
			end
		end
		SetSpineCallBack(door.sp_catch,SPINE_CALLBACK_TYPE.Complete,catchEnd)
	else
		if callBack then
			callBack()
		end
	end
end

function UI_RelicView:NextNodexAni(choose_reward,normalReward,door_index)	
	local function firstEnd()
		self:CheckIsNextRound(choose_reward)
	end
	local function callFunc()
        if not self.ui then return end
		--普通奖励
		if normalReward > 0 then
			self:BubbleByIndexAnim(normalReward,1,door_index)
		end
		--选中奖励
		local arr = string.split(choose_reward,";")
		for k, v in pairs(arr) do
			local arr1 = string.split(v,"|")
			local itemId = v2n(arr1[1])
			local num = v2n(arr1[2])
			if k == table.count(arr) then
				self:BubbleByIndexAnim(itemId,num,door_index,firstEnd)
			else
				self:BubbleByIndexAnim(itemId,num,door_index,nil)
			end			
		end
        self:FlyScore(door_index, function ()
            if not self.ui then return end
            self.ui.m_goPassPortContent.transform:DOComplete()
            self.ui.m_goPassPortContent.transform:DOPunchScale(Vector3(0.2, 0.2, 1), 0.3)
            EffectConfig:CreateEffect(167, 0, 0, 0, self.ui.m_goPassPortContent.transform)
        end)
	end
	local param = {}
	param.type = 13
	param.rewards = choose_reward  --111|2;3552|2
	local function showRechar()
        -- 自动操作时，直接获得奖励，飞入奖励云
		if self.autoPlay then
			callFunc()
        -- 手动操作时，会弹出奖励界面
		else
			UI_SHOW(UIDefine.UI_Recharge, param, callFunc, false)
		end
	end
	self:ChooseFlSpine(door_index,showRechar)
end

function UI_RelicView:IsShowRobotHead(value)
	for k, v in pairs(self.robotItemList) do
		SetActive(v,value or false)
	end
end

function UI_RelicView:CheckIsNextRound(choose_reward)
    local isNewRound = NetRelicData:IsNewRound()
    -- 新章节
    if isNewRound then
		NetRelicData.isNewRound = false
        -- 关闭自动操作
        self.autoPlay = false
        SetActive(self.ui.m_goAutoPlaySpine, self.autoPlay)
        SetActive(self.ui.m_goGiftMask, self.autoPlay)
        SetActive(self.ui.m_goAutoPlayNormal, not self.autoPlay)
        SetActive(self.ui.m_txtPlayAutoTip, self.autoPlay)
        self.ui.m_txtPlayAuto.text = LangMgr:GetLang(9317)
        self.ui.m_txtPlayAutoTip.text = LangMgr:GetLang(9322)
        -- 玩家排名前三
        if self.playerRank <= 3 then
            local str = "rank" .. self.playerRank .. "_reward"
            local config = RelicManager:GetRoundConfig(self.old_round)
            if config[str] then
                local pos = self.ui["m_goRank" .. self.playerRank]
                -- 季节活动宝箱转换
                local rewardStr = NetSeasonActivity:GetChangeItemId(config[str])
                self.rankRewardTable = string.split(rewardStr, ";")
                -- 玩家跳到宝箱
                self:PlayJumpAnimation(pos, 30, function()
                    self:ShowRankReward(rewardStr)
                end)
            end
        else
            self:ShowChapterReward()
        end
    -- 不是新章节
    else
		if choose_reward then
            -- 通关后先开云再让玩家头像跳跃
            self:OpenCloudNormal()
		else
			self:UpdatePanel()
			self.canOpenDoor = true
		end
	end
end

--- 显示排名宝箱奖励动画
--- @param rewardStr string 奖励配置
function UI_RelicView:ShowRankReward(rewardStr)
    self:CreateReward(rewardStr)
    SetActive(self.ui.m_goBlackBgShow, true)
    self.ui.m_txtRankRewardTitle.text = LangMgr:GetLangFormat(8927, self.playerRank)
    local anim = GetComponent(self.ui.m_goRewardRankPrize, UE.Animation)
    if not anim then return end
    -- 获取宝箱动画时长
    local animLength = anim:GetClip("relicprize").length
    -- 暂时屏蔽点击
    self.canContinueRankReward = false
    -- 延迟动画时长后恢复点击
    TimeMgr:CreateTimer(UIDefine.UI_RelicView,
    function()
        self.canContinueRankReward = true
    end, animLength, 1)
end

--机器人匹配
function UI_RelicView:BeginRobotMatch()
    -- 匹配时先隐藏机器人头像
    local robotPos = self.nodexItemList[1].robotPos
    SetActive(robotPos, false)
    local currentRoundCount = NetRelicData:GetCurrentRoundCount()
    UI_SHOW(UIDefine.UI_RollDiceViewMatching, function()
        -- 防止飞机器人头像时点击了金字塔开门
		if not self.autoPlay then
        	self.canOpenDoor = false
		end
        self:FlyRobot()
    end, nil, NetRelicData:GetRobotData(), currentRoundCount)
end

--章节开云
function UI_RelicView:OpenCloud()
    local function openMid()
        self.ui.m_scrollview.horizontalNormalizedPosition = 0
        -- 先隐藏机器人头像
        local robotPos = self.nodexItemList[1].robotPos
        SetActive(robotPos, false)
        self:UpdatePanel()
        DOScale(self.ui.m_imgBody.gameObject.transform, Camera_Siz, 0)
        DOScale(self.ui.m_imgBody.gameObject.transform, 1, Camera_Time)
        if self.choose_bool then
            -- 玩家头像直接设置到第一个节点的位置
            self:UpdateRobot(nil, true)
        end
	end
    local function openEnd()
        self:BeginRobotMatch()
        self.canShowRankBox = true
    end
    local cloud_ani = GetComponent(self.ui.m_goCloud.gameObject,UE.Animation)
    PlayAnimStatusIndex(cloud_ani,"cloud_relic",openMid,openEnd)
    self:CloseRankBox()
    self.canShowRankBox = false
end

--- 节点开云
function UI_RelicView:OpenCloudNormal()
    local function openMid()
        DOScale(self.ui.m_imgBody.gameObject.transform, Camera_Siz, 0)
        DOScale(self.ui.m_imgBody.gameObject.transform, 1, Camera_Time)
        self:UpdatePanel()
    end
    local function openEnd(index)
        -- 自动操作时，不播放法老王动画
        if self.autoPlay then
            self.canOpenDoor = true
            if self.choose_bool then
                self:UpdateRobot()
            end
        else
            self:FlAppearSp()
        end
        self.canShowRankBox = true
    end
    local cloud_ani = GetComponent(self.ui.m_goCloud.gameObject,UE.Animation)
    PlayAnimStatusIndex(cloud_ani,"cloud_relic",openMid,openEnd)
    self:CloseRankBox()
    self.canShowRankBox = false
end

function UI_RelicView:OnRefresh()
	SetActive(self.ui.m_goPassPortRed,NetRelicData:IsShowRelicRed())
    self.ui.m_txtRelicNum.text = NetRelicData:GetRelicNum()
    
    UIMgr:RefreshAllMainFace(9, nil, {id = self.activeId, state = 2});
end

function UI_RelicView:onDestroy()
	self.ui.m_imgBody.gameObject.transform:DOKill()
	self.ui.m_goRankTitle1.transform:DOKill()
	self.ui.m_goRankTitle2.transform:DOKill()
	TimeMgr:DestroyTimer(UIDefine.UI_RelicView,Timer)
	if self.isPush then
		UI_CLOSE(UIDefine.UI_GuideMask)
	end
	NetRelicData:GetAirReward()
	self.doorItemList = nil
	self.robotItemList = nil
	self.nodexItemList = nil
	self.activityItem = nil
	if self.isPush then
		NetPushViewData:RemoveViewByIndex(PushDefine.UI_RelicView)
		NetPushViewData:CheckOtherView(true)
	end
end

function UI_RelicView:onUIEventClick(go,param)
    local name = go.name
	--if not self.canOpenDoor then
		--return
	--end
    self:CloseRankBox()
	if name == "m_btnPassPort" then
		UI_SHOW(UIDefine.UI_RelicPassPort)
	elseif name == "m_btnClose" then
		if not self.canOpenDoor then
			return
		end
        -- 开启自动操作时不能关闭界面
        if self.autoPlay then return end
		self:Close()
	elseif name == "m_btnHelp" then
        -- NetRelicData:AddRelicNum(10)
        UI_SHOW(UIDefine.UI_RelicHelp)
    -- 点击排名宝箱 1
    elseif name == "m_btnRankBox1" then
        local rewardStr = RelicManager:GetRoundConfig(self.round).rank1_reward
        self:ShowRankBox(name, rewardStr)
    -- 点击排名宝箱 2
    elseif name == "m_btnRankBox2" then
        local rewardStr = RelicManager:GetRoundConfig(self.round).rank2_reward
        self:ShowRankBox(name, rewardStr)
    -- 点击排名宝箱 3
    elseif name == "m_btnRankBox3" then
        local rewardStr = RelicManager:GetRoundConfig(self.round).rank3_reward
        self:ShowRankBox(name, rewardStr)
    -- 宝箱奖励动画界面，点击继续
    elseif name == "m_btnContinueRank" then
        if self.canContinueRankReward then
            self:FlyRankReward()
        end
	elseif name == "btnCloseAni" then
		SetActive(self.ui.m_goFL,false)
		self.canOpenDoor = true
        -- 跳过法老王动画之后，玩家头像跳跃
        if self.choose_bool then
            self:UpdateRobot()
        end
    -- 点击放大镜
    elseif name == "m_btnRelicGlass" then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(8902))
    -- 点击自动操作
    elseif name == "m_btnAutoPlay" then
        self:OpenAutoPlay()
    end
end

function UI_RelicView:FlyRankReward()
	SetActive(self.ui.m_goBlackBgShow, false)

	for k, v in pairs(self.rankRewardTable) do
		local arr1 = string.split(v,"|")
		local itemId = v2n(arr1[1])
		local num = v2n(arr1[2])
        if k == table.count(self.rankRewardTable) then
            self:BubbleByIndexAnim(itemId, num, nil, function()
                self:ShowChapterReward()
            end)
        else
            self:BubbleByIndexAnim(itemId, num)
        end
	end			
end

--- 显示章节奖励界面
function UI_RelicView:ShowChapterReward()
    local rewardStr = RelicManager:GetClearRewardStr(self.old_round or self.round)
    UI_SHOW(UIDefine.UI_RelicViewMessage, 1, rewardStr, function()
        local arr = string.split(rewardStr,";")
        for k, v in pairs(arr) do
            local arr1 = string.split(v,"|")
            local itemId = v2n(arr1[1])
            local num = v2n(arr1[2])
            if k == table.count(arr) then
                self:BubbleByIndexAnim(itemId, num, nil, function()					
					self:OpenCloud()
                end)
            else
                self:BubbleByIndexAnim(itemId, num)
            end
        end
    end)
end

---------Tool--------
function UI_RelicView:GetNowEnergySch()
	local maxCount = self.activityItem.form.max
	local schedule = self.activityItem.info.energySch + 1
	schedule = math.min(schedule,maxCount)
	return schedule
end

function UI_RelicView:RemoveBtnDoor()
	for k, v in pairs(self.doorItemList) do
		local btnDoor = GetChild(v.obj,"btnDoor",UEUI.Button)
		if btnDoor then
			RemoveUIComponentEventCallback(btnDoor,UEUI.Button)
			--AddUIComponentEventCallback(btnDoor, UEUI.Button, function(arg1,arg2)
					--self:OpenDoor(i)
				--end)
		end	
	end		
end

function UI_RelicView:AddBtnDoor()
	for k, v in pairs(self.doorItemList) do
		local btnDoor = GetChild(v.obj,"btnDoor",UEUI.Button)
		if btnDoor then
			RemoveUIComponentEventCallback(btnDoor.Button)
			AddUIComponentEventCallback(btnDoor, UEUI.Button, function(arg1,arg2)
				self:OpenDoor(k)
			end)
		end
	end
end

--------Ani------
function UI_RelicView:BubbleByIndexAnim(rewardId,_num,door_index,onCompleted)
    if not self.ui then return end
	SetActive(self.ui.m_goTarget,true)
		local oldPos
		if door_index then
			oldPos =  UIMgr:GetObjectScreenPos(self.doorItemList[door_index].obj.transform)
		else
			oldPos = Vector3(0,0,0)
		end				
		local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_goTarget.transform.position)
		local flyId = rewardId
		local num = _num or 1
		MapController:FlyUIAnim(oldPos.x,oldPos.y,flyId,num,posScreen.x, posScreen.y,
			nil,nil,nil,0.7,nil,function ()
                if not self.ui then return end
                if self.airIndex <= 5 then
					local rewardGo     = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
					self.airIndex = self.airIndex + 1
					SetActive(rewardGo,true)
					SetImageSprite(rewardGo,ItemConfig:GetIcon(v2n(NetSeasonActivity:GetChangeItemId(rewardId))),false)
				else
					local rewardGo     = GetChild(self.ui.m_goPanel,"pos" .. 1 ,UEUI.Image)
					SetImageSprite(rewardGo,ItemConfig:GetIcon(v2n(NetSeasonActivity:GetChangeItemId(rewardId))),false)
				end
                -- 奖励云播放动画
                GetChild(self.ui.m_goTarget, "bubble", Animation):Play()
                GetChild(self.ui.m_goTarget, "bg", Animation):Play()
				if onCompleted then
					onCompleted()
				end
			end)
end

function UI_RelicView:FlyScore(door_index,onCompleted)
	local oldPos =  UIMgr:GetObjectScreenPos(self.doorItemList[door_index].obj.transform)
	local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_btnPassPort.gameObject.transform.position)

	local flyId = RelicManager:GetPassPortFlyItem()
	local score = v2n(RelicManager:GetSettingConfigById(5).value)
	--self.activityItem:AddActiveIntegral(score)
	MapController:FlyUIAnim(oldPos.x,oldPos.y,flyId,score,posScreen.x, posScreen.y,
		nil,nil,nil,0.7,nil,function ()
			if onCompleted then	
				onCompleted()
			end 
			self.directBuyGo:SetInteractable(true)
		end)
end


function UI_RelicView:PalyTopAni()
	local y1 = self.ui.m_goRankTitle1.transform.localPosition.y
	local y2 = self.ui.m_goRankTitle2.transform.localPosition.y
	if y1 > 0 then -->0在显示
		DOLocalMoveY(self.ui.m_goRankTitle1.transform,200,1,function ()
				self.ui.m_goRankTitle1.transform.localPosition = Vector3(0,-150,0)
		end,Ease.OutQuint)
	else
		DOLocalMoveY(self.ui.m_goRankTitle1.transform,50,1,function ()
				self.ui.m_goRankTitle1.transform.localPosition = Vector3(0,55,0)
		end,Ease.OutQuint)
	end
	if y2 > 0 then
		DOLocalMoveY(self.ui.m_goRankTitle2.transform,200,1,function ()
				self.ui.m_goRankTitle2.transform.localPosition = Vector3(0,-150,0)
			end,Ease.OutQuint)
	else
		DOLocalMoveY(self.ui.m_goRankTitle2.transform,50,1,function ()
			self.ui.m_goRankTitle2.transform.localPosition = Vector3(0,55,0)
		end,Ease.OutQuint)
	end
end

--- 初始化排名宝箱奖励列表
function UI_RelicView:InitRankBox(name, rewardStr)
    if self.rankBox == nil then
        self.rankBox = RankBox.new(self.uiGameObject)
        self.rankBox:InitUI(nil,nil,function()
			-- 获取定位物体
			local rewardPosObj = GetChild(self.ui.m_goRankBox, name .. "/rewardPos")
			if not rewardPosObj then return end
			-- 刷新奖励列表
			self.rankBox:UpdateItem(rewardPosObj, rewardStr)
			self:CheckRankBoxOverScreen(rewardPosObj)
			self:MoveRankBoxArrow(rewardPosObj)
		end)
    end
end

--- 关闭排名宝箱奖励列表
function UI_RelicView:CloseRankBox()
    if self.rankBox then
        self.rankBox:Destory()
        self.rankBox = nil
    end
end

--- 显示排名宝箱奖励列表
--- @param name string 宝箱按钮名称
--- @param rewardStr string 奖励配置
function UI_RelicView:ShowRankBox(name, rewardStr)
    if not self.canShowRankBox then return end
    self:InitRankBox(name, rewardStr)
end

--- 移动排名宝箱奖励列表的底部箭头
--- @param rewardPosObj any 定位物体
function UI_RelicView:MoveRankBoxArrow(rewardPosObj)
    local downArrow = GetChild(self.rankBox.BoxGo, "Image")
    if not downArrow then return end
    -- 底部箭头向宝箱位置偏移
    local pos = rewardPosObj.transform.localPosition
    local downArrowPos = downArrow.transform.localPosition
    downArrow.transform.localPosition = Vector3.New(-pos.x, downArrowPos.y, downArrowPos.z)
end

--- 检查排名宝箱奖励列表是否超出屏幕边缘
--- @param rewardPosObj any 定位物体
function UI_RelicView:CheckRankBoxOverScreen(rewardPosObj)
    local canvas = UIMgr:GetCanvasRectTrans()
    local rankBoxBg = GetChild(self.rankBox.BoxGo, "bg", UE.RectTransform)
    if not rankBoxBg then return end
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rankBoxBg)
    -- 右侧边缘位置
    local edgePos = canvas.rect.width / 2 - rankBoxBg.rect.width / 2
    -- 奖励列表当前位置
    local rewardPos = UIMgr:GetUIPosByWorld(rewardPosObj.transform.position)
    -- 奖励列表当前位置超过了右侧边缘位置，需要往左边移动
    if rewardPos.x > edgePos then
        -- 超出的差值
        local diff = rewardPos.x - edgePos
        local pos = rewardPosObj.transform.localPosition
        -- 定位物体的新位置
        local space = 10
        pos.x = pos.x - diff - space
        rewardPosObj.transform.localPosition = pos
        -- 根据定位物体重新设置奖励列表的位置
        local targetPos = UIMgr:GetUIPosByWorld(rewardPosObj.transform.position)
        self.rankBox.BoxGo.transform.localPosition = targetPos
    end
end

--- 排名宝箱奖励动画的奖励列表创建
--- @param rewardStr string 奖励配置
function UI_RelicView:CreateReward(rewardStr)
    -- 清空已生成的奖励列表
    local length = self.ui.m_goRewardParent.transform.childCount
    for i = 1, length, 1 do
        UEGO.Destroy(self.ui.m_goRewardParent.transform:GetChild(i - 1).gameObject)
    end
    -- 季节活动宝箱转换
    rewardStr = NetSeasonActivity:GetChangeItemId(rewardStr)
    -- 生成新的奖励列表
    local itemDatas = rewardStr:split(";")
    for i, itemData in ipairs(itemDatas) do
        local data = itemData:split("|")
        local id = data[1]
        local count = data[2]
        local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, id)
        local newGo = CreateGameObjectWithParent(self.ui.m_goRewardChild,self.ui.m_goRewardParent.transform)
        local newTrans = newGo.transform
        SetActive(newGo,true)
        if i == 1 then
            newTrans.localPosition = Vector3.New(-158, -128, 0)
        else
            newTrans.localPosition = Vector3.New(98, -128, 0)
        end
        local imgReward = GET_UI(newGo, "imgReward", TP(UEUI.Image))
        local countReward = GET_UI(newGo, "countReward", TP(UEUI.Text))
        local localMoveY = GetChild(newGo, "DOLocalMoveYLoop", UE.Transform)
        SetUIImage(imgReward, itemConfig["icon_b"], false)
        countReward.text = "x"..count
        local detail = 2.0 + Random(1, 5) * 0.2
        DOLocalMoveYLoop(localMoveY, localMoveY.localPosition.y + 20, detail, LoopType.Yoyo, Ease.InOutSine)
    end
end

--- 更换背景和石台图片
--- @param backgroundIcon string 背景图路径
--- @param stoneIcon string 石台路径
function UI_RelicView:ChangeBackground(backgroundIcon, stoneIcon)
    local currentRoundCount = NetRelicData:GetCurrentRoundCount()
    -- 第 8 关及后续关卡，背景和石台图片循环复用
    if currentRoundCount > 7 then
        local mapNum = currentRoundCount % 3
        if mapNum == 0 then mapNum = 3 end
        local mapStr = "map" .. mapNum
        backgroundIcon = string.gsub(backgroundIcon, "map1", mapStr)
        stoneIcon = string.gsub(stoneIcon, "map1", mapStr)
    end
    -- 修改背景图和石头图片
    SetUIImage(self.ui.m_imgBody, backgroundIcon, false)
    local stone = GetComponent(self.ui.m_goRankBox, typeof(UEUI.Image))
    SetUIImage(stone, stoneIcon, false)
end

--- 机器人视图滑动到玩家位置
function UI_RelicView:TurnPageScroll()
    -- 获取 item 的宽度
    local itemRT = GetComponent(self.ui.m_goNodeOneBox, UE.RectTransform)
    if not itemRT then return end
    local itemWidth = itemRT.rect.width
    -- 计算 content 的位置
    local moveX = (self.index - 1) * itemWidth
    local moveY = self.ui.m_scrollview.content.anchoredPosition.y
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_scrollview.content)
    -- 滑动最大距离限制
    local viewportWidth = self.ui.m_scrollview.viewport.rect.width
    local contentWidth = self.ui.m_scrollview.content.rect.width
    local limit = math.abs(contentWidth - viewportWidth)
    if moveX > limit then moveX = limit end
    -- 滚动视图自动滑动的动画
    self.ui.m_scrollview.enabled = false
    self.ui.m_scrollview.content:DOAnchorPos(Vector2.New(-moveX, moveY), 1)
        :SetId(UIDefine.UI_RelicView)
        :OnComplete(function()
            self.ui.m_scrollview.enabled = true
        end):SetDelay(0.6)
end

--- 匹配后飞机器人头像
function UI_RelicView:FlyRobot()
    -- 刷新机器人头像
    local robotData = NetRelicData:GetRobotData()
    for k, v in pairs(robotData) do
        local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, v.head)
        local robot = GetChild(self.ui.m_transFlyRobot, "player" .. k)
        local imgHead = GetChild(robot, "imgHead", UEUI.Image)
        --SetUIImage(imgHead, headConfig.icon, false)
		SetActive(imgHead,false)
		local customHeadObj = GetChild(robot, "headNode/CustomHead") or CreateCommonHead(robot.transform,0.5)

		local headNodeRect = GetComponent(customHeadObj,UE.RectTransform)
		headNodeRect.anchoredPosition = Vector2.New(0,16)
		
		if v2n(v.PlayerID) == 1 then
			SetMyHeadAndBorderByGo(customHeadObj)
		else
			SetHeadAndBorderByGo(customHeadObj,v.head,v.border)
		end
    end
    SetActive(self.ui.m_transFlyRobot.gameObject, true)
    -- 第一个节点的位置
    local robotPos = self.nodexItemList[1].robotPos
    local targetPoint = self:ConvertToRectPos(robotPos)
    targetPoint.x = targetPoint[1]
    targetPoint.y = targetPoint[2] + 100

    local length = self.ui.m_transFlyRobot.childCount
    local count = 0
    for i = 1, length, 1 do
        local robot = self.ui.m_transFlyRobot:GetChild(i - 1)
        local robotRT = GetComponent(robot, UE.RectTransform)
        -- 记录机器人头像原来的位置
        local originPos = robotRT.localPosition
        -- 机器人头像位移
        DOLocalMoveY(robot.transform, robot.localPosition.y + 200, 0.2,
        function()
            DOLocalMoveY(robot.transform, targetPoint.y, 0.5,
            function()
                -- 隐藏父节点并还原机器人头像原始位置
                SetActive(self.ui.m_transFlyRobot.gameObject, false)
                robotRT.localPosition = originPos
                count = count + 1
                -- 所有机器人头像飞完了
                if count >= 5 then
                    SetActive(robotPos, true)
                    -- 播放法老王动画
                    self:FlAppearSp()
                end
            end, Ease.InQuad)
        end, Ease.OutQuad)

        DOLocalMoveX(robot.transform, robot.localPosition.x - 150, 0.1,
        function()
            DOLocalMoveX(robot.transform, targetPoint.x, 0.6,
            function()
                -- 隐藏父节点并还原机器人头像原始位置
                SetActive(self.ui.m_transFlyRobot.gameObject, false)
                robotRT.localPosition = originPos
            end, Ease.InQuad)
        end,Ease.OutQuad)
    end
end

return UI_RelicView