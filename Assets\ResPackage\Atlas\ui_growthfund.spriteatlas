%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: ui_growthfund
  m_EditorData:
    serializedVersion: 2
    textureSettings:
      serializedVersion: 2
      anisoLevel: 0
      compressionQuality: 0
      maxTextureSize: 0
      textureCompression: 0
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings:
    - serializedVersion: 3
      m_BuildTarget: DefaultTexturePlatform
      m_MaxTextureSize: 2048
      m_ResizeAlgorithm: 0
      m_TextureFormat: 50
      m_TextureCompression: 1
      m_CompressionQuality: 50
      m_CrunchedCompression: 0
      m_AllowsAlphaSplitting: 0
      m_Overridden: 0
      m_IgnorePlatformSupport: 0
      m_AndroidETC2FallbackOverride: 0
      m_ForceMaximumCompressionQuality_BC6H_BC7: 0
    packingSettings:
      serializedVersion: 2
      padding: 2
      blockOffset: 0
      allowAlphaSplitting: 0
      enableRotation: 0
      enableTightPacking: 0
      enableAlphaDilation: 0
    secondaryTextureSettings: {}
    variantMultiplier: 1
    packables:
    - {fileID: 2800000, guid: 00c295698f55b8145bb97d5dc7ef5c51, type: 3}
    - {fileID: 2800000, guid: a9f3eba9eb238d7439f2fb4f4e2a9f9f, type: 3}
    - {fileID: 2800000, guid: 5df8b06e2fe791b4e9b5d5e08d89296b, type: 3}
    bindAsDefault: 0
    isAtlasV2: 0
    cachedData: {fileID: 0}
    packedSpriteRenderDataKeys:
    - 00c295698f55b8145bb97d5dc7ef5c51: 21300000
    - a9f3eba9eb238d7439f2fb4f4e2a9f9f: 21300000
    - 5df8b06e2fe791b4e9b5d5e08d89296b: 21300000
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites:
  - {fileID: 21300000, guid: 00c295698f55b8145bb97d5dc7ef5c51, type: 3}
  - {fileID: 21300000, guid: a9f3eba9eb238d7439f2fb4f4e2a9f9f, type: 3}
  - {fileID: 21300000, guid: 5df8b06e2fe791b4e9b5d5e08d89296b, type: 3}
  m_PackedSpriteNamesToIndex:
  - window_chengzhangjijin_close
  - window_chengzhangjijin_iconbox
  - window_chengzhangjijin_jiangli
  m_RenderDataMap: {}
  m_Tag: ui_growthfund
  m_IsVariant: 0
