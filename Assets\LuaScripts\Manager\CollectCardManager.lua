-- 卡牌收集活动管理器

local CollectCardManager = Class()
local M = CollectCardManager
local Round = 1001  -- 第几期活动

function M:ctor()
    self.cardConfig = {}
    self.cardList = {}
    self.cardGroupByStar = {}
    self.cardGroupByQualityStar = {}
end

--- 初始化事件
function M:InitEvent()

end

--- 清理列表
function M:ClearTable()
    self.cardConfig = {}
    self.cardList = {}
    self.cardGroupByStar = {}
    self.cardGroupByQualityStar = {}
end

--- 设置第几期活动
function M:SetRound()
    local open, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.CollectCard)
    if open then
        Round = activityItem.form.activities_type
    end
end

--- 获取卡牌系列配置
--- @return table cardConfig 卡牌系列配置
function M:GetCardSeries()
    if IsTableEmpty(self.cardConfig) then
        self:SetRound()
        local collection = self:GetCardCollectionConfig(Round)
        if collection then
            local itemList = string.split(collection.card_collection, "|")
            for _, value in ipairs(itemList) do
                local seriesID = v2n(value) or 1
                local seriesData = self:GetCardCollectionItemConfig(seriesID)
                if seriesData then					
                    table.insert(self.cardConfig, seriesData)
                end
            end
        end
    end
    return self.cardConfig
end

--- 获取卡牌列表
--- @return table cardList 卡牌列表
function M:GetCardList()
    if IsTableEmpty(self.cardList) then
        local series = self:GetCardSeries()
        if series then
            for _, value in ipairs(series) do
                local cardList = string.split(value.card, "|")
                for _, cardID in ipairs(cardList) do
                    local quality = "c"
                    local star = 1
                    local itemID = v2n(cardID)
                    local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
                    if itemConfig then
                        if itemConfig.id_use2 then
                            quality = "cg"
                        end
                        star = itemConfig.id_use
                    end
                    local card = {
                        seriesID = value.id,
                        cardID = cardID,
                        quality = quality,
                        star = tostring(star)
                    }
                    table.insert(self.cardList, card)
                end
            end
        end
    end
    return self.cardList
end

--- 通过卡牌 ID 获取卡牌对象
--- @param cardID string 卡牌 ID
--- @return table card 卡牌对象
function M:GetCardByID(cardID)
    for _, value in ipairs(self.cardList) do
        if value.cardID == cardID then
            return value
        end
    end
end

--- 根据卡牌 ID 获取对应的系列 ID
--- @param cardID number 卡牌 ID
--- @return number seriesID 系列 ID
function M:GetSeriesIDByCard(cardID)
    local cardList = self:GetCardList()
    for _, card in pairs(cardList) do
        if card.cardID == cardID then
            return card.seriesID
        end
    end
end

--- 根据星级获取卡牌列表
--- @param star string 星级
--- @return table result 过滤后的卡牌列表
function M:GetCardGroupByStar(star)
    if IsTableEmpty(self.cardGroupByStar) then
        local cardList = self:GetCardList()
        for _, card in ipairs(cardList) do
            if not self.cardGroupByStar[card.star] then
                self.cardGroupByStar[card.star] = {}
            end
            table.insert(self.cardGroupByStar[card.star], card)
        end
    end
    return self.cardGroupByStar[star]
end

--- 根据品质和星级获取卡牌列表
--- @param quality string 品质
--- @param star string 星级
--- @return table result 过滤后的卡牌列表
function M:GetCardGroupByQualityStar(quality, star)
    if IsTableEmpty(self.cardGroupByQualityStar) then
        local cardList = self:GetCardList()
        for _, card in ipairs(cardList) do
            if not self.cardGroupByQualityStar[card.quality] then
                self.cardGroupByQualityStar[card.quality] = {}
            end
            if not self.cardGroupByQualityStar[card.quality][card.star] then
                self.cardGroupByQualityStar[card.quality][card.star] = {}
            end
            table.insert(self.cardGroupByQualityStar[card.quality][card.star], card)
        end
    end
    return self.cardGroupByQualityStar[quality][star]
end

--- 获取当前周期活动的收藏册配置
---@return table|nil config 收藏册配置
function M:GetCurrentRoundConfig()
    self:SetRound()
    local config = self:GetCardCollectionConfig(Round)
    return config
end

--- 根据 id 获取收藏册配置
--- @param id number ID
--- @return table|nil config 收藏册配置
function M:GetCardCollectionConfig(id)
    local config = ConfigMgr:GetData(ConfigDefine.ID.card_collection)
    if config then
        for _, value in pairs(config) do
            if value.id == id then
                return value
            end
        end
    else
        Log.Error("获取收藏册配置表错误")
    end
end

--- 根据 id 获取收藏册卡牌配置
--- @param id number ID
--- @return table|nil config 收藏册卡牌配置
function M:GetCardCollectionItemConfig(id)
    local config = ConfigMgr:GetData(ConfigDefine.ID.card_collection_item)
    if config then
        for _, value in pairs(config) do
            if value.id == id then
                return value
            end
        end
    else
        Log.Error("获取收藏册配置表错误")
    end
end

--- 获取礼包配置
--- @return table config 礼包配置
function M:GetGiftConfig()
    local config = ConfigMgr:GetData(ConfigDefine.ID.card_trade)
    if config then
        return config
    else
        Log.Error("获取礼包配置表错误")
    end
end

--- 获取单个礼包配置
--- @param id number 礼包 ID
--- @return table config 礼包配置
function M:GetGiftItemConfig(id)
    local config = ConfigMgr:GetData(ConfigDefine.ID.card_trade)
    if config then
        for _, value in pairs(config) do
            if value.id == id then
                return value
            end
        end
    else
        Log.Error("获取礼包配置表错误")
    end
end

--展示卡牌背景
function M:ShowCardBg(bg,isGold,hasCard)
    local path = "jika_ka2"
    if hasCard then
        path = isGold and "jika_ka2" or "jika_ka1"
    else
        path = isGold and "jika_ka2_hui" or "jika_ka1_hui"
    end
    SetUIImage(bg, "Sprite/ui_tujian/"..path..".png", false)
end

--设置特效遮罩
function M:SetMaskSize(obj,rectObj)
    if obj == nil or rectObj == nil then
        return
    end
    
	local rect = Vector4(0,0,0,0)
	if GameUtil.GetWorldCornersByTrans then 
		rect = GameUtil.GetWorldCornersByTrans(rectObj.gameObject)
	end
 	
	--local particle = GetChild(obj,"FX_82_kapaishaoguang/Particle System/Particle System")
	--local render = GetComponent(particle,UE.ParticleSystemRenderer)
    --if render and render.material then
        --render.material:SetVector(UE.Shader.PropertyToID("_Area"), rect)
    --end
    
	local renders = obj:GetComponentsInChildren(typeof(UE.ParticleSystemRenderer))
	for i = 0, renders.Length - 1 do
		if renders[i] and renders[i].material then
			renders[i].material:SetVector(UE.Shader.PropertyToID("_Area"), rect)
		end
	end
end

--判断集卡红点逻辑
function M:CheckRedDot()
    local isOpen,_ = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.CollectCard)
    if not isOpen then
        return false
    end
    
    --集卡直购免费礼包
    if NetLimitActGift:IsShowRed(ActivityTotal.CollectCard) then
        return true
    end
    
    local config = self:GetCardSeries()
    if config then
        for _, series in ipairs(config) do
            local id = series.id
            --存在新卡标识
            local redPointNum = NetCollectCardData:GetRedPointNumOfSeries(id)
            if redPointNum > 0 then
                return true
            end

            --列表卡包收集完成奖励
            local currentNum, totalNum = NetCollectCardData:GetCardNumOfSeries(id)
            local isGetReward = NetCollectCardData:GetSeriesReward(id)
            if currentNum >= totalNum and (not isGetReward) then
                return true
            end
        end
    end
    
    return false
end

--设置星星样式
function  M:SetStar(cardID,img)
    local cardStar = NetCollectCardData:GetCardStar(cardID)
    cardStar = (cardStar and cardStar > 0) and cardStar or 1
    SetUIImage(img,"Sprite/ui_tujian/jika_xiangqing_star_"..cardStar..".png", true)
end

return CollectCardManager