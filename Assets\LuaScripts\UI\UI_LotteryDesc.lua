--抽卡掉落概率详情界面
local UI_LotteryDesc = Class(BaseView)

function UI_LotteryDesc:OnInit()

    self.ratioConfig = {}
    ----------初始化界面相关逻辑----------
    SetActive(self.ui.m_goPrefab,false)
    SetActive(self.ui.m_btnTip,false)
end

function UI_LotteryDesc:OnCreate(configId)

    
    ----------初始化业务逻辑----------
    self.configId = configId
    self:ShowDescList()
    
end

function UI_LotteryDesc:OnRefresh(param)
    
end

function UI_LotteryDesc:onDestroy()
    
end

function UI_LotteryDesc:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        SetActive(self.ui.m_btnTip,false)
        self:Close()
    elseif name == "m_btnTip" then    
        SetActive(self.ui.m_btnTip,false)
    end

end

--********************************业务逻辑********************************
function UI_LotteryDesc:ShowDescList()
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_draw_cards,self.configId)
    if config then
        local list = Split1(config.drop,";")
        for _,v in ipairs(list) do
            local value = Split1(v,"|")
            self.ratioConfig[tonumber(value[1])] = tonumber(value[2])
        end
    end
    
    local cullingList = {}
    local config1 = ConfigMgr:GetData(ConfigDefine.ID.slg_draw_cards_drop)
    if config1 then
        for _,v in pairs(config1) do
            if self.ratioConfig[tonumber(v.group_id)] then
                if not cullingList[v.group_id] then
                    cullingList[v.group_id] = {}
                end
                table.insert(cullingList[v.group_id],v)
            end
        end
    end
    
    local cullingGroupIdList = {}
    for k,v in pairs(cullingList) do
        local isNeedCulling = true
        for m,n in ipairs(v) do
            local canAdd = true
            --判断是否需要剔除，如果未获得该英雄则不可掉落
            if v2n(n.is_get) == 1 and n.get_id then
                local isExist = HeroManager:IsHeroActive(n.get_id)
                if not isExist then
                    canAdd = false
                end
            end

            if canAdd then
                isNeedCulling = false
                break
            end
        end
        if isNeedCulling then
            table.insert(cullingGroupIdList,k)
        end
    end

    if next(cullingGroupIdList) ~= nil then
        local cullSum = 0
        for k,v in ipairs(cullingGroupIdList) do
            cullSum = cullSum + self.ratioConfig[v]
            self.ratioConfig[v] = nil
        end
        cullSum = 1-cullSum
        local newRatioConfig = {}
        for k,v in pairs(self.ratioConfig) do
            newRatioConfig[k] = v/cullSum
        end
        
        self.ratioConfig = newRatioConfig
    end
    
    self.sumList = {}
    local config1 = ConfigMgr:GetData(ConfigDefine.ID.slg_draw_cards_drop)
    if config1 then
        for _,v in pairs(config1)do
            if self:CheckUnlock(v.group_id,v.is_get,v.get_id) then
                if not self.sumList[v.group_id] then
                    self.sumList[v.group_id] = 0
                end
                self.sumList[v.group_id] = self.sumList[v.group_id] + v2n(v.weight)
            end
        end

        self.resultList = {}
        for _,v in pairs(config1) do
            if self:CheckUnlock(v.group_id,v.is_get,v.get_id) then
                local ratio1 = self:KeepDotNumber(5,v.weight/self.sumList[v.group_id])
                v.ratio = ratio1*self.ratioConfig[tonumber(v.group_id)]
                table.insert(self.resultList,v)
            end
        end

        local sortList = {}
        for _,v in ipairs(self.resultList) do
            local config2 = ConfigMgr:GetDataByID(ConfigDefine.ID.item,tonumber(v.item_id))
            if config2 then
                local quality = config2.slg_quality
                v.quality = config2.slg_quality
                if not sortList[quality] then
                    sortList[quality] = {}
                end
                table.insert(sortList[quality],v)
            end
        end

        local resultList = {}
        for k,v in pairs(sortList) do
            local sumRatio = 0
            for _,n in ipairs(v) do
                sumRatio = sumRatio + n.ratio
            end
            table.insert(resultList,{quality = k;list = v;sumRatio = sumRatio})
        end
        
        table.sort(resultList,function(a, b) 
            return a.quality > b.quality
        end)

        local content = self.ui.m_goContent.transform
        local listSum = #resultList
        for k,v in ipairs(resultList) do
            local qualityConfig = LotteryManager:GetQualityConfig(v.quality)

            local obj = UEGO.Instantiate(self.ui.m_goDescContainer,content)
            local bgRect = GetComponent(obj,UE.RectTransform)
            local title = GetChild(obj,"title",UEUI.Text)
            local ratio = GetChild(obj,"ratio",UEUI.Text)
            local titleIcon = GetChild(obj,"titleIcon",UEUI.Image)
            local line = GetChild(obj,"line")

            local gridLayout = GetChild(obj,"main",UEUI.GridLayoutGroup)
            local layoutRect = GetChild(obj,"main",UE.RectTransform)
            local mainTrans = GetChild(obj,"main",UE.Transform)

            title.text = qualityConfig.name
            ratio.text = self:KeepDotNumber(3,v.sumRatio*100).."%"
            local childCount = #v.list
            SetUIImage(titleIcon,qualityConfig.icon,true)
            SetActive(line,k~= listSum)

            --适配Container
            local rowCount = self:CalculateRowCount(5,childCount)
            local height = gridLayout.cellSize.y*rowCount+gridLayout.spacing.y*(rowCount-1)
            layoutRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, height)
            bgRect:SetSizeWithCurrentAnchors(UE.RectTransform.Axis.Vertical, 74+height+40)
            
            table.sort(v.list,function(a, b) 
                return v2n(a.sort) > v2n(b.sort)
            end)
            
            for index,n in ipairs(v.list) do
                local gridItem = UEGO.Instantiate(self.ui.m_goDescGridItem,mainTrans)
                local loader = GetChild(gridItem,"loader",UE.Transform)
                local rewardItem = UEGO.Instantiate(self.ui.m_goHeroPiece,loader)
                rewardItem.transform.localPosition = Vector3.zero

                local gridRatio = GetChild(gridItem,"ratio",UEUI.Text)
                gridRatio.text = self:KeepDotNumber(3,n.ratio*100).."%"
                
                local itemData = ConfigMgr:GetDataByID(ConfigDefine.ID.item,n.item_id)
                if itemData then
                    local bg = GetChild(rewardItem,"quality",UEUI.Image)
                    local icon = GetChild(rewardItem,"icon",UEUI.Image)
                    local txt = GetChild(rewardItem,"txt",UEUI.Text)
                    local btn = GetChild(rewardItem,"btn",UEUI.Button)

                    txt.text = "x"..NumToGameString(n.item_num)
                    SetUIImage(bg,qualityConfig.sprite,false)
                    SetUIImage(icon,itemData.icon_b,false)

                    RemoveUIComponentEventCallback(btn,UEUI.Button)
                    AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
                        if itemData.type_use == ItemUseType.BagHeroClothes then
                            local bagItemMoudle = BagItemModule.new({
                                id = itemData.id,
                                num = 1
                            })
                            UI_SHOW(UIDefine.UI_EquipmentItemTip, bagItemMoudle)
                        else
                            SetActive(self.ui.m_btnTip,true)
                            
                            local offset = index%5
                            local isShowLeft = offset == 3 or offset == 4 or offset == 0;
                            self.ui.m_goTipNode:SetActive(isShowLeft)
                            self.ui.m_goTipNode1:SetActive(not isShowLeft)
                            
                            self.ui.m_goTipNode.transform.position = btn.transform.position
                            self.ui.m_goTipNode1.transform.position = btn.transform.position
                            Log.Info(index%5)
                            local count = BagManager:GetBagItemCount(v2n(n.item_id));
                            local tipStr = "<color=#"..self:GetQualityTipHex(itemData.slg_quality)..">"..LangMgr:GetLang(itemData.id_lang).."</color>\n".."<color=#001f33>"..LangMgr:GetLang(70000258)..NumToGameString(count).."\n"..LangMgr:GetLang(itemData.explain).."</color>"
                            
                            self.ui.m_txtTipTxt.text = tipStr
                            self.ui.m_txtTipTxt1.text = tipStr
                        end
                    end)
                end
            end
            SetActive(obj,true)
        end
        SetUIForceRebuildLayout(content)
    end
end

--判断某奖励是否可以掉落
function UI_LotteryDesc:CheckUnlock(group_id,is_get,get_id)
    if not self.ratioConfig[tonumber(group_id)] then
        return false
    end
    
    local canAdd = true
    --判断是否需要剔除，如果未获得该英雄则不可掉落
    if v2n(is_get) == 1 and get_id then
        local isExist = HeroManager:IsHeroActive(get_id)
        if not isExist then
            canAdd = false
        end
    end
    return canAdd
end

--计算行数
function UI_LotteryDesc:CalculateRowCount(rowCount,countSum)
    if countSum <= rowCount then
        return 1
    else
        local count1,count2 = math.modf(countSum/5)
        if count2 > 0 then
            count1 = count1 + 1
        end
        return count1
    end
end

--保留几位小数
function UI_LotteryDesc:KeepDotNumber(count,number)
    return tonumber(string.format("%."..count.."f",number)) or 0
end

--获取提示描述颜色码
function UI_LotteryDesc:GetQualityTipHex(quality)
    local hex = "0dc42b"
    if quality == 1 then
        hex = "0dc42b"
    elseif quality == 2 then
        hex = "299ee1"
    elseif quality == 3 then
        hex = "c129e1"
    elseif quality == 4 then
        hex = "e59123"
    end
    return hex
end

return UI_LotteryDesc