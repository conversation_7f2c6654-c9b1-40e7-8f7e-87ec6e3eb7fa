﻿using System;
using UnityEngine;
using System.Collections.Generic;
using DeletegateCall;
//An example implementation of a class that communicates with a TableView
public class CCTableViewController : MonoBehaviour, ITableViewDataSource
{
    public CCTableView m_tableView;
    public int m_numRows;
    private int m_numInstancesCreated = 0;

    public FuncCount GetCellCount;
    public FuncSize GetCellSize;   
    public FuncUpdateCell UpdateCell;
    public FuncLoadFinish LoadFinish;
    public FuncHideCell HideCell;
    public FuncUpdateCellData UpdateCellData;

    private float defaultHeight = 0.0f;
    private Dictionary<int, float> m_customRowHeights;

    private void OnDestroy()
    {
        GetCellCount = null;
        GetCellSize = null;
        UpdateCell = null;
        LoadFinish = null;
        HideCell = null;
        UpdateCellData = null;
        m_customRowHeights = null;
        m_tableView.dataSource = null;
    }

    public void Awake()
    {        
        m_customRowHeights = new Dictionary<int, float>();
        m_tableView.dataSource = this;
    } 

    #region ITableViewDataSource

    //Will be called by the TableView to know how many rows are in this table
    public int GetNumberOfRowsForTableView( CCTableView tableView )
    {      
        if(GetCellCount != null )        
            m_numRows = GetCellCount.Invoke(tableView);        
        return m_numRows;
    }

    //Will be called by the TableView to know what is the height of each row
    public Vector2 GetSizeForRowInTableView( CCTableView tableView, int row )
    {
        Vector2 value = Vector2.zero;
        if ( GetCellSize != null)        
            value = GetCellSize.Invoke(tableView, row);        
        return value;
    }

    //Will be called by the TableView when a cell needs to be created for display
    public CCTableViewCell GetCellForRowInTableView( CCTableView tableView, int row )
    {
        CCTableViewCell cell = null;
        if ( UpdateCell != null )
            cell = UpdateCell.Invoke(tableView, row);
        return cell;
    }

    public void HideCellForVisibleCells( CCTableViewCell tableViewCell, int row )
    {
        if ( HideCell != null)
        {
            HideCell.Invoke(tableViewCell, row);
        }
    }

    public void ReloadFinished( CCTableView tableView )
    {
        if ( LoadFinish != null)
            LoadFinish.Invoke(tableView);
    }

    #endregion ITableViewDataSource

    private float GetHeightOfRow(int row)
    {
        if (m_customRowHeights.ContainsKey(row))
        {
            return m_customRowHeights[row];
        }
        else
        {
            return defaultHeight;
        }
    }

    public void OnCellHeightChanged(int row, float newHeight)
    {
        if (GetHeightOfRow(row) == newHeight)
        {
            return;
        }
        //Debug.Log(string.Format("Cell {0} height changed to {1}", row, newHeight));
        m_customRowHeights[row] = newHeight;
        m_tableView.NotifyCellDimensionsChanged(row);
    }

    public void ReloadData(bool isClear = false)
    {
        m_tableView.ReloadData(isClear);
    }

    public void ShowCenter(int index, Action call)
    {
        m_tableView.ShowCenter(index,call);
    }

    public void ShowBottom( Action call )
    {
        m_tableView.ShowBottom(call);
    }

    public CCTableViewCell GetCellAtRow(int row)
    {
        CCTableViewCell cell = m_tableView.GetCellAtRow(row);       
        
        if (cell == null || cell.gameObject.name == "TopPadding" || cell.gameObject.name == "Bottom")        
            return null;        

        return cell;
    }

    public void updateTableView()
    {
        if ( null == UpdateCellData)
        {
            Debug.Log("updateCellData is null in updateTableView");
            return;
        }
        Transform contentTrans = m_tableView.transform.GetChild(0);
        int count = contentTrans.childCount;        
        foreach ( CCTableViewCell cell in contentTrans.GetComponentsInChildren<CCTableViewCell>() )
        {
            if ( null == cell )            
                continue;
            int index = int.Parse(cell.name);
            UpdateCellData.Invoke(cell, index);         
        }
    }
}