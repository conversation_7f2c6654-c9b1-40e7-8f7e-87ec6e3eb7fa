local UI_UnionScienceUpgrade = Class(BaseView)

function UI_UnionScienceUpgrade:OnInit()
    self.isTimer = false;
    self.addTime = 0;
    self.checkTime = 1;
    self.isDonate1 = false;
    self.isDonate2 = false;
    self.isRefresh = false;

    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
    EventMgr:Add(EventID.CHANGE_RESOURCE, self.ChangeResource, self);
end

function UI_UnionScienceUpgrade:OnCreate(param)
    self.groupId = param;

    self.ui.m_txtContribute.text = NetUpdatePlayerData:GetResourceNumByID(ItemID.TUANDUIGONGXIAN);

    self:OnUpdateUI();
    self:SetIsUpdateTick(true);
end

function UI_UnionScienceUpgrade:OnRefresh(param)
    if param == 1 then
        self:OnUpdateUI();
    end
end

function UI_UnionScienceUpgrade:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
    EventMgr:Remove(EventID.CHANGE_RESOURCE, self.ChangeResource, self);

    if self.isRefresh then
        self.isRefresh = false;
        UI_UPDATE(UIDefine.UI_UnionScience, 1);
    end
end

function UI_UnionScienceUpgrade:onUIEventClick(go, param)
    local name = go.name
    if name == "m_btnClose" then
        self:Close();
    elseif name == "tipBtn" then
        UI_SHOW(UIDefine.UI_UnionSciencePreview, self.data.group_id, self.data.lv);
    elseif name == "m_imgSign" then
        local myDuty = LeagueManager:GetMyLeagueDuty();
        local pos_config = LeagueManager:GetUnionPosById(myDuty);
        if pos_config and pos_config.tech_recommend == 1 then
            if self.groupId ~= LeagueManager:GetScienceRecommend(self.data.tech_tag) then
                LeagueManager:SetScienceRecommend(self.data.group_id, self.data.tech_tag, function()
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9455));
                    self:OnUpdateUI();
                end);
            else
                LeagueManager:SetScienceRecommend(0, self.data.tech_tag, function()
                    self:OnUpdateUI();
                end);
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9451));
        end
        self.isRefresh = true;
    elseif name == "m_btnDiamondCost" then
        if self.isDonate2 then
            return
        end

        local remainNum = v2n(LeagueManager:GetConfigById(28)) - LeagueManager.donate_2_num;
        if remainNum > 0 then
            local cost = string.split(LeagueManager:GetConfigById(29), "|");
            local itemId = v2n(cost[1]);
            local needCount = v2n(cost[2]) + LeagueManager.donate_2_num * v2n(LeagueManager:GetConfigById(30));
            local hasCount = NetUpdatePlayerData:GetResourceNumByID(itemId);

            local function OnSure()
                if hasCount >= needCount then
                    self.isDonate2 = true;
                    local recordDic = { id = self.data.id, level = self.data.lv, exp = LeagueManager:GetScienceExp(self.data.group_id),
                                        itemId = itemId, needCount = needCount };
                    LeagueManager:SetScienceDonate(self.data.id, 2, function(data)
                        if data then
                            NetUpdatePlayerData:AddResourceNumByID(itemId, -needCount, nil, "UI_UnionScienceUpgrade");
                            self:OnDonateResult(data, recordDic, 2);
                        end
                        self.isDonate2 = false;
                    end);
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(9436, ItemConfig:GetLangByID(itemId)));
                end
            end

            if UIMgr:GetOnly("UI_UnionScienceUpgrade") then
                OnSure();
            else
                UI_SHOW(UIDefine.UI_TipsOnly, "UI_UnionScienceUpgrade", LangMgr:GetLangFormat(9437, ItemConfig:GetLangByID(itemId)), OnSure);
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9429));
        end
        self.isRefresh = true;
    elseif name == "m_btnContributeCost" then
        if self.isDonate1 then
            return
        end

        if LeagueManager.donate_1_num > 0 then
            local cost = string.split(self.data.tech_cost, "|");
            local itemId = v2n(cost[1]);
            local needCount = v2n(cost[2]);
            local hasCount = NetUpdatePlayerData:GetResourceNumByID(itemId);
            if hasCount >= needCount then
                self.isDonate1 = true;
                local recordDic = { id = self.data.id, level = self.data.lv, exp = LeagueManager:GetScienceExp(self.data.group_id),
                                    itemId = itemId, needCount = needCount };
                LeagueManager:SetScienceDonate(self.data.id, 1, function(data)
                    if data then
                        NetUpdatePlayerData:AddResourceNumByID(itemId, -needCount, nil, "UI_UnionScienceUpgrade");
                        self:OnDonateResult(data, recordDic, 1);
                    end
                    self.isDonate1 = false;
                end);
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(9436, ItemConfig:GetLangByID(itemId)));
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(5201));
        end
        self.isRefresh = true;
    elseif name == "m_imgReward1" then
        local reward = string.split(self.data.tech_reward, "|");
        local itemId = v2n(reward[1]);
        UI_SHOW(UIDefine.UI_ItemTips, itemId);
    elseif name == "m_imgReward2" then
        UI_SHOW(UIDefine.UI_TipsTop, LangMgr:GetLang(9448));
    end
end

function UI_UnionScienceUpgrade:TickUI(deltaTime)
    if self.isTimer then
        self.addTime = self.addTime + deltaTime;
        if self.addTime >= 1 then
            self.addTime = self.addTime - 1;
            self.time = self.time - 1;
            if self.time <= 0 then
                self.time = 0;
                if self.checkTime == 0 then
                    self.checkTime = 3;
                    LeagueManager:GetScienceData(function()
                        if self.ui then
                            self:OnUpdateUI();
                        end
                    end)
                else
                    self.checkTime = self.checkTime - 1;
                end
            end
            self.ui.m_txtTime.text = LangMgr:GetLangFormat(9389, TimeMgr:ConverSecondToString(self.time));
        end
    end
end

function UI_UnionScienceUpgrade:ChangeResource(type, num, changeValue, isTop)
    if type == PlayerDefine.Tuanduigongxian then
        if not self.ui then
            return ;
        end
        self.ui.m_doContribute:DORestart()
        AddDOTweenNumberDelay(num - changeValue, num, 0.3, 0.8, function(value)
            if self.ui then
                self.ui.m_txtContribute.text = math.floor(value);
            end
        end)
    end
end

function UI_UnionScienceUpgrade:OnUpdateUI()
    self.data = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, LeagueManager:GetScienceId(self.groupId));
    self.ui.m_txtTitle.text = LangMgr:GetLang(self.data.tech_name);

    if self.data.lv == self.data.max_lv then
        self.ui.m_txtLevel.text = GetStrRichColor(LangMgr:GetLang(9379), "55ff26");
    else
        if self.data.lv == 0 then
            self.ui.m_txtLevel.text = LangMgr:GetLangFormat(9384, GetStrRichColor(self.data.lv, "ff8888"), self.data.max_lv);
        else
            self.ui.m_txtLevel.text = LangMgr:GetLangFormat(9384, GetStrRichColor(self.data.lv, "55ff26"), self.data.max_lv);
        end
    end

    self.ui.m_txtDesc.text = LangMgr:GetLang(self.data.tech_sketch);
    SetImageSprite(self.ui.m_imgItem, self.data.tech_icon, false);

    local state;
    if self.data.lv == 0 then
        if self.data.pre_id then
            local idList = string.split(self.data.pre_id, ",");
            local count = #idList;
            local len = 3;
            local item;
            local unlockNum = 0;
            for i = 1, len do
                item = self.ui["m_goCond" .. i];
                if i <= count then
                    local showTxt = GetChild(item, "showTxt", UEUI.Text);
                    local yesSp = GetChild(item, "yesSp", UEUI.Image);
                    local goBtn = GetChild(item, "goBtn");

                    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, idList[i]);
                    local isUnlock = config.lv <= LeagueManager:GetScienceLv(config.group_id);
                    local nameStr = GetStrRichColor(LangMgr:GetLang(config.tech_name), "87fbff");
                    if isUnlock then
                        showTxt.text = LangMgr:GetLangFormat(9392, nameStr, GetStrRichColor(config.lv, "55ff26"));
                        unlockNum = unlockNum + 1;
                    else
                        showTxt.text = LangMgr:GetLangFormat(9392, nameStr, GetStrRichColor(config.lv, "ff8484"));

                        RemoveUIComponentEventCallback(goBtn, UEUI.Button);
                        AddUIComponentEventCallback(goBtn, UEUI.Button, function(arg1, arg2)
                            UI_UPDATE(UIDefine.UI_UnionScience, 2, config.group_id);
                            self:Close();
                        end);
                    end
                    SetActive(yesSp, isUnlock);
                    SetActive(goBtn, not isUnlock);
                end
                SetActive(item, i <= count);
            end
            state = unlockNum >= count and 1 or 0;
        else
            state = 1;
        end
    elseif self.data.lv == self.data.max_lv then
        state = 2;
    else
        state = 1;
    end

    if state == 2 then
        self.ui.m_txtMaxLv.text = LangMgr:GetLangFormat(9385, self.data.lv);
        self.ui.m_txtNextProp.text = self.data.tech_lvup_desc_2;
    else
        self.ui.m_txtCurLv.text = LangMgr:GetLangFormat(9385, self.data.lv);
        --if self.data.lv == 0 then
        --    self.ui.m_txtCurProp.text = GetStrRichColor(self.data.tech_lvup_desc_2, "ffffff");
        --else
            self.ui.m_txtCurProp.text = self.data.tech_lvup_desc_2;
        --end

        local nextConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.union_tech_lv, self.data.next_id);
        self.ui.m_txtNextLv.text = LangMgr:GetLangFormat(9385, nextConfig.lv);
        self.ui.m_txtNextProp.text = nextConfig.tech_lvup_desc_2;

        if state == 1 then
            local progress = LeagueManager:GetScienceExp(self.data.group_id) / self.data.tech_exp;
            if progress > 1 then
                progress = 1;
            end
            SetUISize(self.ui.m_imgProgress, 615 * progress, 29);
            self.ui.m_txtProgress.text = LeagueManager:GetScienceExp(self.data.group_id) .. "/" .. self.data.tech_exp;

            local reward = string.split(self.data.tech_reward, "|");
            SetImageSprite(self.ui.m_imgReward1, ItemConfig:GetIcon(v2n(reward[1])), true);
            self.ui.m_txtReward1.text = "X" .. reward[2];
            self.ui.m_txtReward2.text = "X" .. self.data.tech_reward_exp;

            local limit = v2n(LeagueManager:GetConfigById(28));
            local remainNum = limit - LeagueManager.donate_2_num;
            self.ui.m_txtDiamondTime.text = LangMgr:GetLangFormat(9388, remainNum);

            limit = v2n(LeagueManager:GetConfigById(26));
            self.ui.m_txtContributeTime.text = LangMgr:GetLangFormat(9387, LeagueManager.donate_1_num, limit);

            local cost = string.split(LeagueManager:GetConfigById(29), "|");
            SetImageSprite(self.ui.m_imgDiamondCost, ItemConfig:GetIcon(v2n(cost[1])), false);
            self.ui.m_txtDiamondCost.text = v2n(cost[2]) + LeagueManager.donate_2_num * v2n(LeagueManager:GetConfigById(30));

            cost = string.split(self.data.tech_cost, "|");
            SetImageSprite(self.ui.m_imgContributeCost, ItemConfig:GetIcon(v2n(cost[1])), false);
            self.ui.m_txtContributeCost.text = cost[2];

            self.isTimer = LeagueManager.donate_1_num < limit and LeagueManager.donate_1_at > 0;
            if self.isTimer then
                self.checkTime = 0;
                self.time = LeagueManager.donate_1_at - TimeMgr:GetServerTime() + v2n(LeagueManager:GetConfigById(27));
                self.ui.m_txtTime.text = LangMgr:GetLangFormat(9389, TimeMgr:ConverSecondToString(self.time));
            end
            SetActive(self.ui.m_goTime, self.isTimer);

            local isGray = remainNum <= 0;
            SetUIImageGray(self.ui.m_btnDiamondCost, isGray);
            -- SetUIImageGray(self.ui.m_imgDiamondCost, isGray);
            -- SetUITextGray(self.ui.m_txtDiamondCost, isGray);

            isGray = LeagueManager.donate_1_num <= 0;
            SetUIImageGray(self.ui.m_btnContributeCost, isGray);
            -- SetUIImageGray(self.ui.m_imgContributeCost, isGray);
            -- SetUITextGray(self.ui.m_txtContributeCost, isGray);
        end
    end
    self.ui.m_txtProp.text = LangMgr:GetLang(self.data.tech_lvup_desc_1);

    local isRecommend = self.groupId == LeagueManager:GetScienceRecommend(self.data.tech_tag);
    if isRecommend then
        self.ui.m_txtAdd.text = LangMgr:GetLangFormat(9450, LeagueManager:GetConfigById(31));
    else
        self.ui.m_txtAdd.text = "";
    end
    SetUIImageGray(self.ui.m_imgSign, not isRecommend);

    local myDuty = LeagueManager:GetMyLeagueDuty();
    local pos_config = LeagueManager:GetUnionPosById(myDuty);
    if pos_config and pos_config.tech_recommend == 1 then
        SetActive(self.ui.m_imgSign, state == 1);
    else
        SetActive(self.ui.m_imgSign, state == 1 and isRecommend);
    end

    SetActive(self.ui.m_goDefault, state ~= 2);
    SetActive(self.ui.m_goLock, state == 0);
    SetActive(self.ui.m_goUpgrade, state == 1);
    SetActive(self.ui.m_goMax, state == 2);
end

function UI_UnionScienceUpgrade:OnDonateResult(data, recordDic, _type)
    if recordDic.id == v2n(data.tech_id) and recordDic.level == v2n(data.level) and recordDic.exp == v2n(data.exp) then
        Log.Error("科技捐献返回数据重复");
        return ;
    end

    local critNum = data.times;
    if critNum > 1 then
        if _type == 1 then
            SetUIPos(self.ui.m_txtCrit, -260, -197);
        else
            SetUIPos(self.ui.m_txtCrit, 260, -197);
        end

        self.ui.m_txtCrit.text = LangMgr:GetLangFormat(9394, critNum);
        self:CreateScheduleFun(function()
            SetActive(self.ui.m_txtCrit, false);
        end, 1, 1);
    end
    SetActive(self.ui.m_txtCrit, critNum > 1);

    local addNum = 1;
    if self.groupId == LeagueManager:GetScienceRecommend(self.data.tech_tag) then
        addNum = addNum + v2n(LeagueManager:GetConfigById(31)) / 100;
    end

    local reward = string.split(self.data.tech_reward, "|");
    local itemId = v2n((reward[1]));
    local count = math.floor(v2n(reward[2]) * addNum);
    local flyPos = UIMgr:GetObjectScreenPos(self.ui.m_imgReward1.transform);
    MapController:AddResourceBoomAnim(flyPos.x, flyPos.y, itemId, count);
    NetUpdatePlayerData:AddResource(PlayerDefine[itemId], count, nil, nil, "UI_UnionScienceUpgrade");

    for i = 1, 6 do
        local go = CreateGameObjectWithParent(self.ui.m_imgReward2.gameObject, self.uiGameObject);
        SetUIPos(go, -32.4, -146);

        local time = 0.3 + i * 0.1;
        DOScale(go.transform, Vector3(0.2, 0.2, 0.2), time)
        DOLocalMove(go.transform, Vector3(-90, 0, 0), time, function()
            UEGO.Destroy(go);
        end);
    end

    self:OnUpdateUI();

    UI_UPDATE(UIDefine.UI_MainFace, 50);
    UI_UPDATE(UIDefine.UI_Union, 1, 4);
    UI_UPDATE(UIDefine.UI_UnionMain, 2);

    -- 数数上报            
    local thinkTable = {
        ["Team_TechID"] = recordDic.id, -- 捐献科技ID
        ["Team_TechOldlv"] = recordDic.level, -- 捐献前科技等级
        ["Team_TechNewlv"] = data.level, -- 捐献后科技等级
        ["Team_TechOldProgress"] = recordDic.exp, -- 捐献前科技进度
        ["Team_TechNewProgress"] = data.exp, -- 捐献后科技进度
        ["Team_TechUsetype"] = recordDic.itemId, -- 捐献消耗货币类型
        ["Team_TechUsenum"] = recordDic.needCount, -- 捐献消耗货币数量
        ["Team_Techbet"] = data.times, -- 捐献倍数
        ["Team_TechAward"] = string.format("{%s|%s}", itemId, count); -- 捐献个人奖励
    };
    SdkHelper:ThinkingTrackEvent(ThinkingKey.Team_Tech, thinkTable);
end

return UI_UnionScienceUpgrade