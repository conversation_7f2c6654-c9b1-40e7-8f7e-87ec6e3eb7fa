local UI_CollectCardResolve = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local SeriesItem = Class(ItemBase)

local RepeatCardList = {}  -- 重复卡牌列表
local CurrentSelectCardList = {}  -- 当前选中的卡牌列表
local CardPoint = {}
local IsAutoSelect = false  -- 是否自动选择
local PointPos
local IsPush
local effectMask

function UI_CollectCardResolve:OnInit()
    effectMask = self.ui.m_goEffectMask
end

function UI_CollectCardResolve:OnCreate(isPush)
    self.isPush = isPush
    IsPush = isPush
    CurrentSelectCardList = {}
    CardPoint = {}
    PointPos = UIRectPosFit(self.ui.m_goPointPos)
    IsAutoSelect = NetCollectCardData:GetDataByKey("isAutoSelect")
    if self.isPush then
        IsAutoSelect = true
        SetActive(self.ui.m_goOver, true)
        SetActive(self.ui.m_togAutoSelect, false)
        SetActive(self.ui.m_btnClose, false)
    end
    self:InitPanel()
    self:RefreshPanel()
    self:RefreshScroll()
end

function UI_CollectCardResolve:OnRefresh(type)
    -- 刷新滚动视图
    if type == 1 then
        self:RefreshScroll()
    -- 刷新滚动视图（不重置列表）
    elseif type == 2 then
        self:RefreshScroll(true)
    end
    self:RefreshPanel()
end

function UI_CollectCardResolve:onDestroy()
    Tween.Kill("AutoMoveFunc")
    if self.seriesList then
        for i = 1, #self.seriesList do
            self.seriesList[i]:onDestroy()
        end
    end
    self.seriesList = nil
    self.seriesData = nil
    RepeatCardList = nil
    CurrentSelectCardList = nil
    CardPoint = nil
    IsAutoSelect = nil
    IsPush = nil

    if self.isPush then
        local _, activityItem = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.CollectCard)
        if activityItem then
            activityItem:CloseActive()
            CollectionItems:ReflushMainPoint()
        end
        NetPushViewData:RemoveViewByIndex(PushDefine.UI_CollectCardResolve)
        NetPushViewData:CheckOtherView(true)
        CollectCardManager:ClearTable()
    end
end

function UI_CollectCardResolve:onUIEventClick(go,param)
    local name = go.name
    -- 关闭
    if name == "m_btnClose" then
        self:Close()
    -- 确定分解
    elseif name == "m_btnResolve" then
        self:ResolveCard()
    -- 跳过
    elseif name == "m_btnSkip" then
        self:Close()
    end
end

function UI_CollectCardResolve:AutoClose()
    if self.isPush then
        return
    end
    self:Close()
end

--- 初始化界面
function UI_CollectCardResolve:InitPanel()
    -- 初始化 TableViewD
    self:InitTableViewD()
    -- 刷新重复卡牌列表
    self:RefreshRepeatCardList()

    self.ui.m_togAutoSelect.onValueChanged:AddListener(function (isOn)
        IsAutoSelect = isOn
        NetCollectCardData:SetDataByKey("isAutoSelect", IsAutoSelect)
        -- 自动选择
        if IsAutoSelect then
            for cardID, repeatNum in pairs(RepeatCardList) do
                CurrentSelectCardList[cardID] = repeatNum
            end
        else
            for cardID, _ in pairs(RepeatCardList) do
                CurrentSelectCardList[cardID] = 0
            end
        end
        UI_UPDATE(UIDefine.UI_CollectCardResolve, 2)
    end)
    self.ui.m_togAutoSelect.isOn = IsAutoSelect
end



--- 刷新重复卡牌列表
function UI_CollectCardResolve:RefreshRepeatCardList()
    RepeatCardList = {}
    local config = NetCollectCardData:GetRepeatCardSeries()
    if config then
        for _, series in ipairs(config) do
            local cardList = string.split(series.card, "|")
            for _, cardID in ipairs(cardList) do
                local num = NetCollectCardData:GetCardNum(cardID)
                if num > 1 then
                    RepeatCardList[cardID] = num - 1
                end
            end
        end
    end
end

--- 刷新界面
function UI_CollectCardResolve:RefreshPanel()
    -- 确定按钮状态
    local isEmpty = IsTableEmpty(CurrentSelectCardList)
    local hasNum = false
    for _, value in pairs(CurrentSelectCardList) do
        if value > 0 then
            hasNum = true
            break
        end
    end
    local hasSelect = not isEmpty and hasNum
    SetActive(self.ui.m_btnResolve, hasSelect)
    SetActive(self.ui.m_btnNotSelect, not hasSelect)

    if self.isPush then
        if not hasSelect then
            SetActive(self.ui.m_btnNotSelect, false)
            SetActive(self.ui.m_btnSkip, true)
        end
    end

    local point = 0
    for cardID, num in pairs(CurrentSelectCardList) do
        if num > 0 then
            -- 卡牌点数
            local cardPoint = NetCollectCardData:GetCardStar(cardID)
            point = point + cardPoint * num
        end
    end

    self.ui.m_txtExchangePoint.text = point
    self.ui.m_txtPoint.text = NetCollectCardData:GetPoint()
end

--- 刷新滚动视图
--- @param isNotReset boolean|nil 是否不重置列表
function UI_CollectCardResolve:RefreshScroll(isNotReset)
    local config = NetCollectCardData:GetRepeatCardSeries()
    if config then
        self.seriesData = config
        -- 不重置列表时，只更新数据
        if isNotReset then
            -- 有收藏册内容为空，刷新列表
            if HasSeriesEmpty then
                self.ui.m_TableViewD:ReloadData()
                HasSeriesEmpty = false
            else
                self.ui.m_TableViewD:ReloadData()
            end
        else
            self.ui.m_TableViewD:ReloadData()
        end
    end

    -- 没有可分解的重复卡牌
    local isEmpty = IsTableEmpty(config)
    SetActive(self.ui.m_goNoCard, isEmpty)
end

--- 分解卡牌
function UI_CollectCardResolve:ResolveCard()
    local isEmpty = IsTableEmpty(CurrentSelectCardList)
    local hasNum = false
    for _, value in pairs(CurrentSelectCardList) do
        if value > 0 then
            hasNum = true
            break
        end
    end
    local hasSelect = not isEmpty and hasNum
    if not hasSelect then
        return
    end

    CardPoint = {}

    local point = 0
    for cardID, num in pairs(CurrentSelectCardList) do
        if num > 0 then
            -- 卡牌点数
            local cardPoint = NetCollectCardData:GetCardStar(cardID)
            point = point + cardPoint * num
            -- 减少卡牌
            NetCollectCardData:ReduceCard(cardID, num, "ExchangePoint")
            -- 清空选中
            CurrentSelectCardList[cardID] = 0
            CardPoint[cardID] = cardPoint * num
        end
    end

    -- 获得点数
    NetCollectCardData:AddPoint(point)

    self.ui.m_TableViewD.enabled = false
    -- 播放卡牌碎裂特效
    -- 由于使用 TableViewD，无法直接访问所有 SeriesItem 实例
    -- 这里需要通过其他方式处理特效，或者保留一个 SeriesItem 列表
    -- 特效播完后分解卡牌
    local duration = 1.5
    TimeMgr:CreateTimer("UI_CollectCardResolve", function()
        UI_UPDATE(UIDefine.UI_CollectCardResolve, 1)
        UI_UPDATE(UIDefine.UI_CollectCardShop)
        UI_UPDATE(UIDefine.UI_CollectCardView)
        self.ui.m_TableViewD.enabled = true
        if self.isPush then
            local duration2 = 0.5
            TimeMgr:CreateTimer("UI_CollectCardResolve", function()
                self:Close()
            end, duration2, 1)
        end
    end, duration, 1)

    -- 刷新重复卡牌列表
    self:RefreshRepeatCardList()
end

function SeriesItem:OnInit(transform)
    self.trans            = transform
    self.bg               = GetChild(transform, "bg", UE.RectTransform)
    self.title            = GetChild(transform, "bg/Image/text", UEUI.Text)
    self.imgSeries        = GetChild(transform, "bg/Image/Image/imgSeries", UEUI.Image)
    self.sliderProgess    = GetChild(transform, "bg/Image/Image/normal/SliderBg/sliderProgess", UEUI.Slider)
    self.txtProgress      = GetChild(transform, "bg/Image/Image/normal/SliderBg/txtProgress", UEUI.Text)
    self.rewardContent    = GetChild(transform, "bg/reward/scrollview/viewport/content")
    self.rewardItem       = GetChild(transform, "bg/reward/rewardItem")
    self.cardContent      = GetChild(transform, "bg/card/scrollview/viewport/content")
    self.cardItem         = GetChild(transform, "bg/card/cardItem")
    self.effect           = GetChild(self.cardItem, "item/effect")
    self.cachedHeight     = 750
    CollectCardManager:SetMaskSize(self.effect,effectMask)
    self.effectList = {}
end

function SeriesItem:UpdateData(data, index)
    if not data then return end
    self.data = data
    self.index = index

    local config = CollectCardManager:GetCardCollectionItemConfig(data.id)
    if config then
        self.title.text = LangMgr:GetLang(config.lang_id)
    end

    SetUIImage(self.imgSeries, data.picture, true)

    local currentNum, totalNum = NetCollectCardData:GetCardNumOfSeries(data.id)
    self.sliderProgess.value = currentNum / totalNum
    self.txtProgress.text = string.format("%s/%s", currentNum, totalNum)

    -- 生成收藏册奖励
    local rewardStr = NetSeasonActivity:GetChangeItemId(data.reward)
    local rewardList = string.split(rewardStr, ";")
    local rewardItemCount = self.rewardContent.transform.childCount
    -- 先全部隐藏
    for i = 1, rewardItemCount, 1 do
        local item = self.rewardContent.transform:GetChild(i - 1)
        SetActive(item, false)
    end
    -- 再根据奖励列表显示
    for key, value in ipairs(rewardList) do
        local rewardTable = string.split(value, "|")
        local itemID = v2n(rewardTable[1])
        local itemNum = v2n(rewardTable[2])

        local item
        -- 有可用的 item 直接获取
        if key <= rewardItemCount then
            item = self.rewardContent.transform:GetChild(key - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(self.rewardItem, self.rewardContent)
        end
        -- 图标
        local icon = GetChild(item, "icon", UEUI.Image)
        local iconPath = ItemConfig:GetIcon(itemID)
        SetUIImage(icon, iconPath, false)
        -- 数量
        local textNum = GetChild(item, "num", UEUI.Text)
        textNum.text = "x" .. itemNum
        -- 按钮
        local button = GetChild(item, "icon", UEUI.Button)
        button.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_ItemTips, itemID)
        end)
        SetActive(item, true)
    end

    -- 生成卡牌
    local cardList = string.split(data.card, "|")
    local cardItemCount = self.cardContent.transform.childCount
    -- 先全部隐藏
    for i = 1, cardItemCount, 1 do
        local item = self.cardContent.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    local height
    local cardCount = 0

    self.effectList = {}
    -- 再根据卡牌列表显示
    for key, value in ipairs(cardList) do
        local cardID = v2n(value)
        local count = NetCollectCardData:GetCardNum(cardID)
        local countMax = count - 1
        -- 有重复卡牌
        local hasCard = count > 0
        local hasRepeatCard = count > 1
        local isGold = NetCollectCardData:IsGoldCard(cardID)
        -- 显示有重复的卡牌
        if hasRepeatCard then
            cardCount = cardCount + 1
            local cardItem
            -- 有可用的 item 直接获取
            if key <= cardItemCount then
                cardItem = self.cardContent.transform:GetChild(key - 1)
            -- item 不够用，创建新的
            else
                cardItem = CreateGameObjectWithParent(self.cardItem, self.cardContent)
            end
            local item = GetChild(cardItem, "item")

            local effect = GetChild(cardItem, "item/effect")
            SetActive(effect, false)

            local canvasGroup = GetChild(cardItem, "item", UE.CanvasGroup)
            canvasGroup.alpha = 1
            local effectObj = {
                effect = effect,
                cardID = cardID,
                canvasGroup = canvasGroup,
            }
            table.insert(self.effectList, effectObj)

            -- 背景
            local bg = GetChild(item, "bg", UEUI.Image)
            local titleBg = GetChild(item, "titleBg", UEUI.Image)
            -- 标题
            local title = GetChild(item, "titleBg/title", UEUI.Text)
            title.text = ItemConfig:GetLangByID(cardID)
            -- 图标
            local icon = GetChild(item, "icon", UEUI.Image)
            local iconPath = ItemConfig:GetIcon(cardID)
            SetUIImage(icon, iconPath, false)
            SetUIImageGray(icon, true)

            --星级
            local starIcon = GetChild(item, "starIcon", UEUI.Image)
            CollectCardManager:SetStar(cardID,starIcon)

            -- 数量
            local goNum = GetChild(item, "Num", UEUI.Image)
            local txtNum = GetChild(item, "Num/txtNum", UEUI.Text)
            txtNum.text = string.format("%s/%s", 0, countMax)
            -- 有重复卡牌
            SetActive(goNum, hasRepeatCard)
            -- 红点
            local redPoint = GetChild(item, "redPoint", UEUI.Image)
            local hasRedPoint = NetCollectCardData:GetRedPoint(cardID)
            if hasRedPoint then
                SetActive(redPoint, false)
            else
                SetActive(redPoint, false)
            end

            -- 减少按钮
            local btnReduce = GetChild(item, "btnReduce", UEUI.Button)
            -- 增加按钮
            local btnAdd = GetChild(item, "btnAdd", UEUI.Button)
            SetActive(btnReduce, not IsPush)
            SetActive(btnAdd, not IsPush)
            btnReduce.onClick:RemoveAllListeners()
            btnReduce.onClick:AddListener(function ()
                local cardIDStr = tostring(cardID)
                if not CurrentSelectCardList[cardIDStr] then
                    CurrentSelectCardList[cardIDStr] = 0
                end
                CurrentSelectCardList[cardIDStr] = math.max(CurrentSelectCardList[cardIDStr] - 1, 0)

                -- 刷新选中数量
                txtNum.text = string.format("%s/%s", CurrentSelectCardList[cardIDStr], countMax)

                -- 刷新加减按钮
                local hasSelect = CurrentSelectCardList[cardIDStr] > 0
                SetUIBtnGrayAndEnable(btnReduce, hasSelect)
                local isSelectMax = CurrentSelectCardList[cardIDStr] == countMax
                SetUIBtnGrayAndEnable(btnAdd, not isSelectMax)

                -- 刷新置灰状态
                if hasSelect then
                    if isGold then
                        SetUIImage(bg, "Sprite/ui_tujian/jika_ka2.png", false)
                    else
                        SetUIImage(bg, "Sprite/ui_tujian/jika_ka1.png", false)
                    end
                    SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title.png", false)
                    SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new.png", false)
                    SetUIImageGray(icon, false)
                    SetUIImageGray(goNum, false)
                    title.color = GetColorByHex("A24C04")
                    UnifyOutline(txtNum, "B3168C")
                else
                    SetUIImage(bg, "Sprite/ui_tujian/jika_ka1_hui.png", false)
                    SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title_hui.png", false)
                    SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new_hui.png", false)
                    SetUIImageGray(icon, true)
                    SetUIImageGray(goNum, true)
                    title.color = GetColorByHex("575757")
                    UnifyOutline(txtNum, "575757")
                end
                for starIndex, star in ipairs(starList) do
                    SetActive(star, starIndex <= cardStar)
                    if hasSelect then
                        SetUIImage(star, "Sprite/ui_tujian/jika_xiangqing_star.png", false)
                    else
                        SetUIImage(star, "Sprite/ui_tujian/jika_xiangqing_star_hui.png", false)
                    end
                end
                SetUIImageGray(starIcon,not hasSelect)
                -- 刷新界面
                UI_UPDATE(UIDefine.UI_CollectCardResolve)
            end)
            btnAdd.onClick:RemoveAllListeners()
            btnAdd.onClick:AddListener(function ()
                local cardIDStr = tostring(cardID)
                if not CurrentSelectCardList[cardIDStr] then
                    CurrentSelectCardList[cardIDStr] = 0
                end
                CurrentSelectCardList[cardIDStr] = math.min(CurrentSelectCardList[cardIDStr] + 1, countMax)

                -- 刷新选中数量
                txtNum.text = string.format("%s/%s", CurrentSelectCardList[cardIDStr], countMax)

                -- 刷新加减按钮
                local hasSelect = CurrentSelectCardList[cardIDStr] > 0
                SetUIBtnGrayAndEnable(btnReduce, hasSelect)
                local isSelectMax = CurrentSelectCardList[cardIDStr] == countMax
                SetUIBtnGrayAndEnable(btnAdd, not isSelectMax)

                -- 刷新置灰状态
                if hasSelect then
                    if isGold then
                        SetUIImage(bg, "Sprite/ui_tujian/jika_ka2.png", false)
                    else
                        SetUIImage(bg, "Sprite/ui_tujian/jika_ka1.png", false)
                    end
                    SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title.png", false)
                    SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new.png", false)
                    SetUIImageGray(icon, false)
                    SetUIImageGray(goNum, false)
                    title.color = GetColorByHex("A24C04")
                    UnifyOutline(txtNum, "B3168C")
                else
                    SetUIImage(bg, "Sprite/ui_tujian/jika_ka1_hui.png", false)
                    SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title_hui.png", false)
                    SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new_hui.png", false)
                    SetUIImageGray(icon, true)
                    SetUIImageGray(goNum, true)
                    title.color = GetColorByHex("575757")
                    UnifyOutline(txtNum, "575757")
                end

                SetUIImageGray(starIcon,not hasCard)
                -- 刷新界面
                UI_UPDATE(UIDefine.UI_CollectCardResolve)
            end)

            local cardIDStr = tostring(cardID)
            if not CurrentSelectCardList[cardIDStr] then
                CurrentSelectCardList[cardIDStr] = 0
            end

            -- 自动选择，刷新选中数量
            if IsAutoSelect then
                txtNum.text = string.format("%s/%s", countMax, countMax)
            else
                txtNum.text = string.format("%s/%s", CurrentSelectCardList[cardIDStr], countMax)
            end

            -- 刷新加减按钮
            local hasSelect = CurrentSelectCardList[cardIDStr] > 0
            SetUIBtnGrayAndEnable(btnReduce, hasSelect)
            local isSelectMax = CurrentSelectCardList[cardIDStr] == countMax
            SetUIBtnGrayAndEnable(btnAdd, not isSelectMax)

            -- 刷新置灰状态
            if hasSelect then
                if isGold then
                    SetUIImage(bg, "Sprite/ui_tujian/jika_ka2.png", false)
                else
                    SetUIImage(bg, "Sprite/ui_tujian/jika_ka1.png", false)
                end
                SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title.png", false)
                SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new.png", false)
                SetUIImageGray(icon, false)
                SetUIImageGray(goNum, false)
                title.color = GetColorByHex("A24C04")
                UnifyOutline(txtNum, "B3168C")
            else
                SetUIImage(bg, "Sprite/ui_tujian/jika_ka1_hui.png", false)
                SetUIImage(titleBg, "Sprite/ui_tujian/tujian_kabao_title_hui.png", false)
                SetUIImage(redPoint, "Sprite/ui_tujian/jika_xiangqingjiaobiao_new_hui.png", false)
                SetUIImageGray(icon, true)
                SetUIImageGray(goNum, true)
                title.color = GetColorByHex("575757")
                UnifyOutline(txtNum, "575757")
            end

            CollectCardManager:SetStar(cardID,starIcon)
            SetUIImageGray(starIcon,not hasSelect)
            SetActive(cardItem, true)
        end
    end

    height = cardCount < 6 and 475 or 710
    self:UpdateBgHeight(height)
end

function SeriesItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function SeriesItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

function SeriesItem:Refresh()
    self:UpdateData(self.data, self.index)
end

function SeriesItem:PlayEffect()
    for _, value in ipairs(self.effectList) do
        local cardID = value.cardID
        local num = CardPoint[tostring(cardID)]
        if num then
            -- 显示碎裂特效
            SetActive(value.effect, true)
            -- 卡牌渐隐
            DOFadeAlpha(value.canvasGroup, 1, 0, 0.2)
            -- 延迟飞星星
            local pos = UIRectPosFit(value.effect)
            local duration = 1.2
            TimeMgr:CreateTimer("UI_CollectCardResolve", function()
                MapController:FlyUIAnim(pos[1], pos[2], 60006, num, PointPos[1], PointPos[2])
            end, duration, 1)
        else
            SetActive(value.effect, false)
            value.canvasGroup.alpha = 1
        end
    end
end

function SeriesItem:onDestroy()
    UEGO.Destroy(self.trans.gameObject)
    self.trans = nil
    self.title = nil
    self.icon = nil
    self.rewardContent = nil
    self.rewardItem = nil
    self.cardContent = nil
    self.cardItem = nil
    self.data = nil
    self.index = nil
end

--隐藏该节点下所有子节点
function SeriesItem:HideAllChild(transform)
    local rewardItemCount = transform.childCount
    -- 先全部隐藏
    for i = 0, rewardItemCount-1 do
        local item = transform:GetChild(i)
        SetActive(item, false)
    end
end

function SeriesItem:UpdateBgHeight(height)
    SetUISize(self.bg, 940, height)
end

function UI_CollectCardResolve:InitTableViewD()

    SetActive(self.ui.m_goSeriesItem, false)

    -- 获取数据数量的回调
    self.ui.m_TableViewD.GetCellCount = function ()
        if self.seriesData then
            return #self.seriesData
        end
        return 0
    end

    -- 获取每个 cell 大小的回调
    self.ui.m_TableViewD.GetCellSize = function (_, index)
        return self:GetCellSize(index)
    end

    -- 更新 cell 内容的回调
    self.ui.m_TableViewD.UpdateCell = function (tableView, index)
        if not tableView then
            return nil
        end
        local cell = tableView:GetReusableCell()
        local item
        if not cell then
            -- 创建新的 cell
            item = UEGO.Instantiate(self.ui.m_goSeriesItem)
            SetActive(item, true)
            cell = GetAndAddComponent(item, CS.CCTableViewCell)
            item.name = v2s(index + 1)
        else
            item = cell.gameObject
        end

        -- 更新 cell 数据
        if self.seriesData and self.seriesData[index + 1] then
            -- 每次都创建新的 SeriesItem 实例，避免复用问题
            local seriesItem = SeriesItem.new()
            seriesItem:OnInit(item.transform)
            seriesItem:UpdateData(self.seriesData[index + 1], index + 1)
            self.ui.m_TableViewD.m_tableView:NotifyCellDimensionsChanged(index)
        end

        return cell
    end

    -- 更新现有 cell 数据的回调
    self.ui.m_TableViewD.UpdateCellData = function (cell, index)
        if cell and self.seriesData and self.seriesData[index + 1] then
            local item = cell.gameObject
            local seriesItem = SeriesItem.new()
            seriesItem:OnInit(item.transform)
            seriesItem:UpdateData(self.seriesData[index + 1], index + 1)
        end
    end

    -- 初始化完成后重新加载数据
    self.ui.m_TableViewD:ReloadData(true)
end

function UI_CollectCardResolve:GetCellSize(index)
    local height = 560  -- 默认高度

    if self.seriesData and self.seriesData[index + 1] then
        local data = self.seriesData[index + 1]
        local cardList = string.split(data.card, "|")
        local cardCount = 0
        for _, value in ipairs(cardList) do
            local cardID = v2n(value)
            local count = NetCollectCardData:GetCardNum(cardID)
            local hasRepeatCard = count > 1
            if hasRepeatCard then
                cardCount = cardCount + 1
            end
        end
        height = cardCount < 6 and 515 or 750
    end

    return Vector2.New(940, height)
end

return UI_CollectCardResolve