local UI_LotteryDescModel = {}

UI_LotteryDescModel.config = {["name"] = "UI_LotteryDesc", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_LotteryDescModel:Init(c)
    c.ui = {}    
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    c.ui.m_goContent = GetChild(c.uiGameObject,"bg/Scroll View/Viewport/m_goContent")
    c.ui.m_goPrefab = GetChild(c.uiGameObject,"bg/m_goPrefab")
    c.ui.m_goDescContainer = GetChild(c.uiGameObject,"bg/m_goPrefab/m_goDescContainer")
    c.ui.m_goDescGridItem = GetChild(c.uiGameObject,"bg/m_goPrefab/m_goDescGridItem")
    c.ui.m_goHeroPiece = GetChild(c.uiGameObject,"bg/m_goPrefab/m_goHeroPiece")
    c.ui.m_btnTip = GetChild(c.uiGameObject,"bg/m_btnTip",UEUI.Button)
    c.ui.m_goTipNode = GetChild(c.uiGameObject,"bg/m_btnTip/m_goTipNode")
    c.ui.m_txtTipTxt = GetChild(c.uiGameObject,"bg/m_btnTip/m_goTipNode/m_txtTipTxt",UEUI.Text)
    c.ui.m_goTipNode1 = GetChild(c.uiGameObject,"bg/m_btnTip/m_goTipNode1")
    c.ui.m_txtTipTxt1 = GetChild(c.uiGameObject,"bg/m_btnTip/m_goTipNode1/m_txtTipTxt1",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_LotteryDescModel