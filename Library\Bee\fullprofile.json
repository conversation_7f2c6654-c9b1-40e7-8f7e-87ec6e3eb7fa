{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 24240, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 24240, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 24240, "tid": 6, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 24240, "tid": 6, "ts": 1758072903965349, "dur": 562, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903968192, "dur": 602, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 24240, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 24240, "tid": 1, "ts": 1758072903904272, "dur": 5363, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24240, "tid": 1, "ts": 1758072903909637, "dur": 14891, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 24240, "tid": 1, "ts": 1758072903924534, "dur": 30065, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903968797, "dur": 224, "ph": "X", "name": "", "args": {}}, {"pid": 24240, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903903082, "dur": 2911, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903905994, "dur": 55405, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903909013, "dur": 1700, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903910716, "dur": 877, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911595, "dur": 139, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911738, "dur": 7, "ph": "X", "name": "ProcessMessages 20507", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911746, "dur": 28, "ph": "X", "name": "ReadAsync 20507", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911776, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911777, "dur": 11, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911791, "dur": 10, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911803, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911814, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911833, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911845, "dur": 13, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911860, "dur": 11, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911872, "dur": 12, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911885, "dur": 33, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911920, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911931, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911942, "dur": 11, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903911955, "dur": 47, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912005, "dur": 10, "ph": "X", "name": "ReadAsync 1300", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912016, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912028, "dur": 8, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912037, "dur": 10, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912049, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912061, "dur": 10, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912072, "dur": 10, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912084, "dur": 9, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912094, "dur": 9, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912105, "dur": 11, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912118, "dur": 13, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912132, "dur": 10, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912143, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912144, "dur": 9, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912154, "dur": 10, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912167, "dur": 10, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912178, "dur": 9, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912189, "dur": 8, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912199, "dur": 9, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912209, "dur": 10, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912221, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912251, "dur": 10, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912263, "dur": 10, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912274, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912291, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912304, "dur": 11, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912316, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912328, "dur": 13, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912342, "dur": 16, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912359, "dur": 9, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912370, "dur": 9, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912381, "dur": 12, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912394, "dur": 9, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912404, "dur": 9, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912415, "dur": 14, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912431, "dur": 10, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912443, "dur": 12, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912456, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912467, "dur": 10, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912479, "dur": 11, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912491, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912503, "dur": 13, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912517, "dur": 32, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912551, "dur": 10, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912563, "dur": 12, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912577, "dur": 12, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912591, "dur": 12, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912605, "dur": 11, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912618, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912629, "dur": 17, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912647, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912670, "dur": 11, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912683, "dur": 31, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912716, "dur": 10, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912727, "dur": 9, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912738, "dur": 10, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912749, "dur": 9, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912760, "dur": 10, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912771, "dur": 21, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912794, "dur": 8, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912804, "dur": 10, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912815, "dur": 12, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912829, "dur": 37, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912868, "dur": 10, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912880, "dur": 10, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912892, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912902, "dur": 9, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912913, "dur": 10, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912925, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912937, "dur": 11, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912950, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912962, "dur": 10, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912974, "dur": 11, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912987, "dur": 11, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903912999, "dur": 10, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913011, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913026, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913038, "dur": 10, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913049, "dur": 10, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913061, "dur": 12, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913075, "dur": 8, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913084, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913097, "dur": 10, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913109, "dur": 9, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913119, "dur": 10, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913130, "dur": 10, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913142, "dur": 10, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913154, "dur": 21, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913177, "dur": 10, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913188, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913200, "dur": 17, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913218, "dur": 13, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913233, "dur": 16, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913251, "dur": 8, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913260, "dur": 9, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913271, "dur": 11, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913284, "dur": 10, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913295, "dur": 12, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913308, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913321, "dur": 10, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913333, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913345, "dur": 8, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913355, "dur": 11, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913368, "dur": 10, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913379, "dur": 10, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913391, "dur": 10, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913403, "dur": 123, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913527, "dur": 11, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913539, "dur": 15, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913556, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913582, "dur": 10, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913593, "dur": 10, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913604, "dur": 12, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913618, "dur": 11, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913631, "dur": 13, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913646, "dur": 10, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913657, "dur": 11, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913669, "dur": 13, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913683, "dur": 10, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913695, "dur": 8, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913705, "dur": 12, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913718, "dur": 9, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913729, "dur": 10, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913740, "dur": 15, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913757, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913776, "dur": 10, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913787, "dur": 11, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913799, "dur": 20, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913820, "dur": 11, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913833, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913848, "dur": 16, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913865, "dur": 11, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913878, "dur": 15, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913894, "dur": 26, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913922, "dur": 17, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913940, "dur": 9, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913953, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913969, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913981, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903913992, "dur": 19, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914014, "dur": 14, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914029, "dur": 10, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914040, "dur": 7, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914049, "dur": 10, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914061, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914072, "dur": 11, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914085, "dur": 10, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914096, "dur": 15, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914113, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914125, "dur": 10, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914136, "dur": 12, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914150, "dur": 10, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914161, "dur": 10, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914173, "dur": 10, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914184, "dur": 11, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914197, "dur": 10, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914208, "dur": 9, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914219, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914230, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914241, "dur": 10, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914253, "dur": 319, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914573, "dur": 3, "ph": "X", "name": "ProcessMessages 7749", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914576, "dur": 11, "ph": "X", "name": "ReadAsync 7749", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914588, "dur": 10, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914600, "dur": 10, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914611, "dur": 16, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914629, "dur": 11, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914642, "dur": 11, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914654, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914666, "dur": 12, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914679, "dur": 10, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914691, "dur": 11, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914704, "dur": 11, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914717, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914736, "dur": 27, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914764, "dur": 9, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914775, "dur": 9, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914786, "dur": 11, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914798, "dur": 24, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914824, "dur": 10, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914836, "dur": 11, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914849, "dur": 10, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914860, "dur": 14, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914876, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914887, "dur": 12, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914901, "dur": 10, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914912, "dur": 10, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914924, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914936, "dur": 10, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914947, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914963, "dur": 9, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914973, "dur": 9, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914983, "dur": 10, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903914995, "dur": 10, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915006, "dur": 12, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915019, "dur": 10, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915031, "dur": 13, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915045, "dur": 9, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915056, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915067, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915091, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915114, "dur": 34, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915150, "dur": 10, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915162, "dur": 9, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915172, "dur": 13, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915186, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915201, "dur": 10, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915213, "dur": 28, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915242, "dur": 9, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915253, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915269, "dur": 10, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915280, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915292, "dur": 13, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915306, "dur": 11, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915318, "dur": 10, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915330, "dur": 10, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915342, "dur": 9, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915352, "dur": 11, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915365, "dur": 10, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915376, "dur": 12, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915390, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915407, "dur": 10, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915418, "dur": 10, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915429, "dur": 9, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915440, "dur": 9, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915450, "dur": 9, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915461, "dur": 14, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915476, "dur": 11, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915489, "dur": 9, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915500, "dur": 10, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915511, "dur": 10, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915522, "dur": 13, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915537, "dur": 456, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903915996, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916061, "dur": 2, "ph": "X", "name": "ProcessMessages 6360", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916107, "dur": 68, "ph": "X", "name": "ReadAsync 6360", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916176, "dur": 3, "ph": "X", "name": "ProcessMessages 8775", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916180, "dur": 22, "ph": "X", "name": "ReadAsync 8775", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916204, "dur": 12, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916218, "dur": 57, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916277, "dur": 42, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916321, "dur": 1, "ph": "X", "name": "ProcessMessages 1990", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916324, "dur": 21, "ph": "X", "name": "ReadAsync 1990", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916346, "dur": 31, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916379, "dur": 25, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916406, "dur": 12, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916419, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916433, "dur": 11, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916447, "dur": 13, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916462, "dur": 13, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916478, "dur": 15, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916495, "dur": 11, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916507, "dur": 12, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916522, "dur": 11, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916535, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916551, "dur": 11, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916564, "dur": 12, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916579, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916596, "dur": 14, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916612, "dur": 10, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916624, "dur": 24, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916649, "dur": 16, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916667, "dur": 140, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916809, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916822, "dur": 11, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916834, "dur": 11, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916848, "dur": 17, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916868, "dur": 11, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916880, "dur": 10, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916892, "dur": 11, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916905, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903916918, "dur": 472, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903917395, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903917488, "dur": 145, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903917635, "dur": 209, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903917846, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903917847, "dur": 455, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903918305, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903918308, "dur": 99, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903918408, "dur": 3, "ph": "X", "name": "ProcessMessages 1600", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903918414, "dur": 1601, "ph": "X", "name": "ReadAsync 1600", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903920018, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903920025, "dur": 28, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903920054, "dur": 2, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903920057, "dur": 3807, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903923868, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903923885, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903923905, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903923916, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924020, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924030, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924198, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924260, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924279, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924320, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924330, "dur": 233, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924565, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924577, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924590, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924600, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924612, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924673, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924683, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924729, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924740, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924934, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924942, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903924995, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925005, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925050, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925058, "dur": 870, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925929, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925944, "dur": 8, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903925953, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926014, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926024, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926042, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926051, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926074, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926081, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926093, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926101, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926116, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926124, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926132, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926139, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926165, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926175, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926184, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926191, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926222, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926230, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926291, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926299, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926424, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926432, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926441, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926448, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926492, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926501, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926567, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926575, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926647, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926658, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926679, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926688, "dur": 118, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926807, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926816, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926880, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926889, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926948, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926960, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903926969, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927025, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927034, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927045, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927055, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927063, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927078, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927087, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927096, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927119, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927128, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927166, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927175, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927186, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927200, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927210, "dur": 7, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927218, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927227, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927240, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927248, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927460, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927468, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927478, "dur": 11, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927490, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927498, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927507, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927576, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927585, "dur": 6, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927592, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927643, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927652, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927706, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927716, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927724, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927735, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927743, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927751, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927760, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927769, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927777, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927850, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927886, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927897, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927899, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927908, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927917, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927926, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927937, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927947, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927957, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927966, "dur": 8, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927976, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903927991, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928002, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928011, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928026, "dur": 8, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928036, "dur": 9, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928048, "dur": 10, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928059, "dur": 7, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928068, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928082, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928090, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928101, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928109, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928118, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928149, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928158, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928180, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928188, "dur": 8, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928197, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928207, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928215, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928234, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928246, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928272, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928346, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928348, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928383, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928384, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928404, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928422, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928445, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928447, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928471, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928511, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928524, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928539, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928585, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928598, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928663, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928674, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928685, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928711, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928725, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928737, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928747, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928796, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928810, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928830, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928841, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928867, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928880, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903928893, "dur": 119, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929013, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929024, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929040, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929051, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929092, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929104, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929112, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929128, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929135, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929157, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929166, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929209, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929219, "dur": 145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929366, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929379, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929402, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929410, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929455, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929465, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929557, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929565, "dur": 7, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929574, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929625, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929637, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929791, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929804, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929831, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903929838, "dur": 208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930048, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930061, "dur": 160, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930223, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930236, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930259, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930269, "dur": 216, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930487, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903930502, "dur": 26967, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903957475, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903957490, "dur": 166, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 24240, "tid": 12884901888, "ts": 1758072903957657, "dur": 3061, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903969022, "dur": 500, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 24240, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 24240, "tid": 8589934592, "ts": 1758072903901397, "dur": 53220, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 24240, "tid": 8589934592, "ts": 1758072903954619, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 24240, "tid": 8589934592, "ts": 1758072903954621, "dur": 862, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903969524, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 24240, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 24240, "tid": 4294967296, "ts": 1758072903883153, "dur": 78830, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 24240, "tid": 4294967296, "ts": 1758072903887458, "dur": 10017, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 24240, "tid": 4294967296, "ts": 1758072903961991, "dur": 1600, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 24240, "tid": 4294967296, "ts": 1758072903963629, "dur": 41, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903969528, "dur": 3, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758072903903840, "dur": 635, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903904482, "dur": 317, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903904883, "dur": 576, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903906027, "dur": 3529, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_A38B4233660E9CB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758072903910215, "dur": 901, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1758072903912782, "dur": 121, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1758072903913900, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1758072903915389, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1758072903915497, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1758072903905474, "dur": 10816, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903916297, "dur": 39538, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903955840, "dur": 93, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903955951, "dur": 67, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903956180, "dur": 699, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758072903905388, "dur": 10913, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903916359, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1758072903916313, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758072903916529, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903916827, "dur": 382, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758072903917214, "dur": 521, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903917735, "dur": 598, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758072903918658, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\UIPlugins\\HyperlinkText.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758072903918333, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903919725, "dur": 1712, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\ProjectGeneration\\ProjectGeneration.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758072903919619, "dur": 2359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903922001, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_PackageResourceImporter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758072903921978, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903922667, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903923357, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903923607, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903924011, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903924362, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903925485, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758072903926061, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758072903926476, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758072903926587, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758072903926842, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758072903927153, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903927254, "dur": 544, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UIEffect.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758072903927799, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903927887, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903928043, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903928209, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903928476, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903928784, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903928882, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903929206, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758072903929642, "dur": 26070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903905449, "dur": 10872, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903916362, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1758072903916326, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758072903916760, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903916889, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1758072903916846, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D8923AA791216057.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758072903917046, "dur": 713, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_D8923AA791216057.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758072903917994, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903918716, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\InjectionAnimator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758072903918256, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903919571, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903920203, "dur": 1695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_SpriteCharacterPropertyDrawer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758072903921898, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_SpriteAssetMenu.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758072903922554, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_FontAsset_CreationMenu.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758072903920202, "dur": 3152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903923354, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903923613, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903924006, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903924368, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903925314, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758072903925458, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758072903926194, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758072903926022, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758072903926421, "dur": 761, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903927183, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758072903927282, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758072903927589, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903927791, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903927857, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903928019, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903928208, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903928482, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903928778, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903928889, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903929206, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903929438, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758072903929651, "dur": 26060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903905405, "dur": 10907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903916358, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758072903916317, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903916767, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903916849, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903917031, "dur": 373, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1758072903917730, "dur": 599, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1758072903918330, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903919193, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758072903919268, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758072903919345, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758072903919546, "dur": 383, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758072903920123, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758072903920348, "dur": 1525, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758072903921874, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758072903922498, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758072903922763, "dur": 429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758072903918684, "dur": 4513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903923239, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903923400, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903923600, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903924013, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903924356, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903925486, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903925568, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903925961, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758072903925826, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903926189, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903926502, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903926593, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903926832, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903926888, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903926959, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758072903927026, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758072903927306, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903927421, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903927501, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903927593, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903927776, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903927867, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903928043, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903928217, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903928490, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903928785, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903928870, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903929213, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758072903929640, "dur": 26075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903905434, "dur": 10882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903916320, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758072903916402, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903916830, "dur": 560, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_2D9AC79B2BBA2E00.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758072903917464, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903917901, "dur": 916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903918968, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Primitives\\SpiralEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758072903918819, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903919852, "dur": 2260, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\ObjectExtension.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758072903922264, "dur": 806, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\UnityEditorInternals.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758072903919830, "dur": 3248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903923140, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903923214, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903923361, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903923648, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903924032, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903924398, "dur": 2845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903927247, "dur": 554, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1758072903927801, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903927860, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903928016, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903928231, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903928488, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903928813, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903928869, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903929277, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758072903929650, "dur": 26173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903905511, "dur": 10866, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903916392, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1758072903916380, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903916796, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_919C6EC381C92D4C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903916849, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758072903916848, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903917057, "dur": 921, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903918068, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903918213, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RectOffsetWrap.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758072903918183, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903918993, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903919212, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903919729, "dur": 2182, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\ViewModel\\TimelineAssetViewModel.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758072903919674, "dur": 2383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903922057, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903922242, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903922432, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903922721, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncherBuildOptions.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758072903922626, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903923497, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903923593, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903923689, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903923988, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903924423, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903925315, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903925438, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758072903925561, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758072903925962, "dur": 1496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758072903927662, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758072903925888, "dur": 2261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758072903928218, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903928498, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903928808, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903928882, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903929271, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758072903929660, "dur": 26149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903905531, "dur": 10857, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903916406, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903916818, "dur": 384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758072903917341, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903918058, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903918423, "dur": 931, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\CommonTools\\CsGetLuaUtilst.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758072903918286, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903919998, "dur": 972, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\State\\SequenceHierarchy.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758072903921024, "dur": 1261, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Signals\\Styles.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758072903919911, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903922414, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758072903922323, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903923039, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903923518, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903923584, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903923693, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903923992, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903924421, "dur": 1549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903926032, "dur": 1446, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758072903927663, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758072903925970, "dur": 1777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758072903927774, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758072903928142, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903928259, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903928521, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903928798, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903928926, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903929246, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758072903929674, "dur": 26080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903905560, "dur": 10831, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903916507, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903916809, "dur": 1515, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758072903918327, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758072903919537, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758072903919998, "dur": 1153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758072903921399, "dur": 1154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758072903922686, "dur": 683, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758072903918994, "dur": 4549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758072903923649, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758072903923972, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758072903924105, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758072903924356, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_4C98D3F7040CD4F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758072903924457, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758072903924522, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758072903924739, "dur": 2721, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903927476, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903927721, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903927815, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903927871, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903928026, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903928253, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903928514, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903928820, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903928968, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903929284, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758072903929645, "dur": 26181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903905586, "dur": 10849, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903916514, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903916809, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758072903917028, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758072903917390, "dur": 856, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903918420, "dur": 923, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\Injectionsprite.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758072903918248, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903919458, "dur": 1314, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\EnumeratorAsyncExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758072903919391, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903920885, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903921117, "dur": 1185, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\DrawUserIcon.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758072903922373, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\DockEditorWindow.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758072903921065, "dur": 2152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903923217, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903923354, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903923650, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903924031, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903924402, "dur": 2820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903927232, "dur": 470, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/WxEditor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1758072903927810, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903927878, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903928023, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903928269, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903928534, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903928815, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903928868, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758072903928940, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903929238, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758072903929680, "dur": 26043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903905603, "dur": 11170, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903916797, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_58061DC3ABA3501B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758072903916866, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758072903916865, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758072903917073, "dur": 1201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758072903918275, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903918667, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903918810, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\Attachments\\Attachment.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758072903918774, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903919727, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\KnownAssemblies.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758072903919663, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903920468, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903920710, "dur": 1201, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Activation\\ActivationPlayableAsset.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758072903920676, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903922062, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903922290, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ToggleEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758072903922239, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903923291, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903923678, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903924010, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903924416, "dur": 1630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903926194, "dur": 1310, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758072903927663, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Lib\\Editor\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758072903926046, "dur": 1880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758072903928020, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758072903928541, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903928784, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903928938, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903929240, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758072903929679, "dur": 26046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903905756, "dur": 11105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903916866, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758072903916864, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758072903917087, "dur": 1226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758072903918315, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903918648, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903918907, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "Assets\\Plugins\\UDP\\Editor\\Analytics\\WebRequestQueue.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758072903918776, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903919852, "dur": 886, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineEditorWindow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758072903919775, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903920830, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903921019, "dur": 1253, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\UIElements\\UIElementsExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758072903922272, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\UIElements\\ProgressControlsForDialogs.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758072903921009, "dur": 2335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903923344, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903923670, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903924023, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903924410, "dur": 2757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903927168, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903927809, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903927876, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903928025, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903928262, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903928528, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903928779, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903928951, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903929232, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758072903929685, "dur": 26013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903905636, "dur": 11141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903916799, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_5733EFC1F4B68777.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758072903916877, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758072903916877, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758072903917195, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903917781, "dur": 1029, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903918908, "dur": 772, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Tools\\ImportTool.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758072903918811, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903919858, "dur": 897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\TimeFormat.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758072903919805, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903921051, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903921242, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903921867, "dur": 1027, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetsUtils\\Processor\\WorkspaceOperationsMonitor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758072903921433, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903922918, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903923401, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903923630, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903924037, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903924395, "dur": 2864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903927293, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903927775, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903927887, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903928012, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903928273, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903928549, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903928829, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903928960, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903929217, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758072903929687, "dur": 26009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903905657, "dur": 11165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903916848, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758072903916848, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758072903917067, "dur": 1147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758072903918225, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\Mosframe_TableViewHWrap.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758072903918988, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\MonoLinkLuaDataWrap.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758072903918215, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903919726, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VisualStudioCodeInstallation.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758072903919588, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903920473, "dur": 997, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\ProjectGeneration\\IAssemblyNameProvider.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758072903920345, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903921859, "dur": 924, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Text.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758072903921521, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903922920, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903923300, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903923677, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903924016, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903924415, "dur": 1789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903926204, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758072903926258, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903926343, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758072903926478, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758072903926843, "dur": 854, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758072903926602, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758072903927703, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903927834, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903927906, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903928034, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903928208, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903928503, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903928806, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903928895, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903929265, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758072903929665, "dur": 26135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903905813, "dur": 11078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903917037, "dur": 595, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_83842F0CC9A4B472.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758072903917684, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903918077, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903918242, "dur": 850, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\TopTriggerWrap.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758072903918194, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903919142, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903919452, "dur": 2438, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\TaskPool.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758072903921890, "dur": 1058, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Progress.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758072903919375, "dur": 3734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903923160, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903923347, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903923661, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903924026, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903924404, "dur": 2777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903927188, "dur": 491, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FancyScrollView.Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758072903927726, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903927790, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903927864, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903928034, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903928215, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903928503, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903928807, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903928888, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903929265, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758072903929660, "dur": 26148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903905775, "dur": 11091, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903916869, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758072903916868, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758072903917187, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903917675, "dur": 822, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903918529, "dur": 940, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\Tcp\\NetworkEvent\\NetworkEvent_StartConnect.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758072903918501, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903919897, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903920531, "dur": 1355, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_BaseShaderGUI.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758072903921886, "dur": 1052, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_BaseEditorPanel.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758072903920225, "dur": 2826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903923051, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903923347, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903923668, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903924024, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903924408, "dur": 2771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927193, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927424, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927678, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927728, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927787, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903927865, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903928033, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903928222, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903928494, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903928813, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903928876, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903929272, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758072903929659, "dur": 26153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903905894, "dur": 11583, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903917686, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903918107, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903918259, "dur": 902, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\DG_Tweening_TweenSettingsExtensionsWrap.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758072903918228, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903919320, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Components\\SplineComputer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758072903919221, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903920209, "dur": 2525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Actions\\MarkerActions.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758072903920184, "dur": 3256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903923440, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903923611, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903924044, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903924385, "dur": 3046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903927431, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903927672, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903927793, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903927888, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928006, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928121, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928276, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928553, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928826, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903928970, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903929278, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758072903929692, "dur": 26047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903905794, "dur": 11082, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903916883, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758072903916878, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758072903917179, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903917618, "dur": 823, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903918443, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903918650, "dur": 914, "ph": "X", "name": "File", "args": {"detail": "Assets\\FlexReader\\Mapping\\ColumnAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758072903918553, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903919564, "dur": 680, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\FancyScrollView\\Sources\\Runtime\\Core\\FancyScrollView.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758072903919564, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903920711, "dur": 1247, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Events\\Signals\\SignalAsset.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758072903920611, "dur": 1459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903922106, "dur": 858, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\half4.gen.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758072903922071, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903923414, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903923618, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903924042, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903924386, "dur": 2980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903927431, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903927673, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903927733, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903927783, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903927870, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903928030, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903928240, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903928510, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903928802, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903928907, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903929258, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758072903929667, "dur": 26117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903905827, "dur": 11223, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903917067, "dur": 969, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1758072903918067, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903918193, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903918569, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IronSourceUtils.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758072903918547, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903919849, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_NiceVibrations\\Scripts\\Components\\HapticController.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758072903919450, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903921121, "dur": 1279, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\PendingChangesTab.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758072903920692, "dur": 1717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903922429, "dur": 103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903922532, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903922746, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903923323, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903923674, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903924018, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903924357, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 17, "ts": 1758072903924502, "dur": 2937, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758072903927445, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903927907, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758072903928122, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903928511, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758072903928588, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903929007, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903929229, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903929657, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758072903929873, "dur": 25841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903905851, "dur": 11339, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903917365, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903918043, "dur": 110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903918229, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_UI_Dropdown_DropdownEventWrap.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758072903918153, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903919012, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903919995, "dur": 1063, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Trim\\ITrimItemMode.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758072903921121, "dur": 1260, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Sequence\\RectangleTool.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758072903922392, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Sequence\\MarkerHeaderTrackManipulator.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758072903919940, "dur": 3413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903923353, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903923650, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903924029, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903924403, "dur": 2798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903927210, "dur": 473, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/FancyScrollView.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1758072903927732, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903927784, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903927870, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903928029, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903928261, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903928527, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903928789, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903928932, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903929245, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758072903929678, "dur": 26075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903905867, "dur": 11485, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903917383, "dur": 806, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903918190, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903918804, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\IkConstraint.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758072903918751, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903920032, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903920478, "dur": 1404, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Implementation\\WebGL\\WebSocket.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758072903921882, "dur": 1015, "ph": "X", "name": "File", "args": {"detail": "Assets\\UnityWebSocket\\Scripts\\Runtime\\Implementation\\NoWebGL\\WebSocketManager.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758072903920478, "dur": 3018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903923497, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903923605, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903924046, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903924524, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758072903924840, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758072903925143, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758072903925610, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903925675, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758072903925927, "dur": 1768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903927750, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903927825, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903927880, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903928019, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903928271, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903928544, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903928833, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903928956, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903929204, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758072903929434, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758072903929691, "dur": 26038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903905880, "dur": 11496, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903917460, "dur": 671, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903918132, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903918483, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903918613, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Assets\\FlexReader\\Core\\ICloneable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758072903918581, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903919916, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903920130, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903920494, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903920706, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903920922, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903921215, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903921501, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903922084, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903922634, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903923296, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903923639, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903923984, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903924375, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903924573, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903924923, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903925094, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903925516, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903925743, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903925809, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926091, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903926263, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926331, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926428, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926508, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926585, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926873, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758072903926960, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758072903927252, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903927483, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903927564, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903927727, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903927782, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903927890, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903928040, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903928210, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903928475, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903928785, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903928876, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903929212, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758072903929641, "dur": 26075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903905907, "dur": 11791, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903917701, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903918429, "dur": 98, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903918597, "dur": 880, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\DataConfig.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758072903918528, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903919552, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\FancyScrollView\\Sources\\Runtime\\ScrollRect\\IFancyScrollRectContext.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758072903919503, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903920312, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903920710, "dur": 1173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\ClipCaps.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758072903920663, "dur": 1653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903922357, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758072903922316, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903923165, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903923314, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903923619, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903924005, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903924368, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903925332, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758072903925404, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758072903925476, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758072903926038, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758072903925950, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758072903926397, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903926462, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758072903926622, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758072903926898, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903926967, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758072903927422, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903927492, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903927573, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903927783, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903927857, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903928019, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903928247, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903928483, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903928777, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903928895, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903929219, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903929635, "dur": 25234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758072903954869, "dur": 802, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903905922, "dur": 11800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903917738, "dur": 596, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1758072903918334, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903918744, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903918851, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903919058, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903919728, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\UnityAsyncExtensions.AssetBundleRequestAllAssets.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903919275, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903920424, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903921028, "dur": 867, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Changesets\\ChangesetsViewMenu.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903922404, "dur": 875, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Changesets\\ChangesetsListView.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903920916, "dur": 2393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903923309, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903923626, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903923999, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903924369, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903925208, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903925500, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758072903925879, "dur": 687, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteEditorMenu.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903926568, "dur": 596, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditor\\SpriteNameFileIdPair.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903927302, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteFrameModule\\SpritePolygonModeModuleView.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758072903925605, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758072903927453, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903927617, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903927776, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903927864, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928006, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928059, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928215, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928474, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928792, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903928869, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758072903928949, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903929288, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758072903929649, "dur": 26063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903905934, "dur": 11808, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903917927, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903917986, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903918292, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903918526, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903918678, "dur": 891, "ph": "X", "name": "File", "args": {"detail": "Assets\\EditorScript\\EditorListener.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758072903918617, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903920019, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903920479, "dur": 1399, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@1.1.6\\Editor\\Updater\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758072903921878, "dur": 1020, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\TextureImporterDataProviderImplementation.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758072903920379, "dur": 2994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903923373, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903923637, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903924035, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903924396, "dur": 2853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903927491, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903927720, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903927805, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903927882, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903928012, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903928238, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903928556, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903928820, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903928963, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903929286, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758072903929694, "dur": 26153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903905960, "dur": 11949, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903917982, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903918459, "dur": 893, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\bc_MiniGameBase_GameObjectComsWrap.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758072903918238, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903919549, "dur": 2878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Triggers\\AsyncStartTrigger.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758072903919364, "dur": 3074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903922438, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903922664, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903922875, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903922980, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903923076, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903923409, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903923624, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903924039, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903924391, "dur": 2921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903927346, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903927781, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903927870, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903928026, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903928247, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903928512, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903928801, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903928913, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903929252, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758072903929672, "dur": 26104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903905993, "dur": 12030, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903918053, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903918238, "dur": 833, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_TimeWrap.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758072903918160, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903919136, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Cast.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758072903920207, "dur": 959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Editor\\SplitterGUILayout.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758072903919136, "dur": 2096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903921232, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903921863, "dur": 937, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetOverlays\\Cache\\BuildPathDictionary.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758072903921484, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903923173, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903923340, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903923671, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903924019, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903924411, "dur": 2443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903927172, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Queue.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758072903926854, "dur": 841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758072903927794, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903927890, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903928038, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903928202, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903928505, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903928805, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903928901, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903929258, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758072903929666, "dur": 26031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903906000, "dur": 12204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903918275, "dur": 924, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\PlayerPrefsWrap.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758072903918206, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903919319, "dur": 661, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Components\\MeshGenerator.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758072903919236, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903920208, "dur": 1034, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\ControlTrack\\ControlPlayableAssetEditor.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758072903920102, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903921401, "dur": 1300, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Developer\\ProgressOperationHandler.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758072903921325, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903922813, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903922905, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903923000, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903923521, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903923599, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903924048, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903924430, "dur": 3008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903927438, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903927672, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903927787, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903927869, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903928032, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903928234, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903928468, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903928815, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903928868, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758072903928941, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903929233, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758072903929684, "dur": 26032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903906020, "dur": 12227, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903918416, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\OrganBaseComponent.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758072903918251, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903919847, "dur": 2062, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\UIEffect\\Enums\\ShadowStyle.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758072903919392, "dur": 2587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903921979, "dur": 962, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_ColorGradient.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758072903921979, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903923534, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903923681, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903924003, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903924418, "dur": 1596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903926014, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758072903926350, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758072903926436, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758072903926548, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758072903927157, "dur": 448, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.UI.ref.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758072903926859, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758072903927692, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903927776, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903927874, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903928025, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903928260, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903928523, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903928799, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903928920, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903929252, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758072903929673, "dur": 26096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903906041, "dur": 12266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903918307, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903918814, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903919151, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Repeat.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758072903919012, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903919988, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\Snapping\\SnapEngine.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758072903919835, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903921026, "dur": 869, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Locks\\LocksListView.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758072903921895, "dur": 1282, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Locks\\LocksListHeaderState.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758072903920831, "dur": 2354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903923185, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903923307, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903923632, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903923997, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903924374, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903924828, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758072903925109, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758072903925400, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758072903925466, "dur": 1703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903927180, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758072903927361, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 28, "ts": 1758072903927360, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758072903927759, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758072903928005, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928112, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928223, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928467, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928542, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928823, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903928970, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903929222, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903929635, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758072903929879, "dur": 25840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758072903958250, "dur": 1435, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 24240, "tid": 6, "ts": 1758072903969793, "dur": 1252, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 24240, "tid": 6, "ts": 1758072903971078, "dur": 1167, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 24240, "tid": 6, "ts": 1758072903967232, "dur": 5604, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}