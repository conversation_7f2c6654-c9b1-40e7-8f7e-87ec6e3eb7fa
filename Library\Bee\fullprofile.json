{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 4768, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 4768, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 4768, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 4768, "tid": 9, "ts": 1757984781388383, "dur": 549, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781391165, "dur": 555, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 4768, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 4768, "tid": 1, "ts": 1757984781303492, "dur": 19734, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 4768, "tid": 1, "ts": 1757984781323229, "dur": 18463, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 4768, "tid": 1, "ts": 1757984781341702, "dur": 35374, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781391723, "dur": 220, "ph": "X", "name": "", "args": {}}, {"pid": 4768, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781302170, "dur": 4654, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781306825, "dur": 78176, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781307476, "dur": 1500, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781308986, "dur": 295, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781309282, "dur": 3987, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781313273, "dur": 127, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781313402, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781313466, "dur": 333, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781313801, "dur": 9437, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323243, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323271, "dur": 315, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323587, "dur": 76, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323664, "dur": 2, "ph": "X", "name": "ProcessMessages 10386", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323668, "dur": 19, "ph": "X", "name": "ReadAsync 10386", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323689, "dur": 18, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323711, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323733, "dur": 18, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323754, "dur": 16, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323772, "dur": 13, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323787, "dur": 14, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323801, "dur": 11, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323814, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323825, "dur": 9, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323836, "dur": 8, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323845, "dur": 11, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323858, "dur": 9, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323868, "dur": 10, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323879, "dur": 9, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323890, "dur": 14, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323905, "dur": 16, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323922, "dur": 9, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323932, "dur": 21, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323955, "dur": 10, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323966, "dur": 9, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323977, "dur": 9, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781323987, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324003, "dur": 27, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324031, "dur": 12, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324044, "dur": 9, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324054, "dur": 15, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324071, "dur": 9, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324081, "dur": 25, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324107, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324118, "dur": 20, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324139, "dur": 10, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324151, "dur": 10, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324162, "dur": 11, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324174, "dur": 9, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324185, "dur": 10, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324196, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324223, "dur": 9, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324234, "dur": 10, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324245, "dur": 8, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324255, "dur": 12, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324268, "dur": 10, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324280, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324300, "dur": 9, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324310, "dur": 11, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324323, "dur": 12, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324337, "dur": 9, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324347, "dur": 14, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324363, "dur": 34, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324399, "dur": 10, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324410, "dur": 12, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324423, "dur": 233, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324657, "dur": 47, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324706, "dur": 2, "ph": "X", "name": "ProcessMessages 6254", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324708, "dur": 9, "ph": "X", "name": "ReadAsync 6254", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324719, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324732, "dur": 17, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324750, "dur": 11, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324763, "dur": 8, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324773, "dur": 49, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324823, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324835, "dur": 21, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324858, "dur": 34, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324894, "dur": 11, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324906, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324916, "dur": 24, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324942, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324962, "dur": 10, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324973, "dur": 9, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324984, "dur": 10, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781324996, "dur": 26, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325023, "dur": 11, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325036, "dur": 11, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325048, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325062, "dur": 10, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325073, "dur": 62, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325137, "dur": 9, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325148, "dur": 10, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325159, "dur": 10, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325171, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325183, "dur": 10, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325194, "dur": 12, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325208, "dur": 10, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325219, "dur": 11, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325231, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325242, "dur": 14, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325258, "dur": 11, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325270, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325282, "dur": 20, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325303, "dur": 13, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325318, "dur": 15, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325334, "dur": 14, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325350, "dur": 12, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325363, "dur": 9, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325374, "dur": 9, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325385, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325401, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325414, "dur": 12, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325427, "dur": 10, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325438, "dur": 12, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325452, "dur": 9, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325462, "dur": 22, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325486, "dur": 9, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325497, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325514, "dur": 8, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325524, "dur": 9, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325534, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325545, "dur": 11, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325557, "dur": 9, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325567, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325578, "dur": 9, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325588, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325601, "dur": 10, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325613, "dur": 10, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325627, "dur": 10, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325638, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325649, "dur": 9, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325659, "dur": 8, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325669, "dur": 15, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325685, "dur": 8, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325695, "dur": 10, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325706, "dur": 11, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325718, "dur": 10, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325729, "dur": 12, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325743, "dur": 8, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325752, "dur": 12, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325765, "dur": 25, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325791, "dur": 9, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325801, "dur": 33, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325836, "dur": 11, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325849, "dur": 14, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325864, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325875, "dur": 9, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325886, "dur": 16, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325903, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325927, "dur": 12, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325941, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325958, "dur": 12, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325971, "dur": 10, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325983, "dur": 12, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781325996, "dur": 10, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326007, "dur": 11, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326020, "dur": 12, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326033, "dur": 9, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326044, "dur": 9, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326054, "dur": 8, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326064, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326089, "dur": 8, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326099, "dur": 14, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326114, "dur": 10, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326125, "dur": 11, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326138, "dur": 9, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326148, "dur": 8, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326158, "dur": 12, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326171, "dur": 9, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326182, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326201, "dur": 10, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326212, "dur": 9, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326223, "dur": 8, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326233, "dur": 9, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326243, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326254, "dur": 9, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326264, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326277, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326279, "dur": 9, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326290, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326300, "dur": 18, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326320, "dur": 9, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326331, "dur": 19, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326351, "dur": 10, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326362, "dur": 12, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326375, "dur": 10, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326386, "dur": 11, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326399, "dur": 12, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326412, "dur": 8, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326422, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326432, "dur": 12, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326446, "dur": 9, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326457, "dur": 10, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326469, "dur": 10, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326480, "dur": 9, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326490, "dur": 9, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326500, "dur": 11, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326512, "dur": 11, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326524, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326525, "dur": 12, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326538, "dur": 11, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326550, "dur": 10, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326563, "dur": 16, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326580, "dur": 10, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326592, "dur": 10, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326603, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326615, "dur": 11, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326627, "dur": 12, "ph": "X", "name": "ReadAsync 47", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326640, "dur": 9, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326650, "dur": 9, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326661, "dur": 9, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326671, "dur": 20, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326693, "dur": 10, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326704, "dur": 9, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326715, "dur": 10, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326727, "dur": 9, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326738, "dur": 8, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326747, "dur": 11, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326759, "dur": 10, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326770, "dur": 9, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326781, "dur": 10, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326793, "dur": 9, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326803, "dur": 14, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326819, "dur": 10, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326831, "dur": 9, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326841, "dur": 7, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326849, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326860, "dur": 10, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326871, "dur": 16, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326888, "dur": 10, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326899, "dur": 9, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326910, "dur": 12, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326924, "dur": 9, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326935, "dur": 8, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326944, "dur": 8, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326954, "dur": 10, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326966, "dur": 10, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326977, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326987, "dur": 9, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781326998, "dur": 11, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327011, "dur": 9, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327021, "dur": 9, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327031, "dur": 8, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327041, "dur": 11, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327053, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327081, "dur": 9, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327091, "dur": 10, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327103, "dur": 9, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327113, "dur": 9, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327124, "dur": 9, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327134, "dur": 8, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327144, "dur": 21, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327167, "dur": 9, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327178, "dur": 10, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327190, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327204, "dur": 11, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327217, "dur": 9, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327227, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327238, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327249, "dur": 11, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327262, "dur": 12, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327275, "dur": 60, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327337, "dur": 22, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327360, "dur": 1, "ph": "X", "name": "ProcessMessages 2115", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327361, "dur": 9, "ph": "X", "name": "ReadAsync 2115", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327372, "dur": 9, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327382, "dur": 9, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327393, "dur": 10, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327405, "dur": 9, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327415, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327519, "dur": 1, "ph": "X", "name": "ProcessMessages 1834", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327521, "dur": 9, "ph": "X", "name": "ReadAsync 1834", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327531, "dur": 9, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327542, "dur": 10, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327553, "dur": 9, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327563, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327575, "dur": 10, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327586, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327601, "dur": 9, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327612, "dur": 16, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327629, "dur": 9, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327639, "dur": 9, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327650, "dur": 9, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327661, "dur": 9, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327671, "dur": 9, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327681, "dur": 9, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327692, "dur": 9, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327702, "dur": 15, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327719, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327730, "dur": 10, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327742, "dur": 12, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327755, "dur": 18, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327774, "dur": 8, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327783, "dur": 10, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327795, "dur": 8, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327804, "dur": 11, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327817, "dur": 10, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327829, "dur": 9, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327839, "dur": 10, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327850, "dur": 12, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327863, "dur": 8, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327872, "dur": 11, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327884, "dur": 10, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327895, "dur": 9, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327906, "dur": 9, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327916, "dur": 15, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327933, "dur": 10, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327943, "dur": 11, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327956, "dur": 12, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327969, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781327987, "dur": 34, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328023, "dur": 10, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328034, "dur": 10, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328046, "dur": 11, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328059, "dur": 9, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328069, "dur": 8, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328078, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328101, "dur": 9, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328111, "dur": 9, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328121, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328135, "dur": 9, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328145, "dur": 19, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328166, "dur": 8, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328175, "dur": 9, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328185, "dur": 11, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328197, "dur": 9, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328207, "dur": 10, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328219, "dur": 9, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328229, "dur": 9, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328239, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328255, "dur": 9, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328266, "dur": 11, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328278, "dur": 11, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328290, "dur": 9, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328301, "dur": 9, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328311, "dur": 9, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328322, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328333, "dur": 10, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328344, "dur": 9, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328354, "dur": 9, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328364, "dur": 9, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328375, "dur": 9, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328385, "dur": 15, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328401, "dur": 10, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328413, "dur": 9, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328423, "dur": 9, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328434, "dur": 9, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328444, "dur": 18, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328463, "dur": 10, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328475, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328488, "dur": 9, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328498, "dur": 8, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328508, "dur": 9, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328518, "dur": 21, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328540, "dur": 9, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328550, "dur": 13, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328564, "dur": 9, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328574, "dur": 10, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328586, "dur": 9, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328596, "dur": 9, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328607, "dur": 9, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328617, "dur": 9, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328627, "dur": 9, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328638, "dur": 8, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328647, "dur": 9, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328658, "dur": 15, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328674, "dur": 10, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328686, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328709, "dur": 9, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328718, "dur": 11, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328731, "dur": 9, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328741, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328753, "dur": 10, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328764, "dur": 10, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328776, "dur": 10, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328787, "dur": 10, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328799, "dur": 9, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328809, "dur": 10, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328821, "dur": 8, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328830, "dur": 8, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328839, "dur": 13, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328891, "dur": 18, "ph": "X", "name": "ReadAsync 88", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328911, "dur": 12, "ph": "X", "name": "ReadAsync 1458", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328924, "dur": 8, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328934, "dur": 11, "ph": "X", "name": "ReadAsync 55", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328946, "dur": 9, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328956, "dur": 9, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328967, "dur": 9, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328977, "dur": 10, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781328989, "dur": 12, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329002, "dur": 9, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329012, "dur": 8, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329022, "dur": 10, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329033, "dur": 11, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329045, "dur": 10, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329057, "dur": 10, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329068, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329083, "dur": 12, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329097, "dur": 10, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329108, "dur": 9, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329119, "dur": 12, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329132, "dur": 8, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329142, "dur": 14, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329157, "dur": 9, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329168, "dur": 11, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329181, "dur": 9, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329191, "dur": 26, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329219, "dur": 9, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329230, "dur": 9, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329240, "dur": 29, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329271, "dur": 9, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329281, "dur": 9, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329291, "dur": 9, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329302, "dur": 9, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329312, "dur": 14, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329327, "dur": 10, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329338, "dur": 9, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329349, "dur": 16, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329366, "dur": 45, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329412, "dur": 9, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329422, "dur": 12, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329435, "dur": 10, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329447, "dur": 10, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329458, "dur": 14, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329474, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329487, "dur": 10, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329499, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329512, "dur": 10, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329523, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329535, "dur": 8, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329545, "dur": 9, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329556, "dur": 11, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329568, "dur": 27, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329597, "dur": 10, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329608, "dur": 10, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329619, "dur": 12, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329633, "dur": 8, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329642, "dur": 10, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329653, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329664, "dur": 13, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329679, "dur": 54, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329737, "dur": 114, "ph": "X", "name": "ReadAsync 1358", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329852, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329909, "dur": 9, "ph": "X", "name": "ReadAsync 1697", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329920, "dur": 9, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329931, "dur": 11, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329944, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781329956, "dur": 852, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781330811, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781330840, "dur": 156, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781330996, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331016, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331018, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331070, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331084, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331129, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331149, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331165, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331176, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331207, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331219, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331255, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331266, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331318, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331331, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331369, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331386, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331411, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331423, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331451, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331462, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331495, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331505, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331532, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331547, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331784, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331798, "dur": 165, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331966, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781331979, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332004, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332019, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332035, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332065, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332086, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332099, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332118, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332128, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332148, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332159, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332181, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332198, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332209, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332258, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332276, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332297, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781332309, "dur": 1173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333486, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333504, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333505, "dur": 29, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333536, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333551, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333577, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333592, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333656, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333667, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333693, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333705, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333834, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333851, "dur": 72, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333925, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333935, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333976, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781333987, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334004, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334015, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334048, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334059, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334089, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334101, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334130, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334140, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334154, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334163, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334175, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334226, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334240, "dur": 11, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334253, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334298, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334315, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334325, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334336, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334377, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334389, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334437, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334449, "dur": 11, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334461, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334484, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334495, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334522, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334594, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334606, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334618, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334648, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334678, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334689, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334701, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334752, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334764, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334799, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334811, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334847, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334860, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334898, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334908, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334939, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334949, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781334991, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335002, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335043, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335054, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335094, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335103, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335139, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335215, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335229, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335242, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335248, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335281, "dur": 360, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335643, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335665, "dur": 83, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781335750, "dur": 1624, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337377, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337425, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337427, "dur": 11, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337439, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337478, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337501, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337513, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337549, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337577, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337850, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337865, "dur": 27, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337895, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337912, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781337925, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338020, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338031, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338061, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338080, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338091, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338108, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338134, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338148, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338159, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338247, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338267, "dur": 26, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338295, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338311, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338321, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338332, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338343, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338360, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338373, "dur": 6, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338381, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338401, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338415, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338425, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338437, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338473, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338484, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338502, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338513, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338526, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338539, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338550, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338565, "dur": 9, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338576, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338586, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338610, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338620, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338631, "dur": 10, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338642, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338668, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338682, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338702, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338712, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338742, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338751, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338784, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338795, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338826, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338836, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338865, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338874, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338911, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781338920, "dur": 1154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781340077, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781340102, "dur": 23, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781340127, "dur": 10066, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350198, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350241, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350297, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350315, "dur": 356, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350675, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350691, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350752, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350778, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781350792, "dur": 346, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351139, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351148, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351187, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351195, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351285, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351298, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351368, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351379, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351421, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351432, "dur": 399, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351833, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351836, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351861, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351941, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351955, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351966, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781351991, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352001, "dur": 213, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352216, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352229, "dur": 125, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352356, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352365, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352458, "dur": 7, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352467, "dur": 257, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781352725, "dur": 2075, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781354805, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781354860, "dur": 5, "ph": "X", "name": "ProcessMessages 2448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781354866, "dur": 122, "ph": "X", "name": "ReadAsync 2448", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781354992, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355013, "dur": 14, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355029, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355041, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355125, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355137, "dur": 69, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355209, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355228, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355242, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355253, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355336, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355383, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355425, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355443, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355460, "dur": 198, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355659, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355677, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355710, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355724, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355762, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355772, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355980, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781355992, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356082, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356093, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356098, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356112, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356122, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356139, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356150, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356376, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356387, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781356398, "dur": 24125, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781380527, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781380528, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781380548, "dur": 171, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 4768, "tid": 12884901888, "ts": 1757984781380721, "dur": 3784, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781391944, "dur": 595, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 4768, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 4768, "tid": 8589934592, "ts": 1757984781300516, "dur": 76622, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 4768, "tid": 8589934592, "ts": 1757984781377140, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 4768, "tid": 8589934592, "ts": 1757984781377142, "dur": 900, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781392541, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 4768, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 4768, "tid": 4294967296, "ts": 1757984781190700, "dur": 194854, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 4768, "tid": 4294967296, "ts": 1757984781195053, "dur": 101252, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 4768, "tid": 4294967296, "ts": 1757984781385562, "dur": 1300, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 4768, "tid": 4294967296, "ts": 1757984781386899, "dur": 37, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781392546, "dur": 2, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1757984781305849, "dur": 16641, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781322498, "dur": 494, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781323065, "dur": 569, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781324045, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5D6C7D883717C04.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1757984781323649, "dur": 6737, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781330393, "dur": 50145, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781380539, "dur": 203, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781380955, "dur": 662, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1757984781323783, "dur": 6679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781330477, "dur": 405, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1757984781330465, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1757984781330884, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781331263, "dur": 1288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781332561, "dur": 940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781333508, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781334246, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781334877, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781335377, "dur": 453, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781335831, "dur": 1303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781337138, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781337279, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781337613, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781338062, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781338667, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781338986, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781339582, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781339698, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781341040, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781342452, "dur": 3913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781347674, "dur": 564, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\EnumExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1757984781346366, "dur": 2303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781348669, "dur": 1844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781350513, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781350639, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781350723, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781351193, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781351613, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781352366, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781352648, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757984781352885, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781353051, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757984781353300, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781353597, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1757984781353812, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354349, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354453, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354528, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354604, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354666, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781354799, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781355096, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781355249, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781355435, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781355720, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781355869, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781356129, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1757984781356545, "dur": 23996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781323794, "dur": 6672, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781330481, "dur": 421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1757984781330469, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781330903, "dur": 3351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781334263, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781334975, "dur": 781, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781335758, "dur": 1571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781337334, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781337656, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781338141, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781338780, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781338867, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781339103, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\Sirenix\\Demos\\Editor Windows\\Scripts\\Editor\\ToolUti.cs"}}, {"pid": 12345, "tid": 2, "ts": 1757984781339049, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781339802, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781340007, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781344249, "dur": 1191, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Triggers\\AsyncAwakeTrigger.cs"}}, {"pid": 12345, "tid": 2, "ts": 1757984781341011, "dur": 4736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781345747, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781347647, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_TextParsingUtilities.cs"}}, {"pid": 12345, "tid": 2, "ts": 1757984781346709, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781348565, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781350043, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781350667, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781351151, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781351624, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781352237, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781352381, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781352454, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781352627, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781352912, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1757984781353153, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781353313, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781353439, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781353512, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1757984781353587, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781353879, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354018, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354137, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354241, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354323, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354400, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354460, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354519, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354642, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781354805, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781355089, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781355256, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781355422, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781355700, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781355852, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781356137, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1757984781356551, "dur": 23990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781323690, "dur": 6708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781330452, "dur": 535, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1757984781330408, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781330988, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781331252, "dur": 935, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781332196, "dur": 1411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781333652, "dur": 876, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781334530, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781334754, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781335246, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781335879, "dur": 1692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781337587, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781337999, "dur": 448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781338458, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781339139, "dur": 1475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781340614, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781345891, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_SpriteAssetMenu.cs"}}, {"pid": 12345, "tid": 3, "ts": 1757984781343227, "dur": 3499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781347744, "dur": 1449, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Sprite.cs"}}, {"pid": 12345, "tid": 3, "ts": 1757984781346727, "dur": 2816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781349543, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781350178, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781350659, "dur": 517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781351176, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781351612, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781352374, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781352662, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757984781352943, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781353228, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353292, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353368, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353438, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353507, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353590, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757984781353815, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781354145, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757984781354399, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781354652, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781354829, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355084, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355257, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355398, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355451, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355676, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781355865, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781356111, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1757984781356305, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781356420, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1757984781356562, "dur": 23965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781323763, "dur": 6652, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781330448, "dur": 551, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1757984781330420, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1757984781331000, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781331294, "dur": 1203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781332505, "dur": 1207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781333720, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781334569, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781334862, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781335480, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781336035, "dur": 1597, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781337645, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781338118, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781338690, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781338795, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781338912, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781339614, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781340040, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781341208, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\Json.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757984781340311, "dur": 3711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781344022, "dur": 2380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781347674, "dur": 1081, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Configuration\\AutoConfig.cs"}}, {"pid": 12345, "tid": 4, "ts": 1757984781346402, "dur": 3278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781349680, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781350976, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781351117, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781351210, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781351606, "dur": 2225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781353831, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1757984781354100, "dur": 732, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781354851, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781355109, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781355252, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781355461, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781355675, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781355808, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781356152, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1757984781356557, "dur": 23987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781323919, "dur": 6570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781330502, "dur": 404, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1757984781330492, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1757984781330907, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781331319, "dur": 1381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781332708, "dur": 1302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781334014, "dur": 651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781334672, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781335014, "dur": 1127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781336147, "dur": 1767, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781337916, "dur": 581, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781338503, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781339263, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781339563, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_BoxCollider2DWrap.cs"}}, {"pid": 12345, "tid": 5, "ts": 1757984781339523, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781340172, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781341440, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\PlayerLoopTimer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1757984781341027, "dur": 2406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781343433, "dur": 3627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781347649, "dur": 2039, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double3.gen.cs"}}, {"pid": 12345, "tid": 5, "ts": 1757984781347060, "dur": 4364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781351424, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781351657, "dur": 2306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781353982, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781354448, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781354690, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781354861, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781355100, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781355256, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781355460, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781355702, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781355800, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781356157, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1757984781356540, "dur": 24006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781323720, "dur": 6686, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781330451, "dur": 542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1757984781330411, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1757984781330993, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781331293, "dur": 1143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781332443, "dur": 1206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781333663, "dur": 774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781334440, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781334817, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781335192, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781335628, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781336076, "dur": 2153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781338231, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781338899, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781339685, "dur": 1776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781341461, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781343056, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781344535, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\TextureImporterDataProvider.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757984781344221, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781347653, "dur": 1402, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Settings\\ShelveAndSwitchOptionsFoldout.cs"}}, {"pid": 12345, "tid": 6, "ts": 1757984781346025, "dur": 4284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781350309, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781350690, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781351168, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781351642, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781352872, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1757984781353177, "dur": 1322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781354523, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781354696, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781354850, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781355110, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781355248, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781355462, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781355670, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781355811, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781356149, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1757984781356564, "dur": 23981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781323746, "dur": 6665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781330449, "dur": 425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1757984781330415, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757984781330884, "dur": 4149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781335035, "dur": 3161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781338198, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781338810, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781338915, "dur": 1587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781340504, "dur": 3883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781344387, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781344588, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781347521, "dur": 870, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_EditorResourceManager.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757984781348391, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_Dropdown.cs"}}, {"pid": 12345, "tid": 7, "ts": 1757984781346772, "dur": 3424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781350197, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781350695, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781351162, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781351645, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781352806, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1757984781352882, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781353049, "dur": 1498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1757984781354548, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781354762, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1757984781355003, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781355430, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781355707, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781355796, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781356160, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781356537, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1757984781356831, "dur": 23719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781323775, "dur": 6645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781330452, "dur": 551, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1757984781330425, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1757984781331004, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781331295, "dur": 1230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781332533, "dur": 1272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781333812, "dur": 829, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781334644, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781335005, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781335651, "dur": 1799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781337454, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781337933, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781338531, "dur": 808, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781339342, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781339562, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781339677, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781340508, "dur": 4115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781345862, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Playables\\TimeNotificationBehaviour.cs"}}, {"pid": 12345, "tid": 8, "ts": 1757984781344623, "dur": 2716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781347625, "dur": 1836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 8, "ts": 1757984781347339, "dur": 2999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781350338, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781350689, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781351174, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781351637, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781353191, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1757984781353580, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781354444, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1757984781354727, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781354818, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781355113, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781355244, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781355467, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781355714, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781355879, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781356120, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1757984781356579, "dur": 23997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781323807, "dur": 6663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781330485, "dur": 522, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1757984781330473, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757984781331008, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781331296, "dur": 1264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781332573, "dur": 1358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781333941, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781334489, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781334889, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781335235, "dur": 775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781336012, "dur": 1698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781337739, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781338269, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781338921, "dur": 790, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781339713, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781341795, "dur": 3865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781345660, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781346011, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781347677, "dur": 1259, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 9, "ts": 1757984781347350, "dur": 2278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781349628, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781350127, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781350698, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781351155, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781351649, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781352443, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1757984781352629, "dur": 1843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781354476, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757984781354784, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781355064, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1757984781355276, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781355467, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781355712, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781355877, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781356168, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1757984781356572, "dur": 23971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781323822, "dur": 6652, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781330488, "dur": 385, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1757984781330477, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757984781330875, "dur": 4167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781335047, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757984781335122, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781335852, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781336150, "dur": 2110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338354, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338436, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338528, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338625, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338753, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338827, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338893, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781338958, "dur": 1593, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781340595, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781340789, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781340902, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341132, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341236, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341351, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341588, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341666, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341765, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 10, "ts": 1757984781341854, "dur": 833, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781342688, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781342995, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781343122, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781343271, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781343452, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781343549, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781343730, "dur": 1686, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781345417, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781345493, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781345551, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781345626, "dur": 834, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781346461, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781346693, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781346768, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781346869, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781346978, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781347103, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781347198, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781347379, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781347495, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781347565, "dur": 1409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandState.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781348974, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349058, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349141, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349233, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349375, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349466, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349533, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestCommandPcHelper.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349796, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781349929, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350228, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350298, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350383, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350446, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350528, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350590, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350791, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781350865, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 10, "ts": 1757984781335677, "dur": 15346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781351023, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781351224, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781351483, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781351601, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781351858, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781352063, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781352258, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757984781352322, "dur": 1653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781353977, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781354230, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781354364, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781354692, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1757984781354773, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781355023, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781355215, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781355454, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781355664, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781355857, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781356011, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781356108, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781356538, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1757984781356728, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1757984781356824, "dur": 23730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781323947, "dur": 6549, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781330510, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1757984781330498, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_7B26F11653A8BB46.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757984781330955, "dur": 685, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781331649, "dur": 1040, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781332697, "dur": 1275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781333976, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781334427, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781334692, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781335132, "dur": 2242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781337379, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781337844, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781338315, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781339032, "dur": 996, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781340030, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781340424, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781343068, "dur": 4015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781347712, "dur": 1252, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ScrollbarEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1757984781347083, "dur": 3347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781350430, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781350676, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781351185, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781351630, "dur": 1802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781353433, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757984781353506, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1757984781353564, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781354133, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1757984781354380, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781355077, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781355237, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781355443, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781355706, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781355828, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781356143, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1757984781356568, "dur": 23975, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781323846, "dur": 6636, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781330496, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1757984781330485, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_24A8B6F9BAE245F7.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757984781330916, "dur": 579, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781331511, "dur": 1217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781332735, "dur": 1354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781334094, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781334419, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781334668, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781335024, "dur": 1198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781336238, "dur": 1551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781337807, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781338530, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781338697, "dur": 851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781339588, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\LuaPerfect_ObjectRefWrap.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757984781339550, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781340324, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781341325, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781342249, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781343704, "dur": 1688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\MenuPriority.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757984781345828, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Utils\\EditModeUtils.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757984781343048, "dur": 4583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781347646, "dur": 687, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UtpMessageReporter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757984781348424, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolStarter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1757984781347646, "dur": 2540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781350186, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781350697, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781351159, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781351646, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781352657, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757984781352724, "dur": 1340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781354065, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757984781354312, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781354568, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1757984781355030, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781355087, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781355256, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781355417, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781355709, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781355795, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1757984781355880, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781356164, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1757984781356587, "dur": 23972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781323879, "dur": 6607, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781330503, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1757984781330489, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D07E2B87C74DF17.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1757984781330922, "dur": 592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781331524, "dur": 682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781332215, "dur": 1125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781333346, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781334252, "dur": 615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781334872, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781335531, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781335641, "dur": 623, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781336288, "dur": 1466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781337768, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781338139, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781338730, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781338835, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781339015, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781339731, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781340182, "dur": 2770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781342952, "dur": 3913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781347620, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint4.gen.cs"}}, {"pid": 12345, "tid": 13, "ts": 1757984781346865, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781348723, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781351210, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781351612, "dur": 1980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781353592, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1757984781354024, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781354325, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781354452, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781354539, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781354663, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781354780, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781355083, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781355260, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781355454, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781355662, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781355847, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781356126, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1757984781356576, "dur": 23989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781323929, "dur": 6564, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781330506, "dur": 427, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1757984781330495, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1542A8759536CB9C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1757984781330933, "dur": 755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781331699, "dur": 916, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781332622, "dur": 1225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781333856, "dur": 874, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781334736, "dur": 690, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781335428, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781335979, "dur": 1446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781337432, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781337730, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781338044, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781338668, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781338954, "dur": 1270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781341374, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-csharp\\TransformConstraintData.cs"}}, {"pid": 12345, "tid": 14, "ts": 1757984781340227, "dur": 2989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781343216, "dur": 1247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781345790, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@1.1.6\\Editor\\NavigationOverlay.cs"}}, {"pid": 12345, "tid": 14, "ts": 1757984781344464, "dur": 2673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781347622, "dur": 1959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ImageEditor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1757984781347137, "dur": 3609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781350746, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781351217, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781351598, "dur": 2315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781353956, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781354262, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781354531, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781354659, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781354793, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781355123, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781355229, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781355450, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781355681, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781355837, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781356141, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1757984781356570, "dur": 23952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781323964, "dur": 6536, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781330512, "dur": 439, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1757984781330503, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757984781330952, "dur": 798, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781331759, "dur": 1284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781333051, "dur": 918, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781333978, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781334854, "dur": 719, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781335621, "dur": 2273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781337898, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781338067, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781338657, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781338771, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781338957, "dur": 1587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781340545, "dur": 1953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781342498, "dur": 2755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781345877, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Locks\\LocksListHeaderState.cs"}}, {"pid": 12345, "tid": 15, "ts": 1757984781345253, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781347627, "dur": 1499, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\common.cs"}}, {"pid": 12345, "tid": 15, "ts": 1757984781346926, "dur": 2779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781349705, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781350530, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781350672, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781351187, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781351624, "dur": 1841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781353466, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1757984781353526, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781353951, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781354171, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781354521, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781354645, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781354806, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781355117, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781355238, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781355441, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781355708, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781355809, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781356151, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1757984781356559, "dur": 23980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781323975, "dur": 6532, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781330520, "dur": 377, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1757984781330510, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F085666F1110AAE3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1757984781330897, "dur": 3991, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781334899, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781335138, "dur": 2539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781337698, "dur": 659, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781338361, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781339103, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\Modules\\SkeletonRenderSeparator\\Editor\\SkeletonPartsRendererInspector.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757984781339103, "dur": 3287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781342390, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781342585, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781345782, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Audio\\AudioClipProperties.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757984781347752, "dur": 1425, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationOutputWeightProcessor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1757984781344966, "dur": 4343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781349309, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781350359, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781350690, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781351172, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781351640, "dur": 1534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781353174, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757984781353416, "dur": 964, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781354388, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1757984781354660, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781354787, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781355124, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781355231, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781355448, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781355723, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781355803, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781356155, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1757984781356544, "dur": 23993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781323994, "dur": 6518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781330524, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1757984781330514, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_7F3AF681D6CECB6F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757984781330976, "dur": 908, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781331892, "dur": 1231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781333130, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781333305, "dur": 1216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781334540, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781335044, "dur": 3518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781338567, "dur": 1951, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781340519, "dur": 2012, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781342531, "dur": 2868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781345399, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781345819, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781347630, "dur": 1377, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetMenu\\Dialogs\\CheckinDialog.cs"}}, {"pid": 12345, "tid": 17, "ts": 1757984781346630, "dur": 3621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781350251, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781350692, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781351165, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781351642, "dur": 1257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781352899, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757984781353176, "dur": 912, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354106, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354335, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354389, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354461, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354535, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354679, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1757984781354746, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781354836, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1757984781355061, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781355230, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781355451, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781355669, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781355844, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781356127, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1757984781356575, "dur": 23948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781324006, "dur": 6509, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781330527, "dur": 429, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1757984781330517, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5D6C7D883717C04.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1757984781330957, "dur": 849, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781331813, "dur": 1269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781333089, "dur": 836, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781333935, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781334770, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781335106, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781335797, "dur": 1939, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781337738, "dur": 702, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781338460, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781339219, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781339489, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781339939, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781340650, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781342425, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781343564, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781345958, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\WebApi\\SubscriptionDetailsResponse.cs"}}, {"pid": 12345, "tid": 18, "ts": 1757984781344984, "dur": 2251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781347614, "dur": 1376, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 18, "ts": 1757984781347235, "dur": 3135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781350370, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781350684, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781351178, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781351657, "dur": 2491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781354153, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781354608, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781354667, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781354774, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781354856, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781355106, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781355254, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781355424, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781355708, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781355796, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781356159, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1757984781356579, "dur": 23989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781324018, "dur": 6501, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781330531, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1757984781330521, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757984781330899, "dur": 1293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781332213, "dur": 1179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781333399, "dur": 860, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781334274, "dur": 600, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781334879, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781335282, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781335794, "dur": 1260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781337060, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781337181, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781337712, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781337997, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781338276, "dur": 697, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781338991, "dur": 596, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781339602, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\AnimationCurvesWrap.cs"}}, {"pid": 12345, "tid": 19, "ts": 1757984781339589, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781340473, "dur": 4191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781344664, "dur": 3530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781348195, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781350150, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781350660, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781351170, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781351617, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781352259, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757984781352311, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781352425, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757984781352662, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781352859, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781353192, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757984781353424, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781353834, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1757984781354122, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354282, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354363, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354451, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354532, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354601, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354667, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781354798, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781355097, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781355244, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781355428, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781355701, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781355794, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1757984781355864, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781356111, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781356427, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1757984781356558, "dur": 23979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781324033, "dur": 6490, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781330535, "dur": 434, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1757984781330525, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_389F212FAF489129.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757984781330970, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781331603, "dur": 979, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781332591, "dur": 1160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781333758, "dur": 842, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781334604, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781334884, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781335187, "dur": 1754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781336946, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781337118, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781337429, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781337872, "dur": 570, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781338462, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781339178, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\AssetDanshari\\Editor\\TreeDataModel\\AssetMultiColumnHeader.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757984781339178, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781340394, "dur": 2880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781343274, "dur": 4484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781347758, "dur": 884, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestSettings\\TestSettings.cs"}}, {"pid": 12345, "tid": 20, "ts": 1757984781347758, "dur": 2693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781350451, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781350640, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781350721, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781351144, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781351626, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781352307, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781352414, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1757984781352722, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781353374, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1757984781353445, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1757984781353662, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781353960, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781354030, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781354107, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781354481, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781354673, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781354786, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781355103, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781355236, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781355441, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781355682, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781355859, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781356125, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1757984781356551, "dur": 23988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781324045, "dur": 6480, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781330536, "dur": 378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1757984781330527, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757984781330915, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781331361, "dur": 1098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781332467, "dur": 1281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781333756, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781334487, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781334931, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781335335, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781335697, "dur": 1785, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781337487, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781337807, "dur": 684, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781338496, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781338639, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781338901, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781339602, "dur": 738, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\TopTriggerWrap.cs"}}, {"pid": 12345, "tid": 21, "ts": 1757984781339525, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781340403, "dur": 2494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781342897, "dur": 3479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781346376, "dur": 2276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781348652, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781350068, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781350664, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781351163, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781351619, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781352250, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781352337, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757984781352390, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781352763, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1757984781353267, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781353553, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781353723, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757984781353832, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781353950, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781354049, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781354447, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781354519, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781354649, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781354805, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781355090, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781355250, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781355412, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781355701, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781355794, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1757984781355859, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781356131, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1757984781356545, "dur": 23997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781324072, "dur": 6457, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781330544, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1757984781330532, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757984781330926, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781331564, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781332405, "dur": 882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781333294, "dur": 946, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781334250, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781334873, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781335087, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781335692, "dur": 1464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781337161, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781337677, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781338277, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781338936, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781339527, "dur": 115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781339642, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781340132, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781340314, "dur": 2656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781342970, "dur": 3376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781347712, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Hub\\CommandLineArguments.cs"}}, {"pid": 12345, "tid": 22, "ts": 1757984781346347, "dur": 2126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781348473, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781350067, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781350666, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781351157, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781351619, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781352244, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781352356, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757984781352417, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781352919, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757984781353154, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781353649, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1757984781353706, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781353846, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1757984781354101, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354217, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354314, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354395, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354459, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354526, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354657, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781354779, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355017, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355162, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355230, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355441, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355677, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781355861, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781356117, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1757984781356552, "dur": 23986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781324104, "dur": 6432, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781330548, "dur": 417, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1757984781330538, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757984781330971, "dur": 831, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781331812, "dur": 891, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781332711, "dur": 1416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781334131, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781334417, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781334681, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781335121, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781335896, "dur": 1202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337134, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337248, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337435, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337565, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337717, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781337834, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338064, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338166, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338264, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338353, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338431, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338526, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338593, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781338942, "dur": 1597, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781340596, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781340785, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781340906, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341136, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341239, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341586, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341658, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341764, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 23, "ts": 1757984781341861, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342058, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342118, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342275, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342343, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342422, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342497, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342686, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781342757, "dur": 2631, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345403, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345468, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345558, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345623, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345727, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345826, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781345914, "dur": 557, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781346472, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781346632, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781346825, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781346900, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347065, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347172, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347379, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347442, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347533, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781347600, "dur": 1351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781348951, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349019, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349108, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349184, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349297, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349362, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349483, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349670, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349810, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781349981, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781350162, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781350277, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781350395, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 23, "ts": 1757984781335664, "dur": 14844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757984781350511, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781350639, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781350730, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781351205, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781351618, "dur": 1957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781353576, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1757984781353642, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1757984781353877, "dur": 595, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781354495, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781354680, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781354863, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781355093, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781355260, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781355468, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781355696, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781355804, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781356153, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1757984781356551, "dur": 23994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781324084, "dur": 6448, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781330545, "dur": 422, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1757984781330534, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757984781330968, "dur": 789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781331766, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781332646, "dur": 1246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781333905, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781334278, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781334641, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781335008, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781335601, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781335930, "dur": 1646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781337591, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781338024, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781338501, "dur": 799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781339603, "dur": 820, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RigidbodyWrap.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757984781339303, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781340448, "dur": 2676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781343124, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781345290, "dur": 1727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781347716, "dur": 1359, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float3x2.gen.cs"}}, {"pid": 12345, "tid": 24, "ts": 1757984781347018, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781350113, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781350699, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781351152, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781351650, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781352276, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757984781352334, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781352389, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757984781352778, "dur": 1775, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781354576, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757984781354668, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1757984781354751, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1757984781355193, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781355399, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781355522, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781355675, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781355841, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781356135, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1757984781356575, "dur": 23946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781324139, "dur": 6403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781330554, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 25, "ts": 1757984781330544, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_243621AF56FD8BB7.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1757984781330974, "dur": 870, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781331851, "dur": 979, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781332838, "dur": 1007, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781333852, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781334701, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781335190, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781335828, "dur": 1818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781337660, "dur": 534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781338207, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781338856, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781339188, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\SkeletonUtility\\Editor\\SkeletonUtilityInspector.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757984781339069, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781341150, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\UnityBindingExtensions.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757984781340843, "dur": 4177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781345743, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\PendingMergeLinks\\MergeLinksListView.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757984781347668, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\PendingChanges\\PendingChangesTab.cs"}}, {"pid": 12345, "tid": 25, "ts": 1757984781345020, "dur": 3824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781348844, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781349746, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 25, "ts": 1757984781349276, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781350417, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781350682, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781351181, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781351635, "dur": 1568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781353204, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757984781353449, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781354156, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1757984781354413, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781354595, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781354656, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781354799, "dur": 320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781355119, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781355236, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781355447, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781355700, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781355834, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781356142, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1757984781356569, "dur": 23955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781324156, "dur": 6389, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781330557, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1757984781330547, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C549C74F0FF91F05.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757984781330976, "dur": 950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781331936, "dur": 873, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781332818, "dur": 1397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781334223, "dur": 735, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781334976, "dur": 693, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781335670, "dur": 2312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781337985, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781338320, "dur": 738, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781339059, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781339331, "dur": 1193, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_UI_ImageWrap.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757984781339275, "dur": 1318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781340593, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781345759, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Extensions\\TrackExtensions.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757984781343120, "dur": 3892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781347553, "dur": 1389, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Il2CppEagerStaticClassConstructionAttribute.cs"}}, {"pid": 12345, "tid": 26, "ts": 1757984781347012, "dur": 1971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781348983, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781350028, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781350702, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781351146, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781351651, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781352259, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757984781352358, "dur": 1656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781354016, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757984781354399, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781354675, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1757984781354784, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1757984781355111, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781355216, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781355466, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781355716, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781355874, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781356111, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781356196, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1757984781356582, "dur": 23980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781324170, "dur": 6380, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781330570, "dur": 410, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1757984781330552, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1757984781330981, "dur": 984, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781331973, "dur": 1194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781333175, "dur": 1353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781334531, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781334754, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781335098, "dur": 876, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781335976, "dur": 1703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781337693, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781338307, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781338939, "dur": 987, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781339928, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781340122, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781340466, "dur": 2246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781342712, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781347849, "dur": 1044, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Gluon\\IncomingChangesTreeView.cs"}}, {"pid": 12345, "tid": 27, "ts": 1757984781345093, "dur": 3831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781348924, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781350026, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781350704, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781351146, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781351653, "dur": 2679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354332, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354393, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354458, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354537, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354638, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354764, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781354817, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781355117, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781355250, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781355430, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781355707, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781355815, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781356147, "dur": 420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1757984781356568, "dur": 23957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781324193, "dur": 6360, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781330563, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 28, "ts": 1757984781330553, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_A31099604CCBA22C.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757984781330952, "dur": 1211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781332172, "dur": 1358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781333536, "dur": 821, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781334377, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781334602, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781334815, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781335132, "dur": 799, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781335933, "dur": 1142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781337082, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781337229, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781337551, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781337752, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781338116, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781338694, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781339033, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781339629, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781339828, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781340020, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781341309, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Runtime\\spine-unity\\Modules\\AttachmentTools\\AttachmentTools.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757984781340197, "dur": 2777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781342974, "dur": 2199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781345855, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Developer\\UnityMergeTree.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757984781347573, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Developer\\IsCurrent.cs"}}, {"pid": 12345, "tid": 28, "ts": 1757984781345173, "dur": 3577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781348750, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781349659, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781350333, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781350657, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781351182, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781351599, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781351720, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781352264, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757984781352376, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757984781352633, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781353187, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781353444, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1757984781353500, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781353681, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1757984781353911, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354163, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354311, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354389, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354507, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354672, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781354792, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781355102, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781355243, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781355435, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781355687, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781355854, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781356135, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781356538, "dur": 21979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1757984781378519, "dur": 1978, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1757984781382775, "dur": 1887, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 4768, "tid": 9, "ts": 1757984781392816, "dur": 1298, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 4768, "tid": 9, "ts": 1757984781394146, "dur": 1230, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 4768, "tid": 9, "ts": 1757984781390245, "dur": 5708, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}