local UI_DailyTargetModel = {}

UI_DailyTargetModel.config = {["name"] = "UI_DailyTarget", ["layer"] = UILayerType.Normal, ["type"] = UIType.Normal, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_DailyTargetModel:Init(c)
    c.ui = {}    
    c.ui.m_btnClose = GetChild(c.uiGameObject,"root/m_btnClose",UEUI.Button)
    c.ui.m_goVerButtons = GetChild(c.uiGameObject,"root/m_goVerButtons")
    c.ui.m_btnTabScoreReward = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreReward",UEUI.Button)
    c.ui.m_imgScoreRewardLight = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreReward/m_imgScoreRewardLight",UEUI.Image)
    c.ui.m_imgScoreRewardRed = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreReward/m_imgScoreRewardRed",UEUI.Image)
    c.ui.m_btnTabScoreGet = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreGet",UEUI.Button)
    c.ui.m_imgScoreGetLight = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreGet/m_imgScoreGetLight",UEUI.Image)
    c.ui.m_imgScoreRewardRed = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabScoreGet/m_imgScoreRewardRed",UEUI.Image)
    c.ui.m_btnTabRank = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabRank",UEUI.Button)
    c.ui.m_imgRankLight = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabRank/m_imgRankLight",UEUI.Image)
    c.ui.m_imgScoreRewardRed = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabRank/m_imgScoreRewardRed",UEUI.Image)
    c.ui.m_btnTabShop = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabShop",UEUI.Button)
    c.ui.m_imgShopLight = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabShop/m_imgShopLight",UEUI.Image)
    c.ui.m_imgShopRed = GetChild(c.uiGameObject,"root/m_goVerButtons/m_btnTabShop/m_imgShopRed",UEUI.Image)
    c.ui.m_goDailyTarget = GetChild(c.uiGameObject,"root/m_goDailyTarget")
    c.ui.m_sliderDailyTarget = GetChild(c.uiGameObject,"root/m_goDailyTarget/m_sliderDailyTarget",UEUI.Slider)
    c.ui.m_txtTargetProgress = GetChild(c.uiGameObject,"root/m_goDailyTarget/m_sliderDailyTarget/m_txtTargetProgress",UEUI.Text)
    c.ui.m_txtDailyTargetTime = GetChild(c.uiGameObject,"root/m_goDailyTarget/m_txtDailyTargetTime",UEUI.Text)
    c.ui.m_imgCurDailyIcon = GetChild(c.uiGameObject,"root/m_goDailyTarget/targetBg1/m_imgCurDailyIcon",UEUI.Image)
    c.ui.m_imgDailyTargetIcon = GetChild(c.uiGameObject,"root/m_goDailyTarget/targetBg2/m_imgDailyTargetIcon",UEUI.Image)
    c.ui.m_txtTarget = GetChild(c.uiGameObject,"root/m_goDailyTarget/targetBg2/m_txtTarget",UEUI.Text)
    c.ui.m_goClock = GetChild(c.uiGameObject,"root/m_goClock")
    c.ui.m_imgClock = GetChild(c.uiGameObject,"root/m_goClock/m_imgClock",UEUI.Image)
    c.ui.m_txtTime = GetChild(c.uiGameObject,"root/m_goClock/m_imgClock/m_txtTime",UEUI.Text)
    c.ui.m_goRewardView = GetChild(c.uiGameObject,"root/m_goRewardView")
    c.ui.m_scrollview = GetChild(c.uiGameObject,"root/m_goRewardView/m_scrollview",UEUI.ScrollRect)
    c.ui.m_panelContent = GetChild(c.uiGameObject,"root/m_goRewardView/m_scrollview/Viewport/m_panelContent")
    c.ui.m_goPathwayView = GetChild(c.uiGameObject,"root/m_goPathwayView")
    c.ui.m_scrollviewPathway = GetChild(c.uiGameObject,"root/m_goPathwayView/m_scrollviewPathway",UEUI.ScrollRect)
    c.ui.m_panelPathwayContent = GetChild(c.uiGameObject,"root/m_goPathwayView/m_scrollviewPathway/Viewport/m_panelPathwayContent")
    c.ui.m_goRankView = GetChild(c.uiGameObject,"root/m_goRankView")
    c.ui.m_goRankScroll = GetChild(c.uiGameObject,"root/m_goRankView/m_goRankScroll")
    c.ui.m_goNoRank = GetChild(c.uiGameObject,"root/m_goRankView/m_goNoRank")
    c.ui.m_txtNoRank = GetChild(c.uiGameObject,"root/m_goRankView/m_goNoRank/m_txtNoRank",UEUI.Text)
    c.ui.m_goSelfRank = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank")
    c.ui.m_txtSelfrank = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_txtSelfrank",UEUI.Text)
    c.ui.m_imgSelfRank = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_imgSelfRank",UEUI.Image)
    c.ui.m_imgSelfHead = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/head/m_imgSelfHead",UEUI.Image)
    c.ui.m_txtSelfScore = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_txtSelfScore",UEUI.Text)
    c.ui.m_txtSelfName = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_txtSelfName",UEUI.Text)
    c.ui.m_imgSelfScoreIcon = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_imgSelfScoreIcon",UEUI.Image)
    c.ui.m_goRewardRoot = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_goRewardRoot")
    c.ui.m_goRewardList = GetChild(c.uiGameObject,"root/m_goRankView/m_goSelfRank/bg/m_goRewardRoot/Viewport/m_goRewardList")
    c.ui.m_goShopView = GetChild(c.uiGameObject,"root/m_goShopView")
    c.ui.m_TableViewVShop = GetChild(c.uiGameObject,"root/m_goShopView/m_TableViewVShop",CS.Mosframe.TableView)
    c.ui.m_TableViewVShop_Scroll = GetChild(c.uiGameObject,"root/m_goShopView/m_TableViewVShop",UEUI.ScrollRect)
    c.ui.m_goShopItemLine = GetChild(c.uiGameObject,"root/m_goShopView/m_goShopItemLine")
    c.ui.m_goShopItem = GetChild(c.uiGameObject,"root/m_goShopView/m_goShopItem")
    c.ui.m_btnTips = GetChild(c.uiGameObject,"root/m_btnTips",UEUI.Button)
    c.ui.m_goPerfabList = GetChild(c.uiGameObject,"m_goPerfabList")
    c.ui.m_goItemRank = GetChild(c.uiGameObject,"m_goPerfabList/m_goItemRank")
    c.ui.m_goReward = GetChild(c.uiGameObject,"m_goPerfabList/m_goReward")
    c.ui.m_goDailyTargetItem = GetChild(c.uiGameObject,"m_goPerfabList/m_goDailyTargetItem")
    c.ui.m_goTarget = GetChild(c.uiGameObject,"m_goTarget")
    c.ui.m_goPanel = GetChild(c.uiGameObject,"m_goTarget/m_goPanel")
    c.ui.m_goMidPlant = GetChild(c.uiGameObject,"m_goMidPlant")
    c.ui.m_goResource = GetChild(c.uiGameObject,"m_goResource")
    c.ui.m_goMedal = GetChild(c.uiGameObject,"m_goResource/m_goMedal")
    c.ui.m_doMedal = GetChild(c.uiGameObject,"m_goResource/m_goMedal/m_doMedal",TweenAnim)
    c.ui.m_txtMedal = GetChild(c.uiGameObject,"m_goResource/m_goMedal/m_txtMedal",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_DailyTargetModel