-- SlideRect 动态高度使用示例
local UI_DynamicHeightExample = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect")
local BaseSlideItem = require("UI.Common.BaseSlideItem")

-- 自定义 Item 类，继承自 BaseSlideItem
local DynamicItem = Class(BaseSlideItem)

function UI_DynamicHeightExample:OnCreate()
    self:InitScrollView()
    self:SetTestData()
end

function UI_DynamicHeightExample:InitScrollView()
    -- 创建 SlideRect 实例
    self.slideRect = SlideRect.new()
    
    -- 初始化 SlideRect，启用动态高度
    self.slideRect:Init(
        self.ui.m_scrollview,  -- ScrollRect 组件
        2,                     -- 垂直滑动
        1,                     -- syncCount
        true,                  -- 启用动态高度
        function(data)         -- 根据数据返回高度的回调函数
            -- 根据数据内容计算高度
            if data.type == "short" then
                return 100
            elseif data.type == "medium" then
                return 150
            elseif data.type == "long" then
                return 200
            else
                return 120  -- 默认高度
            end
        end
    )
    
    -- 创建 Item 实例
    self.itemList = {}
    for i = 1, 6 do  -- 创建6个复用的Item
        self.itemList[i] = DynamicItem.new()
        self.itemList[i]:Init(UEGO.Instantiate(self.ui.m_goItem.transform))
    end
    
    -- 设置 Items 到 SlideRect
    self.slideRect:SetItems(self.itemList, 10, Vector2.New(0, 0))  -- itemOffset=10
end

function UI_DynamicHeightExample:SetTestData()
    -- 测试数据，包含不同类型的内容
    local testData = {
        {type = "short", title = "短内容", content = "这是一个短内容"},
        {type = "medium", title = "中等内容", content = "这是一个中等长度的内容，包含更多文字"},
        {type = "long", title = "长内容", content = "这是一个很长的内容，包含大量文字，需要更多的显示空间来完整展示所有信息"},
        {type = "short", title = "短内容2", content = "另一个短内容"},
        {type = "medium", title = "中等内容2", content = "另一个中等长度的内容"},
        {type = "long", title = "长内容2", content = "另一个很长的内容示例"},
        {type = "short", title = "短内容3", content = "第三个短内容"},
        {type = "medium", title = "中等内容3", content = "第三个中等内容"},
    }
    
    -- 设置数据到 SlideRect
    self.slideRect:SetData(testData, 1)  -- 从第1个开始显示
end

-- DynamicItem 类实现
function DynamicItem:OnInit(transform)
    self.transform = transform
    self.rectTrans = self.transform:GetComponent(typeof(UE.RectTransform))
    
    -- 获取UI组件
    self.txtTitle = GetChild(transform, "txtTitle", UEUI.Text)
    self.txtContent = GetChild(transform, "txtContent", UEUI.Text)
    self.background = GetChild(transform, "background", UEUI.Image)
    
    -- 初始化高度和宽度
    self.height = self.rectTrans.rect.height
    self.width = self.rectTrans.rect.width
end

function DynamicItem:UpdateData(data, index)
    if not data then return end
    
    self.data = data
    self.index = index
    
    -- 更新显示内容
    self.txtTitle.text = data.title
    self.txtContent.text = data.content
    
    -- 根据内容类型设置不同的背景色
    if data.type == "short" then
        self.background.color = Color.New(0.8, 1, 0.8, 1)  -- 浅绿色
    elseif data.type == "medium" then
        self.background.color = Color.New(0.8, 0.8, 1, 1)  -- 浅蓝色
    elseif data.type == "long" then
        self.background.color = Color.New(1, 0.8, 0.8, 1)  -- 浅红色
    end
end

function DynamicItem:Resize()
    -- 关键：根据数据动态调整Item高度
    if not self.data then return end
    
    local newHeight = 0
    if self.data.type == "short" then
        newHeight = 100
    elseif self.data.type == "medium" then
        newHeight = 150
    elseif self.data.type == "long" then
        newHeight = 200
    else
        newHeight = 120
    end
    
    -- 更新 RectTransform 的高度
    self.rectTrans.sizeDelta = Vector2.New(self.rectTrans.sizeDelta.x, newHeight)
    
    -- 重要：更新 height 属性，SlideRect 会使用这个值进行位置计算
    self.height = newHeight
    
    -- 可选：强制重新布局
    SetUIForceRebuildLayout(self.rectTrans)
end

function DynamicItem:UpdatePosition(vec)
    self.rectTrans.anchoredPosition = vec
end

function DynamicItem:GetAnchoredPositon()
    return self.rectTrans.anchoredPosition
end

return UI_DynamicHeightExample
