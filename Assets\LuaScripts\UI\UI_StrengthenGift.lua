local UI_StrengthenGift = Class(BaseView)

function UI_StrengthenGift:OnInit()
    self.timerList = {}
    self.index = -1
end

function UI_StrengthenGift:OnCreate(isNew)
    self.isNew = isNew
    self:InitCard()

    -- 查找选中的卡牌 ID 对应的页签索引
    local currentIndex = 0
    self:InitPageView(currentIndex)
    
    self.timer = TimeMgr:CreateTimer(UIDefine.UI_StrengthenGift, function()
         self:HangUpTimerLogic()
        
    end, 1)
end

function UI_StrengthenGift:OnRefresh(param)
    if param == 1 then
        self:UpdateGiftList()
    end
end

function UI_StrengthenGift:onDestroy()
    if self.timer then
        TimeMgr:DestroyTimer(UIDefine.UI_StrengthenGift,self.timer)
    end
end

function UI_StrengthenGift:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnClose" then
        UI_CLOSE(UIDefine.UI_StrengthenGift)
    elseif name == "m_btnLeft" then
        --向左翻页
        self:PageToLast()
    elseif name == "m_btnRight" then
        --向右翻页
        self:PageToNext()
    end
end

--- 初始化翻页列表
--- @param showIndex number 初始页签
function UI_StrengthenGift:InitPageView(showIndex)
    local view = self.ui.m_PageView
    local maxCount = self.listLength
    local scroll = GetComponent(self.ui.m_PageView.gameObject, UEUI.ScrollRect)
    if maxCount == 0 then
        return
    end
    scroll.enabled = maxCount ~= 1
    
    view.OnUpdatePageViewCell =  function(index)

    end
    -- 页签发生改变
    view.OnPageChanged = function(index)
        self:ShowNowItem(index)
    end
    view:CreatePageView(maxCount)
    self:ShowNowItem(showIndex)
end

--- 翻到上一页
function UI_StrengthenGift:PageToLast()
    if self.index > 1 then
        self.ui.m_PageView:pageToLast()
    end
end

--- 翻到下一页
function UI_StrengthenGift:PageToNext()
    if self.index < self.listLength then
        self.ui.m_PageView:pageToNext()
    end
end

--- 初始化卡牌
function UI_StrengthenGift:InitCard()
    local list = self:GetFinalGiftList()
    self.cardList = {}
    self.listLength = #list
    for _,v in ipairs(list) do
        local obj = CreateGameObjectWithParent(self.ui.m_goGiftItem, self.ui.m_transCardList)
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_strong_gift,v.gift_id)
        if config then
            self:SetSingleGiftView(obj,config,v)
            local timerTxt = GetChild(obj,"timer/txt",UEUI.Text)
            table.insert(self.timerList,{finalTime = v.finalTime;txt = timerTxt;info = v})
        end
        table.insert(self.cardList,obj)

        local figure = GetChild(obj,"figure",UEUI.Image)
        local heroModule = HeroManager:GetHeroVoById(v.hero_id)
        local sprite = heroModule:GetHeroIcon()
        local rect = GetComponent(figure,UE.RectTransform)
        SetUIImage(figure,sprite,false)
        -- rect.anchoredPosition = string.find(sprite,"role2_2_0") and Vector2.New(-618,-135) or Vector2.New(-505,-135)
    end
end

--- 显示当前的页签
--- @param index number 页签索引
function UI_StrengthenGift:ShowNowItem(index, isAnim)
    local now = index + 1
    self.index = now
    
    SetActive(self.ui.m_btnLeft,now > 1)
    SetActive(self.ui.m_btnRight,now < self.listLength)
end

--判断礼包数量类型
function UI_StrengthenGift:CheckGiftType(data)
    local list = {}
    if data.reward_main then
        local temp = Split1(data.reward_main,"|")
        table.insert(list,{id = v2n(temp[1]);count = v2n(temp[2])})
    end
    
    if data.reward_other then
        local temp = Split1(data.reward_other,";")
        for k,v in ipairs(temp) do
            local temp1 = Split1(v,"|")
            table.insert(list,{id = v2n(temp1[1]);count = v2n(temp1[2])})
        end
    end
    
    return table.count(list),list
end

function UI_StrengthenGift:SetSingleGiftView(obj,config,msg)
    local titleTxt = GetChild(obj,"title/Text",UEUI.Text)
    local discountTxt = GetChild(obj,"btn/discount/Text",UEUI.Text)
    local discountObj = GetChild(obj,"discount")
    local btn = GetChild(obj,"btn",UEUI.Button)
    local priceTxt = GetChild(obj,"btn/Text",UEUI.Text)
    
    titleTxt.text = LangMgr:GetLang(config.desc_langid)
    discountTxt.text = LangMgr:GetLang(config.discount)

    local type,typeList = self:CheckGiftType(config)

    local style1 = GetChild(obj,"style1")
    local style2 = GetChild(obj,"style2")
    local style3 = GetChild(obj,"style3")

    SetActive(style1,type == 1)
    SetActive(style2,type == 2)
    SetActive(style3,type == 3)

    local goList = {}
    local target = false
    if type == 1 then
        goList = {"gift1"}
        target = style1
    elseif type == 2 then
        goList = {"gift1","gift2"}
        target = style2
    elseif type == 3 then
        goList = {"gift1","gift2","gift3"}
        target = style3
    end

    for m,n in ipairs(goList) do
        local giftObj = GetChild(target,n)
        self:SetSingleRewardView(giftObj,typeList[m])
    end

    local payConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.payment,config.pay_id)
    priceTxt.text = LangMgr:GetLang(payConfig.price_langid)
    PurchaseManager:CreateScoreTag(config.pay_id,btn.transform)
    
    RemoveUIComponentEventCallback(btn,UEUI.Button)
    AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
        if StrongGiftManager:IsNotActive(config.time,msg.create_timestamp) then
            --"该礼包已过期，不能购买了"
            UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(212))
            return 
        end
        PaymentConfig:ShowPay(config.pay_id)
        UI_CLOSE(UIDefine.UI_StrengthenGift)
    end)
end

function UI_StrengthenGift:SetSingleRewardView(obj,data)
    local titleTxt = GetChild(obj,"title",UEUI.Text)
    local icon1 = GetChild(obj,"icon1",UEUI.Image)
    local icon2 = GetChild(obj,"icon2",UEUI.Image)
    local count = GetChild(obj,"count",UEUI.Text)

    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,data.id)
    if config then
        titleTxt.text = LangMgr:GetLang(config.id_lang)
        SetUIImage(icon1, config.icon_b, false)
        SetUIImage(icon2, config.icon_b, false)
        count.text = "x"..NumToGameString(v2n(data.count))
    end
end

--倒计时
function UI_StrengthenGift:HangUpTimerLogic()
    local list = {}
    for k,v in ipairs(self.timerList) do
        local time = v.finalTime - TimeMgr:GetServerTime()
        if time <= 0 then
            table.insert(list,v.info)
            v.txt.text = "00:00:00"
        else
            if not IsNil(v.txt) then
                v.txt.text = TimeMgr:ConverSecondToString(time)
            end
        end
    end
    
    if next(list) ~= nil then
        StrongGiftManager:DeleteGift(list)
    end
end

function UI_StrengthenGift:UpdateGiftList()
    for k,v in ipairs(self.cardList) do
        SetActive(v,false)
    end
    
    local list = self:GetFinalGiftList()
    self.listLength = #list
    
    local offset = self.listLength - #self.cardList
    if offset > 0 then
        for i = 1,offset do
            local obj = CreateGameObjectWithParent(self.ui.m_goGiftItem, self.ui.m_transCardList)
            table.insert(self.cardList,obj)
        end
    end
    self.timerList = {}
    for k,v in ipairs(list) do
        local target = self.cardList[k]
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_strong_gift,v.gift_id)
        if config then
            self:SetSingleGiftView(target,config,v)
            local timerTxt = GetChild(target,"timer/txt",UEUI.Text)
            table.insert(self.timerList,{finalTime = v.finalTime;txt = timerTxt,info = v})
            SetActive(target,true)
        end

        local figure = GetChild(target,"figure",UEUI.Image)
        local heroModule = HeroManager:GetHeroVoById(v.hero_id)
        local sprite = heroModule:GetHeroIcon()
        local rect = GetComponent(figure,UE.RectTransform)
        SetUIImage(figure,sprite,false)
        rect.anchoredPosition = string.find(sprite,"role2_2_0") and Vector2.New(-618,-135) or Vector2.New(-505,-135)
    end
   
    -- 查找选中的卡牌 ID 对应的页签索引
    local currentIndex = 0
    self:InitPageView(currentIndex)
    self:CheckClose()
end

function UI_StrengthenGift:CheckClose()
    local list = self:GetFinalGiftList()
    if table.count(list) == 0 then
        UI_CLOSE(UIDefine.UI_StrengthenGift)
    end
end

--包裹一个逻辑（如果是推送的礼包优先显示新推送的，普通操作打开的礼包显示时间快要截止的礼包）
function UI_StrengthenGift:GetFinalGiftList()
    local list = StrongGiftManager:GetGiftList()
    if self.isNew then
        table.sort(list,function(a, b)
            return a.create_timestamp > b.create_timestamp
        end)
    end
    return list
end

return UI_StrengthenGift