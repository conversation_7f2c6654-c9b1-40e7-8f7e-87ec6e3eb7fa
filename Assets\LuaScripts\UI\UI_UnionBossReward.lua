local UI_UnionBossReward = Class(BaseView)

local BagItem = require("UI.BagItem")

function UI_UnionBossReward:OnInit()
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
    
    self.curType = 0;
    self.itemList = {};
    self.rewardList = {};
    self.rank = -1;
end

function UI_UnionBossReward:OnCreate(param)
    local limit = v2n(LeagueManager:GetConfigById(110));
    self.ui.m_txtTip.text = LangMgr:GetLangFormat(70000329, limit);
    
    local leagueDetails = LeagueManager:GetMyLeagueDetails();
    if leagueDetails then
        local showStr = LangMgr:GetLangFormat(70000330, leagueDetails.members);
        if leagueDetails.members >= limit then
            showStr = showStr .. LangMgr:GetLang(70000332);
        else
            showStr = showStr .. GetStrRichColor(LangMgr:GetLang(70000331), "ff0030");
        end
        self.ui.m_txtLimit.text = showStr;
    end
    
    self:OnSelectType(1);
end

function UI_UnionBossReward:OnRefresh(param)
    
end

function UI_UnionBossReward:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
end

function UI_UnionBossReward:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif string.startswith(name, "m_tog") then
        if self.ui[name].isOn == false then return end

        local index = string.gsub(name, "m_tog", "");
        index = v2n(index);
        if index then
            if index == 2 and self.rank == -1 then
                LeagueManager:OnRequestBossRankDamage(function(respData)
                    local selfRankInfo = respData.list.self;
                    if selfRankInfo then
                        self.rank = selfRankInfo.rank.rank
                    end
                    self:OnSelectType(2);
                end);
            else
                self:OnSelectType(index);
            end
        end
    end
end

function UI_UnionBossReward:OnSelectType(index)
    if self.curType == index then return end
    self.curType = index;
    
    local list;
    if self.curType == 1 then
        list = LeagueManager.bossList;
        SetUISize(self.ui.m_scrollview, 900, 1245);
    else
        list = ConfigMgr:GetData(ConfigDefine.ID.union_boss_rank);
        SetUISize(self.ui.m_scrollview, 900, 940);
    end
    self.ui.m_scrollview:StopMovement();
    SetUIPos(self.ui.m_scrollview.content, 0, 0);
    SetActive(self.ui.m_goTitle, self.curType == 1);
    SetActive(self.ui.m_goTip, self.curType == 2);
    
    local count = #list;
    local num = #self.itemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    for i = 1, len do
        local item = self.itemList[i];
        if item == nil then
            item = CreateGameObjectWithParent(self.ui.m_goRank, rootTrans);
            table.insert(self.itemList, item);
        end
        SetActive(item, i <= count);

        if i <= count then
            self:OnUpdateItem(item, list[i], i);
        end
    end
end

function UI_UnionBossReward:OnUpdateItem(item, data, index)
    if not self.rewardList[index] then
        self.rewardList[index] = {};
        self.rewardList[index][1] = {};
        self.rewardList[index][2] = {};
        self.rewardList[index][3] = {};
        self.rewardList[index][4] = {};
    end
    
    local bossObj = GetChild(item, "bossObj");
    local rankObj = GetChild(item, "rankObj");
    if self.curType == 1 then
        local bg = GetChild(bossObj, "bg", UEUI.Image);
        local selectBg = GetChild(bossObj, "selectBg", UEUI.Image);
        local bossBg = GetChild(bossObj, "bossBg", UEUI.Image);
        local bossImg = GetChild(bossObj, "bossBg/bossImg", UEUI.Image);
        local downBg = GetChild(bossObj, "bossBg/downBg", UEUI.Image);
        local levelTxt = GetChild(bossObj, "bossBg/levelTxt", UEUI.Text);
        local starObj = GetChild(bossObj, "bossBg/starObj");
        local rewardObj1 = GetChild(bossObj, "rewardObj1");
        local rewardObj2 = GetChild(bossObj, "rewardObj2");
        local rewardObj3 = GetChild(bossObj, "rewardObj3");
        
        local isSelect = LeagueManager.boss_id == data.boss_id;
        SetActive(bg, not isSelect);
        SetActive(selectBg, isSelect);
        SetUISize(item, 900, isSelect and 325 or 300);
        SetUIPos(bossBg, -277, isSelect and -8 or 0);
        
        local title1 = GetChild(bossObj, "m_txtAuto70000325", UEUI.Text);
        local title2 = GetChild(bossObj, "m_txtAuto70000326", UEUI.Text);
        local title3 = GetChild(bossObj, "m_txtAuto70000327", UEUI.Text);
        local colorStr = isSelect and GetColorByHex("BB610C") or GetColorByHex("3979CE");
        title1.color = colorStr;
        title2.color = colorStr;
        title3.color = colorStr;
        
        local imgPath = isSelect and "Sprite/ui_public/windows_line2_slg.png" or "Sprite/ui_public/windows_line_slg.png";
        local lineObj = GetChild(bossObj, "lineObj", UE.Transform);
        for i = 1, lineObj.childCount do
            local line = GetComponent(lineObj:GetChild(i - 1), UEUI.Image);
            SetUIImage(line, imgPath, false);
        end

        local monsterInfo = LeagueManager:GetMonsterInfo(data.boss_id);
        if monsterInfo then
            local starNum = HeroManager:GetStarOrderByLv(monsterInfo.star);
            for i = 1, 5 do
                local starSp = GetChild(starObj, "star" .. i);
                if starSp then
                    SetActive(starSp, i <= starNum);
                end
            end
            levelTxt.text = "Lv." .. monsterInfo.level;
        end

        local heroConfig = HeroManager:GetHeroConfigById(monsterInfo.id);
        SetUIImage(bossImg, heroConfig.resources_jingtai, false);
        
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_boss, data.boss_id);
        if config then
            local quality = config.boss_quality;
            if quality == 4 then
                SetUIImage(bossBg, "Sprite/ui_lianmeng/lianmengboss_jiangliyulan_boss_boss1.png", false);
                SetUIImage(downBg, "Sprite/ui_lianmeng/lianmengboss_jiangliyulan_boss_boss1_1.png", false);
            else
                SetUIImage(bossBg, "Sprite/ui_lianmeng/lianmengboss_jiangliyulan_boss_boss2.png", false);
                SetUIImage(downBg, "Sprite/ui_lianmeng/lianmengboss_jiangliyulan_boss_boss2_1.png", false);
            end
            UnifyOutline(levelTxt, quality == 4 and "634DBD" or "A63E00")
            
            local rewardList = self.rewardList[index];
            self:OnUpdateReward(rewardObj1, rewardList[1], config.fight_reward, 0.6);
            self:OnUpdateReward(rewardObj2, rewardList[2], config.win_reward, 0.6);
            self:OnUpdateReward(rewardObj3, rewardList[3], config.kill_rewad, 0.6);
        end
    else
        local bg = GetChild(rankObj, "bg", UEUI.Image);
        local rankTxt = GetChild(rankObj, "rankTxt", UEUI.Text);
        local noneTxt = GetChild(rankObj, "m_txtAuto53241129", UEUI.Text);
        local rewardObj = GetChild(rankObj, "rewardObj");
        
        SetUISize(item, 900, 152);
        
        local isSelect = false;
        local rangeDic = Split1(data.rank, "|");
        if #rangeDic == 1 then
            isSelect = v2n(rangeDic[1]) == self.rank;
        elseif #rangeDic == 2 then
            isSelect = v2n(rangeDic[1]) <= self.rank and v2n(rangeDic[2]) >= self.rank;
        end
        local bgPath = isSelect and "Sprite/ui_public/windows_list2_slg.png" or "Sprite/ui_public/windows_list1_slg.png";
        SetUIImage(bg, bgPath, false);
        
        rankTxt.text = LangMgr:GetLang(data.lang_id);
        rankTxt.color = isSelect and GetColorByHex("BB610C") or GetColorByHex("3F77CC");
        noneTxt.color = isSelect and GetColorByHex("BE9C36") or GetColorByHex("58AAF8");
        
        local rewardStr = data.reward;
        SetActive(noneTxt, rewardStr == nil);
        self:OnUpdateReward(rewardObj, self.rewardList[index][4], rewardStr, 0.76);
    end
    SetActive(bossObj, self.curType == 1);
    SetActive(rankObj, self.curType == 2);
end

function UI_UnionBossReward:OnUpdateReward(rootTrans, itemList, rewardStr, scale)
    if rewardStr then
        local rewardList = Split2(rewardStr, ";", "|")
        local count = #rewardList;
        local num = #itemList;
        local len = count > num and count or num;
        for i = 1, len do
            local item = itemList[i];
            if item == nil then
                item = BagItem.new();
                item:Create(rootTrans);
                table.insert(itemList, item);
            end
            SetActive(item.go, i <= count);

            if i <= count then
                item:SetScale(scale, scale);
                item:UpdateInfo(BagItemModule.new({ id = v2n(rewardList[i].id), num = v2n(rewardList[i].count) }));
            end
        end
    else
        for i = 1, #itemList do
            SetActive(itemList[i], false);
        end
    end
end

return UI_UnionBossReward