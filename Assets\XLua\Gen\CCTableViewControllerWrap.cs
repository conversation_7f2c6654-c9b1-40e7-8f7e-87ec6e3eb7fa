﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using XLua;
using System.Collections.Generic;


namespace XLua.CSObjectWrap
{
    using Utils = XLua.Utils;
    public class CCTableViewControllerWrap 
    {
        public static void __Register(RealStatePtr L)
        {
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			System.Type type = typeof(CCTableViewController);
			Utils.BeginObjectRegister(type, L, translator, 0, 12, 8, 8);
			
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "Awake", _m_Awake);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetNumberOfRowsForTableView", _m_GetNumberOfRowsForTableView);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetSizeForRowInTableView", _m_GetSizeForRowInTableView);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetCellForRowInTableView", _m_GetCellForRowInTableView);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "HideCellForVisibleCells", _m_HideCellForVisibleCells);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ReloadFinished", _m_ReloadFinished);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "OnCellHeightChanged", _m_OnCellHeightChanged);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ReloadData", _m_ReloadData);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ShowCenter", _m_ShowCenter);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ShowBottom", _m_ShowBottom);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetCellAtRow", _m_GetCellAtRow);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "updateTableView", _m_updateTableView);
			
			
			Utils.RegisterFunc(L, Utils.GETTER_IDX, "m_tableView", _g_get_m_tableView);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "m_numRows", _g_get_m_numRows);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "GetCellCount", _g_get_GetCellCount);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "GetCellSize", _g_get_GetCellSize);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "UpdateCell", _g_get_UpdateCell);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "LoadFinish", _g_get_LoadFinish);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "HideCell", _g_get_HideCell);
            Utils.RegisterFunc(L, Utils.GETTER_IDX, "UpdateCellData", _g_get_UpdateCellData);
            
			Utils.RegisterFunc(L, Utils.SETTER_IDX, "m_tableView", _s_set_m_tableView);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "m_numRows", _s_set_m_numRows);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "GetCellCount", _s_set_GetCellCount);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "GetCellSize", _s_set_GetCellSize);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "UpdateCell", _s_set_UpdateCell);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "LoadFinish", _s_set_LoadFinish);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "HideCell", _s_set_HideCell);
            Utils.RegisterFunc(L, Utils.SETTER_IDX, "UpdateCellData", _s_set_UpdateCellData);
            
			
			Utils.EndObjectRegister(type, L, translator, null, null,
			    null, null, null);

		    Utils.BeginClassRegister(type, L, __CreateInstance, 1, 0, 0);
			
			
            
			
			
			
			Utils.EndClassRegister(type, L, translator);
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CreateInstance(RealStatePtr L)
        {
            
			try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
				if(LuaAPI.lua_gettop(L) == 1)
				{
					
					CCTableViewController gen_ret = new CCTableViewController();
					translator.Push(L, gen_ret);
                    
					return 1;
				}
				
			}
			catch(System.Exception gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
			}
            return LuaAPI.luaL_error(L, "invalid arguments to CCTableViewController constructor!");
            
        }
        
		
        
		
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Awake(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                    gen_to_be_invoked.Awake(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetNumberOfRowsForTableView(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    CCTableView _tableView = (CCTableView)translator.GetObject(L, 2, typeof(CCTableView));
                    
                        int gen_ret = gen_to_be_invoked.GetNumberOfRowsForTableView( _tableView );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetSizeForRowInTableView(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    CCTableView _tableView = (CCTableView)translator.GetObject(L, 2, typeof(CCTableView));
                    int _row = LuaAPI.xlua_tointeger(L, 3);
                    
                        UnityEngine.Vector2 gen_ret = gen_to_be_invoked.GetSizeForRowInTableView( _tableView, _row );

                        translator.PushUnityEngineVector2(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetCellForRowInTableView(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    CCTableView _tableView = (CCTableView)translator.GetObject(L, 2, typeof(CCTableView));
                    int _row = LuaAPI.xlua_tointeger(L, 3);
                    
                        CCTableViewCell gen_ret = gen_to_be_invoked.GetCellForRowInTableView( _tableView, _row );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_HideCellForVisibleCells(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    CCTableViewCell _tableViewCell = (CCTableViewCell)translator.GetObject(L, 2, typeof(CCTableViewCell));
                    int _row = LuaAPI.xlua_tointeger(L, 3);
                    
                    gen_to_be_invoked.HideCellForVisibleCells( _tableViewCell, _row );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ReloadFinished(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    CCTableView _tableView = (CCTableView)translator.GetObject(L, 2, typeof(CCTableView));
                    
                    gen_to_be_invoked.ReloadFinished( _tableView );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_OnCellHeightChanged(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _row = LuaAPI.xlua_tointeger(L, 2);
                    float _newHeight = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    gen_to_be_invoked.OnCellHeightChanged( _row, _newHeight );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ReloadData(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 2&& LuaTypes.LUA_TBOOLEAN == LuaAPI.lua_type(L, 2)) 
                {
                    bool _isClear = LuaAPI.lua_toboolean(L, 2);
                    
                    gen_to_be_invoked.ReloadData( _isClear );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 1) 
                {
                    
                    gen_to_be_invoked.ReloadData(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to CCTableViewController.ReloadData!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ShowCenter(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _index = LuaAPI.xlua_tointeger(L, 2);
                    System.Action _call = translator.GetDelegate<System.Action>(L, 3);
                    
                    gen_to_be_invoked.ShowCenter( _index, _call );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ShowBottom(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    System.Action _call = translator.GetDelegate<System.Action>(L, 2);
                    
                    gen_to_be_invoked.ShowBottom( _call );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetCellAtRow(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _row = LuaAPI.xlua_tointeger(L, 2);
                    
                        CCTableViewCell gen_ret = gen_to_be_invoked.GetCellAtRow( _row );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_updateTableView(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                    gen_to_be_invoked.updateTableView(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_m_tableView(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.m_tableView);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_m_numRows(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                LuaAPI.xlua_pushinteger(L, gen_to_be_invoked.m_numRows);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_GetCellCount(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.GetCellCount);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_GetCellSize(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.GetCellSize);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_UpdateCell(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.UpdateCell);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_LoadFinish(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.LoadFinish);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_HideCell(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.HideCell);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_UpdateCellData(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                translator.Push(L, gen_to_be_invoked.UpdateCellData);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_m_tableView(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.m_tableView = (CCTableView)translator.GetObject(L, 2, typeof(CCTableView));
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_m_numRows(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.m_numRows = LuaAPI.xlua_tointeger(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_GetCellCount(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.GetCellCount = translator.GetDelegate<DeletegateCall.FuncCount>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_GetCellSize(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.GetCellSize = translator.GetDelegate<DeletegateCall.FuncSize>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_UpdateCell(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.UpdateCell = translator.GetDelegate<DeletegateCall.FuncUpdateCell>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_LoadFinish(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.LoadFinish = translator.GetDelegate<DeletegateCall.FuncLoadFinish>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_HideCell(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.HideCell = translator.GetDelegate<DeletegateCall.FuncHideCell>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_UpdateCellData(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			
                CCTableViewController gen_to_be_invoked = (CCTableViewController)translator.FastGetCSObj(L, 1);
                gen_to_be_invoked.UpdateCellData = translator.GetDelegate<DeletegateCall.FuncUpdateCellData>(L, 2);
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
		
		
		
		
    }
}
