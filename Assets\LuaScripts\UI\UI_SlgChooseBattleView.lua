local UI_SlgChooseBattleView = Class(BaseView)

local SlideRect = require("UI.Common.SlideRect")
local ItemBase = require("UI.Common.BaseSlideItem")
local GoSlgHeroItem = require("UI.GoSlgHeroItem")
local fight = require("Proto.Handler.NetRequest.Fight")
local HeroItem = Class(ItemBase)
local CustomHead = require "UI.CustomHead"

local DragItem = Class()

local BuffConfigList = {
    [1] = 4001,
    [2] = 4002,
    [3] = 4003,
}

local heroKindIcon = {
    [130] = "Sprite/ui_slg_zhandou_pve/buff_icon_tanke.png",
    [131] = "Sprite/ui_slg_zhandou_pve/buff_icon_feiji.png",
    [132] = "Sprite/ui_slg_zhandou_pve/buff_icon_daodan.png",
}

local tempBattleList = {}
local trainDefendList = {}

function UI_SlgChooseBattleView:OnInit()
    
end

function UI_SlgChooseBattleView:OnCreate(param)
    self.isEdit = false
    self:InitViewTempBattleList()
	self.battleScene = BattleSceneManager:GetBattleScene()
	self.battleScene:ShowBottomSquare(true)
	BattleSceneManager:Clear()
    self.team_type = BattleSceneManager:GetTeamType()
    if self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
        self.battleParam = BattleSceneManager:GetLevelData()
    elseif self.team_type == BATTLE_TEAM_TYPE.TOWER or self.team_type == BATTLE_TEAM_TYPE.TOWER_TANK or self.team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE or self.team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE then
        self.battleParam = BattleSceneManager:GetTowerData()
    elseif self.team_type == BATTLE_TEAM_TYPE.UNION_BOSS then
        self.battleParam = BattleSceneManager:GetUnionBossData()
        self.unionBossId = self.battleParam.configId
    elseif self.team_type == BATTLE_TEAM_TYPE.ARENA_SELF then
        self.battleParam = BattleSceneManager:GetArenaData()
        self.enemyPlayerId = self.battleParam.roleId
        self.isRobot = self.battleParam.isRobot == 1
        self.enemyPlayerPower = self.battleParam.power
    elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_LOOT then
        self.battleParam = BattleSceneManager:GetPlunderWagonData()
        self.plunderId = self.battleParam.PlunderId
        self.plunderPlayerIdId = self.battleParam.playerId
        self.plunderIndex = self.battleParam.index
    elseif TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
        self.battleParam = BattleSceneManager:GetTradeTrainDefendData()
        self.trainIndex = self.battleParam.trainIndex
    elseif self.team_type == BATTLE_TEAM_TYPE.WORLD_BOSS then
        self.battleParam = BattleSceneManager:GetWorldBossData()
        self.worldBossData = self.battleParam.bossData
    elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
        self.battleParam = BattleSceneManager:GetTradeTrainRobData()
        self.trainData = self.battleParam.trainData
        self.captain = self.trainData.captain
        self.beRobbedTeams = self.trainData.teams
    end
	
    self.toggleTab = {}
    local isShowEdit = self:IsShowEditRoot()
    self.toggle_prefab = GET_UI(self.uiGameObject, "m_tog", "RectTransform")
    self.toggle_perant = GET_UI(self.uiGameObject, "m_togG", "RectTransform")
    if isShowEdit then
        self.toggle_prefab = GET_UI(self.uiGameObject, "m_editTog", "RectTransform")
        self.toggle_perant = GET_UI(self.uiGameObject, "m_editTogG", "RectTransform")
    end
    
    self.heroItemList = {}
    self.selectTogIndex = -1
    self.selectTeamBtnIndex = 1
    
    self:InitTrainDefendList()
    self:InitTrainRobList()
    self:InitTopFightBattleList()
    --self.curTrainAttackList_1 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1)
    --self.curTrainAttackList_2 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2)
    --self.curTrainAttackList_3 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3)
    
    self.curBattleList = self:GetInBattleList()
	self.dragItems = {}
    self.heroRect = GetChild(self.uiGameObject,"bg/Bottom/m_goNormalRoot/listBg/m_imgscrollviewBg/m_scrollviewHero",UEUI.ScrollRect)
    self.editHeroRect = GetChild(self.uiGameObject,"bg/Bottom/m_goEditListRoot/listBg/scrollviewBg/m_scrollviewEditHero",UEUI.ScrollRect)
    
    SetActive(self.toggle_prefab, false)
    SetActive(self.ui.m_goHeroItem, false)

    self.CustomHead_Me = CustomHead.new()
    self.CustomHead_Me:CreateHead(self.ui.m_goHeadMePos.transform)
    self.CustomHead_Me:SetClickCall(
            function()
                
            end)
    
    self.CustomHead_Enemy = CustomHead.new()
    self.CustomHead_Enemy:CreateHead(self.ui.m_goHeadEnemyPos.transform)
    self.CustomHead_Enemy:SetClickCall(
            function()
                
            end)
    
    self:InitUI()
    if self.team_type == BATTLE_TEAM_TYPE.UNION_BOSS or self.team_type == BATTLE_TEAM_TYPE.WORLD_BOSS then
        local isEmpty = true
        for i, v in ipairs(self.curBattleList) do
            if not IsTableEmpty(v) then
                isEmpty = false
                break
            end
        end
        if isEmpty then
            self:OneKeyChooseHero()
        end
    end
    EventMgr:Add(EventID.SELECT_HERO_BATTLE,self.SelectHeroBattle,self)
	self:CheckArenaGuide()
end

function UI_SlgChooseBattleView:CheckIsEdit()
    if not self.isEdit then
        self.isEdit = true
    end
end

function UI_SlgChooseBattleView:InitTopFightBattleList()
    self.curTopFightList_1 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM1)
    self.curTopFightList_2 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM2)
    self.curTopFightList_3 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM3)
end

function UI_SlgChooseBattleView:InitTrainDefendList()
    trainDefendList = {
        [TRAIN_CONST.RoleTrainIndex1] = {BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_1,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_2,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_1_3},
        [TRAIN_CONST.RoleTrainIndex2] = {BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_1,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_2,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_2_3},
        [TRAIN_CONST.RoleTrainIndex3] = {BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_1,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_2,BATTLE_TEAM_TYPE.TRADE_TRAIN_DEFEND_3_3},
    }
    self.curTrainDefendList_1 = {}
    self.curTrainDefendList_2 = {}
    self.curTrainDefendList_3 = {}
    if self.trainIndex then
        local typeList = trainDefendList[self.trainIndex]
        self.curTrainDefendList_1 = self:InitTrainTeamList(typeList[1])
        self.curTrainDefendList_2 = self:InitTrainTeamList(typeList[2])
        self.curTrainDefendList_3 = self:InitTrainTeamList(typeList[3])
    end
end

function UI_SlgChooseBattleView:InitTrainRobList()
    self.curTrainRobList_1 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1)
    self.curTrainRobList_2 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2)
    self.curTrainRobList_3 = self:InitTrainTeamList(BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3)
end

function UI_SlgChooseBattleView:CheckArenaGuide()
    if self.team_type == BATTLE_TEAM_TYPE.ARENA_DEFENDER then
        -- 引导一次
        local isGuide = NetGlobalData:GetActivityGuideCache("JJC")
        if not isGuide then
            NetGlobalData:SetActivityGuideCache("JJC")
            self.isGuide = true
            self:Guide()
        end
    end
end

-- 打开新手引导
function UI_SlgChooseBattleView:Guide()
    local function Guide3()
        UI_CLOSE(UIDefine.UI_GuideMask)
        local isEmpty = self:GetTempBattleListIsEmpty()
        if isEmpty then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000237))
            return
        end
        if self:IsNeedManualSave() then
            local function syncCallBack()
                EventMgr:Dispatch(EventID.BATTLE_MANUAL_SAVE)
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000593))
                BattleSceneManager:CloseScene()
            end
            self:SyncViewTempBattleList(syncCallBack)
        end
        UI_UPDATE(UIDefine.UI_JJcView,3)
    end
    
    local function Guide2()
        local centerPos = UIRectPosFit(self.ui.m_btnEditSave)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {3, 740, 222},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {5, 5},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() Guide3() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
            {-1.5, 0, 70000664},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 第一步
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        self:OneKeyChooseHero()
        Guide2()
    end

    -- 第一步自动上阵
    local centerPos = UIRectPosFit(self.ui.m_btnEditOnClickUp)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {2, 2},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {1, 0, 70000664},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

function UI_SlgChooseBattleView:InitViewTempBattleList()
    for i = 1, 5 do
        tempBattleList[i] = {}
    end
end

function UI_SlgChooseBattleView:InitUI()
	--bgm
	local bgmId =  GlobalConfig:GetNumber(10900,0)
	if bgmId and bgmId > 0 then
		BattleSceneManager:PlayBGM(bgmId)
	end
    self.ui.m_txtEditTitle.text = LangMgr:GetLang(70000534)
    --self.heroSlideRect = SlideRect.new()
    --self.heroSlideRect:Init(self.ui.m_scrollviewHero, 1)
    --local item = self.ui.m_goHeroItem.transform
    --local heroItemList = {}
    --for i = 1, 6, 1 do
    --    heroItemList[i] = HeroItem.new()
    --    heroItemList[i]:Init(UEGO.Instantiate(item))
    --end
    --self.heroSlideRect:SetItems(heroItemList, 10, Vector2.New(0, 0))
    --self.heroItemList = heroItemList

    self.CustomHead_Me:SetHeadByID(NetUpdatePlayerData:GetPlayerInfo().head)
    self.CustomHead_Me:SetHeadBorderByID(NetUpdatePlayerData:GetPlayerInfo().headBorder)

    -- 一键上阵功能开启
    local isOpen = DungeonManager:GetOneKeyChooseHeroIsopen(self.team_type)
    local lockImg = "Sprite/ui_slg_zhandou_pve/chuzhan_but_shangzhen2.png"
    if isOpen then
        lockImg = "Sprite/ui_slg_zhandou_pve/chuzhan_but_shangzhen1.png"
    end
    --SetImageSprite(self.ui.m_imgOnClickUp,lockImg,false)
    SetActive(self.ui.m_imgUpLock,not isOpen)
    --local isNeedAutoSave = HeroManager:CheckIsNeedAutoSaveByType(self.team_type)
    local isShowEdit = self:IsShowEditRoot()
    SetActive(self.ui.m_goNormalRoot,not isShowEdit)
    SetActive(self.ui.m_goEditListRoot,isShowEdit)
    SetActive(self.ui.m_goEditRoot,isShowEdit)
    SetActive(self.ui.m_goPVPRoot,not isShowEdit)
    
    self:InitToggleList()
    self:InitBattleList()
    self:InitEnemyBattleList()
    self:ReFreshHead()
    self:ReFreshEnemyHead()
    self:RefreshHeroPower()
    self:RefreshMainBuffIcon()
    self:RefreshEnemyMainBuffIcon()
    self:InitTradeTrainRoot()
end

function UI_SlgChooseBattleView:IsShowEditRoot()
    local isShow = false
    if self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_1 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_2
            or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_3 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_4 
            or self.team_type == BATTLE_TEAM_TYPE.ARENA_DEFENDER or TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type)
            or TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) or TopFightManager:GetIsTopFightBattleType(self.team_type) then
        isShow = true
    end
    return isShow
end

-- 需要手动保存
function UI_SlgChooseBattleView:IsNeedManualSave()
    local isShow = self:IsShowEditRoot()
    local isNeddManual = false
    if self.team_type == BATTLE_TEAM_TYPE.ARENA_DEFENDER or TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type)
            or TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) or TopFightManager:GetIsTopFightBattleType(self.team_type) then
        isNeddManual = true
    end
    return isShow and isNeddManual
end

function UI_SlgChooseBattleView:ReFreshEnemyHead()
    local enemyHeadPath = ""
    local heroItemId
    SetActive(self.ui.m_goHeadEnemyPos,false)
    if self.team_type == BATTLE_TEAM_TYPE.TOWER or self.team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE
            or self.team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE or self.team_type == BATTLE_TEAM_TYPE.TOWER_TANK then
        local towerId = self.battleParam.tower_id
        local towerCfg = TowerManager:GetTowerConfigById(towerId)
        heroItemId = towerCfg.battle_icon
    elseif self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
        local dungeonId = self.battleParam.id
        local dungeonCfg = DungeonManager:GetDungeonConfigByID(dungeonId)
        heroItemId = dungeonCfg.battle_icon
    elseif self.team_type == BATTLE_TEAM_TYPE.UNION_BOSS then
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_boss, self.unionBossId)
        heroItemId = config.boss_icon
    elseif self.team_type == BATTLE_TEAM_TYPE.ARENA_SELF then
        if self.isRobot then
            local robotConfig = JJcManager:GetRobotDataById(self.enemyPlayerId)
            if robotConfig then
                --heroItemId = robotConfig.battle_icon
                SetActive(self.ui.m_goHeadEnemyPos,true)
                local cfgHeadSet =  ConfigMgr:GetDataByKey(ConfigDefine.ID.head_set, "item_id",robotConfig.battle_icon)
                if cfgHeadSet then
                    self.CustomHead_Enemy:SetHeadByID(cfgHeadSet.id)
                    self.CustomHead_Enemy:SetHeadBorderByID(robotConfig.battle_icon2)
                end
            end
        end
    elseif self.team_type == BATTLE_TEAM_TYPE.WORLD_BOSS then
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_world_boss, WorldBossManager.boss_id)
        heroItemId = config.boss_icon
    end
    if heroItemId then
        local config = ItemConfig:GetDataByID(heroItemId)
        if config then
            if config["icon_ss"] then
                enemyHeadPath = config["icon_ss"]
            end
        end
    end
    if enemyHeadPath ~= "" then
        SetImageSprite(self.ui.m_imgHeadOther,enemyHeadPath,false,function()
            SetActive(self.ui.m_imgHeadOther,true)
        end)
    end
end

function UI_SlgChooseBattleView:ReFreshHead()
    local headConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set, NetUpdatePlayerData:GetPlayerInfo().head)
    SetUIImage(self.ui.m_imgHeadMe, headConfig["icon"], false)
end

function UI_SlgChooseBattleView:GetInBattleList()
    local serverData = {}
    if self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_1 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_2
            or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_3 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_4 then
        serverData = HeroManager:GetTradeCarBattleListByType(self.team_type)
    elseif TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
        local typeList = trainDefendList[self.trainIndex]
        if self.team_type == typeList[1] then
            return self.curTrainDefendList_1
        elseif self.team_type == typeList[2] then
            return self.curTrainDefendList_2
        elseif self.team_type == typeList[3] then
            return self.curTrainDefendList_3
        end
    elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
        if self.team_type == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1 then
            return self.curTrainRobList_1
        elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2 then
            return self.curTrainRobList_2
        elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3 then
            return self.curTrainRobList_3
        end
    elseif TopFightManager:GetIsTopFightBattleType(self.team_type) then
        if self.team_type == BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM1 then
            return self.curTopFightList_1
        elseif self.team_type == BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM2 then
            return self.curTopFightList_2
        elseif self.team_type == BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM3 then
            return self.curTopFightList_3
        end
    else
        serverData = HeroManager:GetBattleTeamByType(self.team_type)
    end
    for i, v in ipairs(serverData) do
        if not IsTableEmpty(v) then
            tempBattleList[v.pos] = DeepCopy(v)
        end
    end
    return tempBattleList
end

-- 初始化火车防守方/掠夺队伍
function UI_SlgChooseBattleView:InitTrainTeamList(initType)
    local teams = {}
    for i = 1, 5 do
        teams[i] = {}
    end
    local serverData = {}
    serverData = HeroManager:GetBattleTeamByType(initType)
    for i, v in ipairs(serverData) do
        if not IsTableEmpty(v) then
            teams[v.pos] = DeepCopy(v)
        end
    end
    return teams
end

function UI_SlgChooseBattleView:InitBattleList()

	--创建dragItem
	for i = 1, 5 do
		local dragItem = DragItem.new()
		dragItem:Init(i,self,self.ui["m_btnAddWarPosA"..i])
		table.insert(self.dragItems,dragItem)
	end
    for i, heroVo in ipairs(self.curBattleList) do
        if not IsTableEmpty(heroVo) then
            local heroModule = HeroManager:GetHeroVoById(heroVo.code)
            self:CreateTeam(heroVo.pos,heroVo.code,heroModule)
        end
    end
    self.battleScene:ShowAllBlood(false)
    --for i = 1, 5 do
    --    local heroVo = self.curBattleList[i]
    --    
    --    if heroVo and heroVo.pos == i then
    --        
    --    else
    --        BattleSceneManager:RemoveTeam(i)
    --    end
    --end
end

function UI_SlgChooseBattleView:InitEnemyBattleList()
    local enemyHead 
    if self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
        local curID = self.battleParam.id
        local dungeonConfig = DungeonManager:GetDungeonConfigByID(curID)
        local enemyList = DungeonManager:GetDungeonMonsterListByID(dungeonConfig.monster_group)
		local _fight = 0
        local countList = HeroManager:ComputeBattleListCountByKind(enemyList)
        local _,_,activeValue = HeroManager:FindActiveBuffMaxNum(countList)
        for i, enemyVo in ipairs(enemyList) do
            local pos = enemyVo.pos + 10
            local monsterVo = HeroModule.new(enemyVo.monsterId)
            monsterVo:initData()
            monsterVo:SetHeroValueByKey("isMonster", true)
            monsterVo:SetHeroValueByKey("level", enemyVo.monsterLevel)
            monsterVo:SetHeroValueByKey("starLv", enemyVo.monsterStar)
            monsterVo:SetHeroValueByKey("atk", enemyVo.monsterAtk)
            monsterVo:SetHeroValueByKey("hp", enemyVo.monsterHp)
            monsterVo:SetHeroValueByKey("def", enemyVo.monsterDef)
            monsterVo:SetHeroValueByKey("power", enemyVo.monsterFight)
            self:CreateTeam(pos,enemyVo.monsterId,monsterVo)
            local multNum = 1 + (activeValue/10000)
            local enemyPower = math.ceil(multNum * (enemyVo.monsterFight or 0))
            _fight = _fight + enemyPower
        end
        enemyHead = dungeonConfig.battle_icon
        self.ui.m_txtPowerOther.text = NumToGameString(_fight)
    elseif self.team_type == BATTLE_TEAM_TYPE.TOWER or self.team_type == BATTLE_TEAM_TYPE.TOWER_TANK 
            or self.team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE or self.team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE then
        local towerId = self.battleParam.tower_id
        local config = TowerManager:GetTowerConfigById(towerId)
		local _fight = 0
        if config then
            local enemyList = TowerManager:GetMonsterListById(v2n(config.monster_group))
            local countList = HeroManager:ComputeBattleListCountByKind(enemyList)
            local _,_,activeValue = HeroManager:FindActiveBuffMaxNum(countList)
            for i, enemyVo in ipairs(enemyList) do
                local pos = enemyVo.pos + 10
                local monsterVo = HeroModule.new(enemyVo.monsterId)
                monsterVo:initData()
                monsterVo:SetHeroValueByKey("isMonster", true)
                monsterVo:SetHeroValueByKey("level", enemyVo.monsterLevel)
                monsterVo:SetHeroValueByKey("starLv", enemyVo.monsterStar)
                monsterVo:SetHeroValueByKey("atk", enemyVo.monsterAtk)
                monsterVo:SetHeroValueByKey("hp", enemyVo.monsterHp)
                monsterVo:SetHeroValueByKey("def", enemyVo.monsterDef)
                monsterVo:SetHeroValueByKey("power", enemyVo.monsterFight)
                self:CreateTeam(pos,enemyVo.monsterId,monsterVo)
                local multNum = 1 + (activeValue/10000)
                local enemyPower = math.ceil(multNum * (enemyVo.monsterFight or 0))
                _fight = _fight + enemyPower
            end
        end
        enemyHead = config.battle_icon
        self.ui.m_txtPowerOther.text = NumToGameString(_fight)
    elseif self.team_type == BATTLE_TEAM_TYPE.UNION_BOSS then
        local configId = self.battleParam.configId
        local monsterConfigInfo,pos = LeagueManager:GetMonsterConfigInfoAndPos(configId)
        local curPos = pos + 10
        local monsterVo = HeroModule.new(monsterConfigInfo.id)
        monsterVo:initData()
        monsterVo:SetHeroValueByKey("isMonster", true)
        monsterVo:SetHeroValueByKey("level", monsterConfigInfo.level)
        monsterVo:SetHeroValueByKey("starLv", monsterConfigInfo.star)
        monsterVo:SetHeroValueByKey("atk", monsterConfigInfo.atk)
        monsterVo:SetHeroValueByKey("hp", monsterConfigInfo.hp)
        monsterVo:SetHeroValueByKey("def", monsterConfigInfo.def)
        monsterVo:SetHeroValueByKey("power", monsterConfigInfo.fight)
        self:CreateTeam(curPos,monsterConfigInfo.id,monsterVo)
        self.ui.m_txtPowerOther.text = NumToGameString(monsterConfigInfo.fight)
    elseif self.team_type == BATTLE_TEAM_TYPE.ARENA_SELF then
        self:FreshArenaEnemyListInfo()
    elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_LOOT then
        self:RefreshPlunderWagonInfo()
    elseif self.team_type == BATTLE_TEAM_TYPE.WORLD_BOSS then
        local monsterConfigInfo,pos = self.worldBossData,4
        local curPos = pos + 10
        local monsterVo = HeroModule.new(monsterConfigInfo.id)
        monsterVo:initData()
        monsterVo:SetHeroValueByKey("isMonster", true)
        monsterVo:SetHeroValueByKey("level", monsterConfigInfo.level)
        monsterVo:SetHeroValueByKey("starLv", monsterConfigInfo.star)
        monsterVo:SetHeroValueByKey("atk", monsterConfigInfo.atk)
        monsterVo:SetHeroValueByKey("hp", monsterConfigInfo.hp)
        monsterVo:SetHeroValueByKey("def", monsterConfigInfo.def)
        monsterVo:SetHeroValueByKey("power", monsterConfigInfo.fight)
        self:CreateTeam(curPos,monsterConfigInfo.id,monsterVo)
        self.ui.m_txtPowerOther.text = NumToGameString(monsterConfigInfo.fight)
    elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
        self:FreshTrainRobEnemyHeadInfo()
        self:FreshTrainRobEnemyTeamsInfo()
    end
    self.battleScene:ShowAllBlood(false)
    --local cfgHeadSet = ConfigMgr:GetDataByID(ConfigDefine.ID.head_set,enemyHead)
    --if cfgHeadSet then
    --    SetUIImage(self.ui.m_imgHeadOther, cfgHeadSet["icon"], false)
    --end
end

function UI_SlgChooseBattleView:FreshTrainRobEnemyHeadInfo()
    if self.captain == nil then
        return
    end
    self.CustomHead_Enemy:SetHeadByID(self.captain.icon)
    self.CustomHead_Enemy:SetHeadBorderByID(self.captain.border)
end

function UI_SlgChooseBattleView:FreshTrainRobEnemyTeamsInfo()
    if self.beRobbedTeams == nil then
        return
    end
    local totalPower = 0
    local curTeam = self.beRobbedTeams[self.selectTeamBtnIndex]
    if not IsTableEmpty(curTeam) then
        local enemyList = curTeam.heroes
        local countList = HeroManager:ComputeBattleListCountByKind(enemyList)
        local findActiveIndex,_,activeValue = HeroManager:FindActiveBuffMaxNum(countList)
        for i, enemyVo in ipairs(enemyList) do
            local pos = enemyVo.pos + 10
            local multNum = 1 + (activeValue/10000)
            local enemyPower = math.ceil(multNum * (enemyVo.power or 0))
            totalPower = totalPower + enemyPower
            local _enemyVo = HeroModule.new(enemyVo.code)
            _enemyVo:initData()
            _enemyVo:SetHeroValueByKey("level", (enemyVo.level or 0))
            _enemyVo:SetHeroValueByKey("starLv", (enemyVo.star or 0))
            _enemyVo:SetHeroValueByKey("power", enemyVo.power)
            self:CreateTeam(pos,enemyVo.code,_enemyVo)
        end

        for i = 1, 3 do
            SetActive(self.ui["m_imgEnemyBuffIcon_"..i],findActiveIndex >= i)
        end
        local allActiveBuffEff = GetChild(self.ui.m_btnBuff2,"battle_zhenyingbuff_4")
        SetActive(allActiveBuffEff,findActiveIndex >= 3)
        self.battleScene:ShowAllBlood(false)
    end
    self.ui.m_txtPowerOther.text = NumToGameString(totalPower)
end

function UI_SlgChooseBattleView:FreshEnemyPlayerInfo(role,teams)
    SetActive(self.ui.m_goHeadEnemyPos,true)
    self.CustomHead_Enemy:SetHeadByID(role.icon)
    self.CustomHead_Enemy:SetHeadBorderByID(role.border)

    local totalPower = 0
    if not IsTableEmpty(teams) then
        local enemyList = teams[1].heroes
        local countList = HeroManager:ComputeBattleListCountByKind(enemyList)
        local findActiveIndex,_,activeValue = HeroManager:FindActiveBuffMaxNum(countList)
        for i, enemyVo in ipairs(enemyList) do
            local pos = enemyVo.pos + 10
            local multNum = 1 + (activeValue/10000)
            local enemyPower = math.ceil(multNum * (enemyVo.power or 0))
            totalPower = totalPower + enemyPower
            local _enemyVo = HeroModule.new(enemyVo.code)
            _enemyVo:initData()
            _enemyVo:SetHeroValueByKey("level", (enemyVo.level or 0))
            _enemyVo:SetHeroValueByKey("starLv", (enemyVo.star or 0))
            _enemyVo:SetHeroValueByKey("power", enemyVo.power)
            self:CreateTeam(pos,enemyVo.code,_enemyVo)
        end

        for i = 1, 3 do
            SetActive(self.ui["m_imgEnemyBuffIcon_"..i],findActiveIndex >= i)
        end
        local allActiveBuffEff = GetChild(self.ui.m_btnBuff2,"battle_zhenyingbuff_4")
        SetActive(allActiveBuffEff,findActiveIndex >= 3)
        self.battleScene:ShowAllBlood(false)
    end
    self.ui.m_txtPowerOther.text = NumToGameString(totalPower)
end

function UI_SlgChooseBattleView:FreshArenaEnemyListInfo()
    if self.isRobot then
        local enemyList = TowerManager:GetMonsterListById(self.enemyPlayerId)
        local _fight = HeroManager:GetMonsterTotalPower(enemyList)
        for i, enemyVo in ipairs(enemyList) do
            local pos = enemyVo.pos + 10
            local monsterVo = HeroModule.new(enemyVo.monsterId)
            monsterVo:initData()
            monsterVo:SetHeroValueByKey("isMonster", true)
            monsterVo:SetHeroValueByKey("level", enemyVo.monsterLevel)
            monsterVo:SetHeroValueByKey("starLv", enemyVo.monsterStar)
            monsterVo:SetHeroValueByKey("atk", enemyVo.monsterAtk)
            monsterVo:SetHeroValueByKey("hp", enemyVo.monsterHp)
            monsterVo:SetHeroValueByKey("def", enemyVo.monsterDef)
            monsterVo:SetHeroValueByKey("power", enemyVo.monsterFight)
            self:CreateTeam(pos,enemyVo.monsterId,monsterVo)
        end
        self.battleScene:ShowAllBlood(false)
        self.ui.m_txtPowerOther.text = NumToGameString(_fight)
    else
        local function RequestPlayerInfoCallBack(isSuccess,resPonseData)
            if isSuccess then
                local player = resPonseData.role
                local teams = resPonseData.teams
                self.enemyPlayerList = teams[1].heroes
                self:FreshEnemyPlayerInfo(player,teams)
            end
        end
        if self.enemyPlayerId then
            self:RequestPlayerInfoByType(self.enemyPlayerId,{BATTLE_TEAM_TYPE.ARENA_DEFENDER},RequestPlayerInfoCallBack)
        end
    end
    --self.ui.m_txtPowerOther.text = self.enemyPlayerPower
end

function UI_SlgChooseBattleView:RefreshPlunderWagonInfo()
    local function RequestWagonInfo(isSuccess,resPonseData)
        if isSuccess then
            local player = resPonseData.role
            local teams = resPonseData.teams
            self.enemyPlayerList = teams[1].heroes
            self:FreshEnemyPlayerInfo(player,teams)
        end
    end
    if self.plunderPlayerIdId and self.plunderIndex then
        local battleType = TradeWagonsManager:GetBattleTypeByIndex(self.plunderIndex)
        self:RequestPlayerInfoByType(self.plunderPlayerIdId,{battleType},RequestWagonInfo)
    end
end

function UI_SlgChooseBattleView:RequestPlayerInfoByType(playerId,teamTypeList,callBack)
    fight:OnReqTeamQuery(playerId, teamTypeList,callBack)
end

function UI_SlgChooseBattleView:GetBattleBGM()
	if self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
        local curID = self.battleParam.id
        local dungeonConfig = DungeonManager:GetDungeonConfigByID(curID)
        if dungeonConfig then
			return dungeonConfig["battle_bgm"] or 0
		end
    elseif self.team_type == BATTLE_TEAM_TYPE.TOWER or self.team_type == BATTLE_TEAM_TYPE.TOWER_TANK 
            or self.team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE or self.team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE then
        local towerId = self.battleParam.tower_id
        local config = TowerManager:GetTowerConfigById(towerId)
        if config then
            if config then
				return config["battle_bgm"] or 0
			end
        end
    end
	return nil
end

function UI_SlgChooseBattleView:RefreshEnemyPower()

end

function UI_SlgChooseBattleView:InitTradeTrainRoot()
    local isTradeTrain = TradeWagonsManager:GetIsTradeTrainType(self.team_type)
    local isTopFight = TopFightManager:GetIsTopFightBattleType(self.team_type)
    SetActive(self.ui.m_goTruckEditRoot,isTradeTrain or isTopFight)
    
    self:RefreshBtnTradeTrain()
end

function UI_SlgChooseBattleView:RefreshBtnTradeTrain()
    for i = 1, 3 do
        local go = self.ui["m_btnTruckEdit_"..i]
        local txtTruckEdit = GetChild(go,"txtTruckEdit",UEUI.Text)
        local txtTruckEditHight = GetChild(go,"imgTruckEditHight/txtTruckEditHight",UEUI.Text)
        local imgTruckEditHight = GetChild(go,"imgTruckEditHight",UEUI.Image)
        txtTruckEdit.text = i
        txtTruckEditHight.text = i
        SetActive(imgTruckEditHight,self.selectTeamBtnIndex == i)
    end
end

function UI_SlgChooseBattleView:InitToggleList()
    local indexDef = 0
    local kindList = HeroManager:GetHeroKindList()
    local _selectTogIndex
    if self.battleParam and self.battleParam.tower_type then
        if self.battleParam.tower_type == TOWER_TYPE.All then
            _selectTogIndex = HERO_KIND.ALL
        elseif self.battleParam.tower_type == TOWER_TYPE.Missile then
            _selectTogIndex = HERO_KIND.MISSILE
        elseif self.battleParam.tower_type == TOWER_TYPE.Tank then
            _selectTogIndex = HERO_KIND.TANK
        elseif self.battleParam.tower_type == TOWER_TYPE.Plane then
            _selectTogIndex = HERO_KIND.PLAEN
        end
    else
        _selectTogIndex = HERO_KIND.ALL
    end
    for index, togleId in ipairs(kindList) do
        local go = UEGO.Instantiate(self.toggle_prefab)
        go.transform:SetParent(self.toggle_perant)
        go.transform.localScale = Vector3.New(1, 1, 1)
        go.transform:SetLocalPosition(0, 0, 0)
        SetActive(go, true)
        go.name = "m_tog" .. togleId
        local img1 = GetChild(go,"Background/imgTog",UEUI.Image)
        local img2 = GetChild(go,"Background/Checkmark/imgTog2",UEUI.Image)
		local img3 = GetChild(go,"Background/notOpen/imgTog3",UEUI.Image)
        local imgNotOpen = GetChild(go,"Background/notOpen",UEUI.Image)
        local txtTogName = GetChild(go,"Background/txtTogName",UEUI.Text)
        local txtTogName2 = GetChild(go,"Background/Checkmark/txtTogName2",UEUI.Text)
        SetActive(txtTogName,togleId == HERO_KIND.ALL)
        SetActive(txtTogName2,togleId == HERO_KIND.ALL)
        SetActive(img1,togleId ~= HERO_KIND.ALL)
        SetActive(img2,togleId ~= HERO_KIND.ALL)
		SetActive(img3,togleId ~= HERO_KIND.ALL)
        local name = ""
        if togleId == HERO_KIND.ALL then
            name = "ALL"
            txtTogName.text = name
            txtTogName2.text = name
        end
        if togleId ~= HERO_KIND.ALL then
            local imgPath = ""
            local normalImgPath = ""
            if togleId == HERO_KIND.MISSILE then        --导弹
                imgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_che1.png"
                normalImgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_che.png"
            elseif togleId == HERO_KIND.PLAEN then      --飞机
                imgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_feiji1.png"
                normalImgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_feiji.png"
            elseif togleId == HERO_KIND.TANK then      --坦克
                imgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_tanke1.png" 
                normalImgPath = "Sprite/ui_slg_jueseyangcheng/yangcheng_liebiao_tanke.png"
            end
            if TradeWagonsManager:GetIsTradeTrainType(self.team_type) then
                normalImgPath = imgPath
            end
            SetImageSprite(img1,normalImgPath,true)
            SetImageSprite(img2,imgPath,true)
			SetImageSprite(img3,normalImgPath,true)
        end
        
        local isOpen = false
        if self.battleParam and self.battleParam.tower_type then
            local showTowerType
            if self.battleParam.tower_type == TOWER_TYPE.All then
                isOpen = true
            elseif togleId == HERO_KIND.ALL then
                showTowerType = TOWER_TYPE.All
            elseif  togleId == HERO_KIND.TANK then
                showTowerType = TOWER_TYPE.Tank
            elseif  togleId == HERO_KIND.MISSILE then
                showTowerType = TOWER_TYPE.Missile
            elseif  togleId == HERO_KIND.PLAEN then
                showTowerType = TOWER_TYPE.Plane
            end
            if showTowerType then
                isOpen = showTowerType == self.battleParam.tower_type
            end
            SetActive(imgNotOpen,not isOpen)
			SetActive(img1.gameObject,isOpen)
        else
			isOpen = true
            SetActive(imgNotOpen,false)
        end
        
        RemoveUIComponentEventCallback(go, UEUI.Toggle)
        AddUIComponentEventCallback(go, UEUI.Toggle, function(arg1, arg2)
            self:onUIEventClick(arg1 , arg2)
        end)
        
        local toggle = go:GetComponent(typeof(UEUI.Toggle))
        self.toggleTab[togleId] =  {
			["toggle"] = toggle,
			["isOpen"] = isOpen,
			["go"] = go,
		}
        if togleId == _selectTogIndex then
            toggle.isOn = true
        else
            toggle.isOn = false
        end
        if indexDef == 0 then
            indexDef = indexDef + 1
        end
    end

    UIRefreshLayout(self.toggle_perant)
end

function UI_SlgChooseBattleView:GetIsInTempBattleList(heroId)
    for i, v in ipairs(self.curBattleList) do
        if not IsTableEmpty(v) then
            if v.code == heroId then
                return true
            end
        end
    end
    return false
end

function UI_SlgChooseBattleView:RefreshHeroList()
    local dataList = self:GetActiveHeroList()
    --if dataList then
    --    self.heroSlideRect:SetData(dataList)
    --end
    local heroMax = HeroManager:ComputeHeroCountByKind(self.selectTogIndex)
    local IsEmpty = IsTableEmpty(dataList)
    local isShowEdit = self:IsShowEditRoot()
    local isTruckType = HeroManager:GetBattleTeamByTypeIsTruckType(self.team_type)
    for i = 1, heroMax do
        local heroData = dataList[i]
        if self.heroItemList[i] == nil then
            local parentGo = self.ui.m_transHeroList
            if isShowEdit then
                parentGo = self.ui.m_transEditHeroList
            end
            local heroGo = GoSlgHeroItem:Create(parentGo,heroData)
            heroGo:SetIsNeedShowSelect(true)
            heroGo:SetTeamType(self.team_type)
            heroGo:SetIsNeedShowInTruck(isTruckType)
            heroGo:SetItem()
            self.heroItemList[i] = heroGo
        else
            self.heroItemList[i]:ChangHero(heroData)
        end
        --if heroData and (self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_1 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_2 
        --        or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_3 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_4) then
        --
        --end
        if heroData and heroData.heroId then
            local isInList = self:GetIsInTempBattleList(heroData.heroId)
            self.heroItemList[i]:SetSelected(isInList)
            if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) or TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
                local teamIndex = 0
                if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
                    teamIndex = self:GetIsInDefendTeam(heroData.heroId)
                elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
                    teamIndex = self:GetIsInRobTeam(heroData.heroId)
                end
                if teamIndex ~= 0 then
                    self.heroItemList[i]:SetTeamInfo(teamIndex)
                else
                    self.heroItemList[i]:SetTeamInfo()
                end
                if teamIndex ~= 0 and self.selectTeamBtnIndex ~= teamIndex then
                    self.heroItemList[i]:SetMaskShow(true)
                else
                    self.heroItemList[i]:SetMaskShow(false)
                end
            elseif TopFightManager:GetIsTopFightBattleType(self.team_type) then
                local teamIndex = 0
                teamIndex = self:GetIsInTopFightTeam(heroData.heroId)
                if teamIndex ~= 0 then
                    self.heroItemList[i]:SetTeamInfo(teamIndex)
                else
                    self.heroItemList[i]:SetTeamInfo()
                end
                if teamIndex ~= 0 and self.selectTeamBtnIndex ~= teamIndex then
                    self.heroItemList[i]:SetMaskShow(true)
                else
                    self.heroItemList[i]:SetMaskShow(false)
                end
            end
        else
            self.heroItemList[i]:SetMaskShow(false)
        end
    end

    for i, v in ipairs(self.heroItemList) do
        v:SetVisible(i <= heroMax)
    end
    --if isShowEdit then
        --self.editHeroRect.verticalNormalizedPosition = 0
    --else
        --self.heroRect.verticalNormalizedPosition = 0
    --end
	TimeMgr:CreateTimer(self, function() self.heroRect.verticalNormalizedPosition = 1 end, 0.1, 1)
	TimeMgr:CreateTimer(self, function() self.editHeroRect.verticalNormalizedPosition = 1 end, 0.1, 1)	
end

function UI_SlgChooseBattleView:GetActiveHeroList()
    local heroList = HeroManager:GetHeroVoList(self.selectTogIndex,true)
    table.sort(heroList, function(a, b)
        if a.power == b.power then
            if a:GetHeroQuality() == b:GetHeroQuality() then
                return a.heroId > b.heroId
            end
            return a:GetHeroQuality() > b:GetHeroQuality()
        end
        return a.power > b.power
    end)
    return heroList
end

function UI_SlgChooseBattleView:RefreshHeroPower()
    local totalPower = HeroManager:GetBattleListTotalPower(self.team_type,self.curBattleList)
    self.ui.m_txtPowerMe.text = NumToGameString(totalPower)
    self.ui.m_txtEditPower.text = NumToGameString(totalPower)
end

function UI_SlgChooseBattleView:RefreshMainBuffIcon()
    local curBattleList = self.curBattleList
    local isShowEdit = self:IsShowEditRoot()
    local countList = HeroManager:ComputeBattleListCountByKind(curBattleList)
    local findActiveIndex = HeroManager:FindActiveBuffMaxNum(countList)
    for i = 1, 3 do
        local goStr = string.format("m_imgBuffIcon_%s",i)
        if isShowEdit then
            goStr = string.format("m_imgBuffEditIcon_%s",i)
        end
        SetActive(self.ui[goStr],findActiveIndex >= i)
    end
    local effGo = self.ui.m_btnBuff1
    
    if isShowEdit then
        effGo = self.ui.m_btnBuffEdit
    end
    local allActiveBuffEff = GetChild(effGo,"battle_zhenyingbuff_4")
    SetActive(allActiveBuffEff,findActiveIndex >= 3)
end

function UI_SlgChooseBattleView:GetEnemyBattleList()
    local enemyList = {}
    local battleList = {}
    local isMonster = true
    if self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
        local curID = self.battleParam.id
        local dungeonConfig = DungeonManager:GetDungeonConfigByID(curID)
        enemyList = DungeonManager:GetDungeonMonsterListByID(dungeonConfig.monster_group)
    elseif self.team_type == BATTLE_TEAM_TYPE.TOWER or self.team_type == BATTLE_TEAM_TYPE.TOWER_TANK
            or self.team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE or self.team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE then
        local towerId = self.battleParam.tower_id
        local config = TowerManager:GetTowerConfigById(towerId)
        if config then
            enemyList = TowerManager:GetMonsterListById(v2n(config.monster_group))
        end
    elseif self.team_type == BATTLE_TEAM_TYPE.ARENA_SELF then
        if self.isRobot then
            enemyList = TowerManager:GetMonsterListById(self.enemyPlayerId)
        else
            isMonster = false
            enemyList = self.enemyPlayerList or {}
        end
    elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_LOOT then
        isMonster = false
        enemyList = self.enemyPlayerList or {}
    end
    for i, enemyVo in ipairs(enemyList) do
        local temp = {}
        if isMonster then
            local pos = i + 10
            temp.code = enemyVo.monsterId
            temp.pos = pos
        else
            temp.code = enemyVo.code
            temp.pos = enemyVo.pos
        end

        table.insert(battleList,temp)
    end    
    return battleList
end

function UI_SlgChooseBattleView:RefreshEnemyMainBuffIcon()
    local battleList = self:GetEnemyBattleList()

    local curBattleList = battleList
    local countList = HeroManager:ComputeBattleListCountByKind(curBattleList)
    local findActiveIndex = HeroManager:FindActiveBuffMaxNum(countList)
    for i = 1, 3 do
        SetActive(self.ui["m_imgEnemyBuffIcon_"..i],findActiveIndex >= i)
    end
    local allActiveBuffEff = GetChild(self.ui.m_btnBuff2,"battle_zhenyingbuff_4")
    SetActive(allActiveBuffEff,findActiveIndex >= 3)
end

function UI_SlgChooseBattleView:RefreshBuffTips(battleList,isRight,targetGo)

	if targetGo then
		local rt = GetChild(self.ui.m_goBuffTips,"buffRoot")
		if isRight then
			SetUIPivot(rt,0.86,0.88)
		else
			SetUIPivot(rt,0.1,0.88)
		end
		local pos = UIMgr:GetObjectScreenPos(targetGo)
		SetUIPos(rt,pos.x,pos.y)
	end

    local curBattleList = battleList and battleList or self.curBattleList
    local fightConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_fight_setting,1)
    local tankConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting, 4005)
    local missileConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting, 4006)
    local planeConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting, 4007)
    
    local hurtValue = (1 - v2n(fightConfig.value)) * 100
    local hurtStr = string.format("<color=#ff7e00>-%s</color>",hurtValue) 
    self.ui.m_txtTankDesc.text = LangMgr:GetLangFormat(tankConfig.lang_id,hurtStr) 
    self.ui.m_txtMissileDesc.text = LangMgr:GetLangFormat(missileConfig.lang_id,hurtStr) 
    self.ui.m_txtPlaneDesc.text = LangMgr:GetLangFormat(planeConfig.lang_id,hurtStr) 

    if isRight then
        self.ui.m_imgBuffBg.transform.localScale = Vector3.New(-1, 1, 1)
    else
        self.ui.m_imgBuffBg.transform.localScale = Vector3.New(1, 1, 1)
    end
    
    for i = 1, 5 do
        SetActive(self.ui["m_imgInBattleIcon_"..i],false) 
    end
    for i, v in ipairs(curBattleList) do
        if v.code then
            SetActive(self.ui["m_imgInBattleIcon_"..i],true)
            local heroId = v.code
            local heroConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero, heroId);
            local kindPath = heroKindIcon[heroConfig.services]
            SetImageSprite(self.ui["m_imgInBattleIcon_"..i],kindPath,false)
        end
    end

    local countList = HeroManager:ComputeBattleListCountByKind(curBattleList)
    local findActiveIndex = HeroManager:FindActiveBuffMaxNum(countList)
    
    for i = 1, 3 do
        local activeBuffGo  = self.ui["m_goActiveBuff_"..i]
        local bg            = GetChild(activeBuffGo,"bg",UEUI.Image)
        local imgIcon1      = GetChild(activeBuffGo,"imgIcon_1")
        local imgIcon2      = GetChild(activeBuffGo,"imgIcon_1/imgIcon_2")
        local imgIcon3      = GetChild(activeBuffGo,"imgIcon_1/imgIcon_3")
        local imgIcon4      = GetChild(activeBuffGo,"imgIcon_1/imgIcon_4")
        local line          = GetChild(activeBuffGo,"line",UEUI.Image)
        local txtBuffDesc1  = GetChild(activeBuffGo,"txtBuffDesc1",UEUI.Text)
        local txtBuffDesc2  = GetChild(activeBuffGo,"txtBuffDesc2",UEUI.Text)
        
        local configId = BuffConfigList[i]
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting,configId)
        local descConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_global_setting,4004)
        local buffConfigArr = string.split(config.value,"|")
        
        local isActive = i == findActiveIndex
        local bgPath = "Sprite/ui_slg_zhandou_pve/buff_dikuang1.png"
        local linePath = "Sprite/ui_slg_zhandou_pve/buff_dikuang1_1.png"
        local descColor = "333333"
        local color = "7e746c"
        if isActive then
            bgPath = "Sprite/ui_slg_zhandou_pve/buff_dikuang2.png"
            linePath = "Sprite/ui_slg_zhandou_pve/buff_dikuang2_1.png"
            descColor = "175924"
            color = "086f08"
        end
        
        SetUIImageGray(imgIcon1,not isActive)
        SetUIImageGray(imgIcon2,not isActive)
        SetUIImageGray(imgIcon3,not isActive)
        SetUIImageGray(imgIcon4,not isActive)
        SetImageSprite(bg,bgPath,false)
        SetImageSprite(line,linePath,false)
        txtBuffDesc1.text = LangMgr:GetLangFormat(descConfig.lang_id,buffConfigArr[1])
        local textStr = string.format("<color=#00a300>%.1f</color>",v2n(buffConfigArr[2])/100)
        txtBuffDesc2.text = LangMgr:GetLangFormat(config.lang_id,textStr) 

        UnifyOutline(txtBuffDesc1,descColor)
        txtBuffDesc2.color = Color.HexToRGB(color)
    end
end

function UI_SlgChooseBattleView:CheckIsInTempBattleList(heroVo)
    for i, v in ipairs(self.curBattleList) do
        if v and v2n(v.code) == v2n(heroVo.heroId) then
            return true, i
        end
    end
    return false,0
end

function UI_SlgChooseBattleView:SetHeroItemSelected(heroId,isSelected)
    for i, v in ipairs(self.heroItemList) do
        if v.heroVo and v.heroVo.heroId == heroId then
            v:SetSelected(isSelected)
            if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) or TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) 
                    or TopFightManager:GetIsTopFightBattleType(self.team_type) then
                local teamIndex = 0
                if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
                    teamIndex = self:GetIsInDefendTeam(v.heroVo.heroId)
                elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
                    teamIndex = self:GetIsInRobTeam(v.heroVo.heroId)
                elseif TopFightManager:GetIsTopFightBattleType(self.team_type) then
                    teamIndex = self:GetIsInTopFightTeam(v.heroVo.heroId)
                end
                if teamIndex ~= 0 then
                    self.heroItemList[i]:SetTeamInfo(teamIndex)
                else
                    self.heroItemList[i]:SetTeamInfo()
                end
                if teamIndex ~= 0 and self.selectTeamBtnIndex ~= teamIndex then
                    self.heroItemList[i]:SetMaskShow(true)
                else
                    self.heroItemList[i]:SetMaskShow(false)
                end
            end
            break
        end
    end
end

function UI_SlgChooseBattleView:InsertInTempBattleTeam(heroVo)
    local findIndex = 0
    for i, heroItem in ipairs(self.curBattleList) do
        if IsTableEmpty(heroItem) then
            findIndex = i
            break
        end
    end
    if findIndex ~= 0 then
        local teamHero = {}
        teamHero.code = heroVo.heroId
        teamHero.pos = findIndex
        self.curBattleList[findIndex] = teamHero
        self:CheckTempIsNeedAutoSaveByType()
        self:CreateTeam(findIndex,heroVo.heroId,heroVo)
        self:SetHeroItemSelected(heroVo.heroId,true)
        self:RefreshHeroPower()
    end
    return findIndex
end

function UI_SlgChooseBattleView:RemoveTempBattleTeam(index)
    local temp = self.curBattleList[index]
    if not IsTableEmpty(temp) then
        BattleSceneManager:RemoveTeam(index)
        self.curBattleList[index] = {}
        self:SetHeroItemSelected(temp.code,false)
        self:CheckTempIsNeedAutoSaveByType()
        self:RefreshHeroPower()
        self:RefreshMainBuffIcon()
    end
end

--临时列表是否自动同步到缓存
function UI_SlgChooseBattleView:CheckTempIsNeedAutoSaveByType()
    local isNeedSave = HeroManager:CheckIsNeedAutoSaveByType(self.team_type)
    if isNeedSave then
        self:SyncViewTempBattleList()
    end
end

-- 同步出战列表到缓存
function UI_SlgChooseBattleView:SyncViewTempBattleList(callBack)
    HeroManager:BattleListSync(self.team_type,DeepCopy(self.curBattleList))
    HeroManager:OnRequestTeamModify(self.team_type,callBack)
end

-- 同步火车所有出战列表到缓存
function UI_SlgChooseBattleView:SyncAllViewTrainDefendOrRobTempBattleList(callBack)
    local isInsertCallBack = false
    local modifyCallBack
    local isDefend = TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type)
    local isAttack = TradeWagonsManager:GetIsTradeTrainRobType(self.team_type)
    local isTopFight = TopFightManager:GetIsTopFightBattleType(self.team_type)
    local typeList
    if isDefend then
        typeList = trainDefendList[self.trainIndex]
    elseif isAttack then
        typeList = {BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1,BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2,BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3}
    else
        typeList = {BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM1,BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM2,BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM3}
    end

    local updataList = {}
    for i = 1, 3 do
        local teamType
        local teams
        if i == 1 then
            teamType = typeList[1]
            if isDefend then
                teams = self.curTrainDefendList_1
            elseif isAttack then
                teams = self.curTrainRobList_1
            else
                teams = self.curTopFightList_1
            end
        elseif i == 2 then
            teamType = typeList[2]
            if isDefend then
                teams = self.curTrainDefendList_2
            elseif isAttack then
                teams = self.curTrainRobList_2
            else
                teams = self.curTopFightList_2
            end
        elseif i == 3 then
            teamType = typeList[3]
            if isDefend then
                teams = self.curTrainDefendList_3
            elseif isAttack then
                teams = self.curTrainRobList_3
            else
                teams = self.curTopFightList_3
            end
        end

        if not isInsertCallBack then
            isInsertCallBack = true
            modifyCallBack = callBack
        end
        HeroManager:BattleListSync(teamType,DeepCopy(teams))
        local param = {}
        param.teamType = teamType
        param.heroes = teams
        table.insert(updataList,param)
    end
    self:UpdataTeamList(updataList,modifyCallBack)
end

function UI_SlgChooseBattleView:UpdataTeamList(editTeams,modifyCallBack)
    HeroManager:OnReqTeamMultiModify(editTeams,modifyCallBack)
end

function UI_SlgChooseBattleView:SelectHeroBattle(heroVo)
    --local curBattleList = self.curBattleList
    
    local inBattle,index = self:CheckIsInTempBattleList(heroVo)
    if not inBattle then
        self:CheckIsEdit()
        self:CheckIsInOtherTeam(self.team_type,heroVo)
    elseif inBattle then
        --HeroManager:RemoveInBattleList(index,team_type)
        self:CheckIsEdit()
        self:RemoveTempBattleTeam(index)
    end

    self.battleScene:ShowAllBlood(false)
end

function UI_SlgChooseBattleView:SwapPosInTempBattleList(indexA,indexB)
    self:CheckIsEdit()
    self.curBattleList[indexA] ,self.curBattleList[indexB] = self.curBattleList[indexB],self.curBattleList[indexA]
    self:CheckTempIsNeedAutoSaveByType()
end

function UI_SlgChooseBattleView:SwapHeroPos(indexA,indexB)
	local a = self:GetBattleListDataByPos(indexA)
	local b = self:GetBattleListDataByPos(indexB)

	--先消除
	if a ~= nil then
		BattleSceneManager:RemoveTeam(a.pos)
	end

	if b ~= nil then
		BattleSceneManager:RemoveTeam(b.pos)
	end
	--再创建
	if a ~= nil then
		a.pos = indexB
        local heroVo = HeroManager:GetHeroVoById(a.code)
		self:CreateTeam(a.pos,a.code,heroVo)
	end

	if b ~= nil then
		b.pos = indexA
        local heroVo = HeroManager:GetHeroVoById(b.code)
        self:CreateTeam(b.pos,b.code,heroVo)
	end
    self.battleScene:ShowAllBlood(false)
    --HeroManager:SwapPosInBattleList(self.team_type,indexA,indexB)
    self:SwapPosInTempBattleList(indexA,indexB)
end

function UI_SlgChooseBattleView:GetBattleListDataByPos(pos)
	if self.curBattleList then
		local list = self.curBattleList
		for index, value in ipairs(list) do
			if value and value.pos == pos then
				return value
			end
		end
	end
	return nil
end

function UI_SlgChooseBattleView:GetIsInDefendTeam(heroId)
    for i = 1, 3 do
        local curTeam = self["curTrainDefendList_"..i]
        for k, v in ipairs(curTeam) do
            if v.code == heroId then
                return i
            end
        end
    end
    return 0
end

function UI_SlgChooseBattleView:GetIsInTopFightTeam(heroId)
    for i = 1, 3 do
        local curTeam = self["curTopFightList_"..i]
        for k, v in ipairs(curTeam) do
            if v.code == heroId then
                return i
            end
        end
    end
    return 0
end

function UI_SlgChooseBattleView:GetIsInRobTeam(heroId)
    for i = 1, 3 do
        local curTeam = self["curTrainRobList_"..i]
        for k, v in ipairs(curTeam) do
            if v.code == heroId then
                return i
            end
        end
    end
    return 0
end

function UI_SlgChooseBattleView:RemoveInOtherTrainDefendTeam(teamIndex,heroId)
    local curTeam = self["curTrainDefendList_"..teamIndex]
    for k, v in ipairs(curTeam) do
        if v.code == heroId then
            curTeam[k] = {}
        end
    end
end

function UI_SlgChooseBattleView:RemoveInOtherTrainRobTeam(teamIndex,heroId)
    local curTeam = self["curTrainRobList_"..teamIndex]
    for k, v in ipairs(curTeam) do
        if v.code == heroId then
            curTeam[k] = {}
        end
    end
end

function UI_SlgChooseBattleView:RemoveInOtherTopFightTeam(teamIndex,heroId)
    local curTeam = self["curTopFightList_"..teamIndex]
    for k, v in ipairs(curTeam) do
        if v.code == heroId then
            curTeam[k] = {}
        end
    end
end

-- 检查是否在别的火车防御队伍
function UI_SlgChooseBattleView:CheckIsInOtherTeam(teamType,heroVo)
    local findIndex = 0
    for i, heroItem in ipairs(self.curBattleList) do
        if IsTableEmpty(heroItem) then
            findIndex = i
            break
        end
    end
    if findIndex == 0 then
        return
    end
    local isDefend = TradeWagonsManager:GetIsTradeTrainDefendType(teamType)
    local isAttack = TradeWagonsManager:GetIsTradeTrainRobType(teamType)
    local isTopFight = TopFightManager:GetIsTopFightBattleType(teamType)
    local isShowTips = false
    local heroId = heroVo.heroId
    local inTeamIndex = 0
    if isDefend then
        inTeamIndex = self:GetIsInDefendTeam(heroId)
        if inTeamIndex ~= 0 and self.selectTeamBtnIndex ~= inTeamIndex then
            isShowTips = true
        end
    elseif isAttack then
        inTeamIndex = self:GetIsInRobTeam(heroId)
        if inTeamIndex ~= 0 and self.selectTeamBtnIndex ~= inTeamIndex then
            isShowTips = true
        end
    elseif isTopFight then  -- 巅峰赛
        inTeamIndex = self:GetIsInTopFightTeam(heroId)
        if inTeamIndex ~= 0 and self.selectTeamBtnIndex ~= inTeamIndex then
            isShowTips = true
        end
    end
    
    local function checkCallBack()
        if isShowTips and inTeamIndex ~= 0 then
            if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
                self:RemoveInOtherTrainDefendTeam(inTeamIndex,heroId)
            elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
                self:RemoveInOtherTrainRobTeam(inTeamIndex,heroId)
            elseif TopFightManager:GetIsTopFightBattleType(self.team_type) then
                self:RemoveInOtherTopFightTeam(inTeamIndex,heroId)
            end
        end
        self:InsertInTempBattleTeam(heroVo)
        self:RefreshHeroPower()
        self:RefreshMainBuffIcon()
    end
    if not isShowTips then
        if checkCallBack then
            checkCallBack()
        end
    else
        local titleStr = LangMgr:GetLang(8121)
        local heroName = heroVo:GetHeroName()
        local contentStr = LangMgr:GetLangFormat(70000597,heroName)
        local params = {
            title = titleStr,
            content = contentStr,
            confirmCallBack = checkCallBack,
        }
        UI_SHOW(UIDefine.UI_TradeWagonsShareConfirm, 1, params)
    end
end

function UI_SlgChooseBattleView:OnRefresh(param)
    
end

function UI_SlgChooseBattleView:onDestroy()
    EventMgr:Remove(EventID.SELECT_HERO_BATTLE, self.SelectHeroBattle, self)
	EventMgr:Dispatch(EventID.CLOSE_SlgChooseBattleView)
    for i, v in pairs(self.heroItemList) do
        v:Close()
    end
    self.heroItemList = {}
    self.isEdit = false
    self.battleParam = nil
	BattleSceneManager:PlayBGM()
end


function UI_SlgChooseBattleView:StartLevelBattle()
    local id = self.battleParam.id
    local team_type = self.team_type
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        if isReplay then
            local reportCache = TowerManager:GetBattleReportCacheById(report.id)
            if reportCache then
                if _isWin then
                    local param = {}
                    param.report = reportCache.report
                    param.rewards = reportCache.rewards
					param.teamType = team_type
					param.oldId = reportCache.oldId
                    UI_SHOW(UIDefine.UI_SlgVictoryView,param)
                else
                    local param = {}
                    param.report = reportCache.report
                    param.oldId = reportCache.oldId
                    param.teamType = team_type
                    UI_SHOW(UIDefine.UI_SlgDefeatView,param)
                end
            end
            return
        end
        DungeonManager:OnRequestDungeonSettle(id,_isWin,report)
    end
    local function EnterBattle(data)
        if data then
            local levelId = data.id
            local report_id = data.report_id
            local route_id = data.route_id
            
            fight:OnReqFightRead(report_id,route_id,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)
                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    DungeonManager:OnRequestDungeonFight(id,EnterBattle)
end

function UI_SlgChooseBattleView:StartTowerBattle(team_type)
    local tower_id = self.battleParam.tower_id
    local tower_type = self.battleParam.tower_type
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        if isReplay then
            local reportCache = TowerManager:GetBattleReportCacheById(report.id)
            if reportCache then
                if _isWin then
                    local param = {}
                    param.report = reportCache.report
                    param.rewards = reportCache.rewards
                    param.teamType = reportCache.teamType
                    param.oldId = reportCache.oldId
                    UI_SHOW(UIDefine.UI_SlgVictoryView,param)
                else
                    local param = {}
                    param.report = reportCache.report
                    param.teamType = reportCache.teamType
                    param.oldId = reportCache.oldId
                    UI_SHOW(UIDefine.UI_SlgDefeatView,param)
                end
            end
            return
        end
        TowerManager:RequestTowerSettle(tower_id,tower_type,_isWin,team_type,report)
    end
    local function EnterBattle(data)
        if data then
            local callBackId = data.id
            local callBackType = data.type
            local report_id = data.report_id
            local route_id = data.route_id

            fight:OnReqFightRead(report_id,route_id,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)
                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    TowerManager:RequestTowerFight(tower_id,tower_type,EnterBattle)
end

function UI_SlgChooseBattleView:StartUnionBossBattle(configId)
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        --if isReplay then
        --    return
        --end
        local reportCache = TowerManager:GetBattleReportCacheById(report.id)
        if reportCache then
            local param = {}
            param.report = reportCache.report
            param.rewards = reportCache.rewards
            param.last_hit_rewards = reportCache.last_hit_rewards
            param.teamType = reportCache.teamType
            param.oldId = reportCache.oldId
            param.isWin = _isWin
            UI_SHOW(UIDefine.UI_SlgVictoryView,param)
        end
    end
    
    local function EnterBattle(data)
        if data then
            local report_id = data.report_id
            local rewards = data.rewards
            local last_hit_rewards = data.last_hit_rewards
            local is_win = data.result == 1
            
            fight:OnReqFightRead(report_id,nil,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)

                    local reportCache = {}
                    reportCache.report = report
                    reportCache.rewards = rewards
                    reportCache.last_hit_rewards = last_hit_rewards
                    reportCache.isWin = is_win
                    reportCache.teamType = BATTLE_TEAM_TYPE.UNION_BOSS
                    reportCache.oldId = self.unionBossId
                    TowerManager:SetBattleReportCache(reportCache)

                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    LeagueManager:OnRequestBossFight(configId, EnterBattle)
end

-- 竞技场战斗
function UI_SlgChooseBattleView:StartArenaBattle()
    if self.enemyPlayerId == nil then
        return
    end
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        --if isReplay then
        --    return
        --end
        local reportCache = TowerManager:GetBattleReportCacheById(report.id)
        if reportCache then
   
            if _isWin then
                local param = {}
                param.report = reportCache.report
                param.rewards = reportCache.rewards
                param.arenaServer = reportCache.arenaServer
                param.up_lv_result = reportCache.up_lv_result
                param.teamType = reportCache.teamType
                UI_SHOW(UIDefine.UI_SlgVictoryView,param)
            else
                local param = {}
                param.report = reportCache.report
                param.rewards = reportCache.rewards
                param.arenaServer = reportCache.arenaServer
                param.up_lv_result = reportCache.up_lv_result
                param.teamType = reportCache.teamType
                UI_SHOW(UIDefine.UI_SlgDefeatView,param)
            end
        end
    end
    local function EnterBattle(data)
        if data then
            local report_id = data.report_id
            local rewards = data.rewards
            local is_win = data.result == 1
            local rank_id = data.arena.rank_id
            local rank_star = data.arena.rank_star
            local arena = data.arena
            local up_lv_result = data.up_lv_result

            fight:OnReqFightRead(report_id,nil,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)

                    local reportCache = {}
                    reportCache.report = report
                    reportCache.rewards = rewards
                    reportCache.arenaServer = arena
                    reportCache.up_lv_result = up_lv_result
                    reportCache.isWin = is_win
                    reportCache.teamType = BATTLE_TEAM_TYPE.ARENA_SELF
                    local temp = {}
                    temp.result = report.result
                    temp.rank_id = rank_id
                    temp.rank_star = rank_star
                    reportCache.arenaData = temp
                    TowerManager:SetBattleReportCache(reportCache)

                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    JJcManager:OnReqArenaFight(self.enemyPlayerId,EnterBattle)
end

-- 货车掠夺战斗
function UI_SlgChooseBattleView:StartPlunderWagonBattle()
    if self.plunderId == nil then
        return
    end
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        local reportCache = TowerManager:GetBattleReportCacheById(report.id)
        if reportCache then
            if _isWin then
                local param = {}
                param.report = reportCache.report
                param.rewards = reportCache.rewards
                param.retake_reward = reportCache.retake_reward
                param.teamType = reportCache.teamType
                param.oldId = reportCache.oldId
                UI_SHOW(UIDefine.UI_SlgVictoryView,param)
            else
                local param = {}
                param.report = reportCache.report
                param.teamType = reportCache.teamType
                param.oldId = reportCache.oldId
                UI_SHOW(UIDefine.UI_SlgDefeatView,param)
            end
            EventMgr:Dispatch(EventID.BATTLE_TRADEWAGON_RESULT, _isWin)
        end
    end
    local function EnterBattle(data)
        if data then
            local report_id = data.report_id
            local rewards = data.rewards
            local retake_reward = data.retake_reward
            local is_win = data.is_success

            fight:OnReqFightRead(report_id,nil,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)

                    local reportCache = {}
                    reportCache.report = report
                    reportCache.rewards = rewards
                    reportCache.retake_reward = retake_reward
                    reportCache.isWin = is_win
                    reportCache.teamType = BATTLE_TEAM_TYPE.TRADE_CAR_LOOT
                    TowerManager:SetBattleReportCache(reportCache)

                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    TradeWagonsManager:RequestWagonPlunder(self.plunderId,EnterBattle)
end

-- 世界boss战斗开始
function UI_SlgChooseBattleView:StartWorldBossBattle(configId)
    local function BattleCallBack(isWin,report,isReplay)
        local _isWin = isWin == 1
        --if isReplay then
        --    return
        --end
        local reportCache = TowerManager:GetBattleReportCacheById(report.id)
        if reportCache then
            local param = {}
            param.report = reportCache.report
            param.teamType = reportCache.teamType
            param.boss_id = reportCache.boss_id
            param.rank_before = reportCache.rank_before
            param.rank_after = reportCache.rank_after
            param.highest_damage = reportCache.highest_damage
            param.current_damage = reportCache.current_damage
            param.isWin = _isWin
            UI_SHOW(UIDefine.UI_SlgVictoryView,param)
        end
    end

    local function EnterBattle(data)
        if data then
            local report_id = data.report_id
            local boss_id = data.boss_id
            local rank_before = data.rank_before
            local rank_after = data.rank_after
            local highest_damage = data.highest_damage
            local current_damage = data.current_damage
            local is_win = data.result == 1

            fight:OnReqFightRead(report_id,nil,function (isSuccess, fightData)
                if isSuccess then
                    local report = ProtocManager:Decode("marge.topia.battle.Report", fightData.report)

                    local reportCache = {}
                    reportCache.report = report
                    reportCache.boss_id = boss_id
                    reportCache.rank_before = rank_before
                    reportCache.rank_after = rank_after
                    reportCache.highest_damage = highest_damage
                    reportCache.current_damage = current_damage
                    reportCache.isWin = is_win
                    reportCache.teamType = BATTLE_TEAM_TYPE.WORLD_BOSS
                    TowerManager:SetBattleReportCache(reportCache)

                    BattleSceneManager:SetBattleCallBack(BattleCallBack)
                    BattleSceneManager:SetBGM(self:GetBattleBGM())
                    BattleSceneManager:SetBossType(self:GetBossBattleType())
                    BattleSceneManager:Battle(report)
                end
            end)
        end
    end
    WorldBossManager:OnRequestBossFight(configId, EnterBattle)
end

function UI_SlgChooseBattleView:StartBattle()
    if self.battleParam == nil then
        return
    end
    --local battleType = self.battleParam.battle_type
    local team_type = self.team_type
    local battleList = HeroManager:GetBattleTeamByType(team_type)
    if IsTableEmpty(battleList) then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000237))
        return
    end
    if team_type == BATTLE_TEAM_TYPE.DUNGEON then
        self:StartLevelBattle()
    elseif team_type == BATTLE_TEAM_TYPE.TOWER or team_type == BATTLE_TEAM_TYPE.TOWER_TANK 
        or team_type == BATTLE_TEAM_TYPE.TOWER_AIRPLANE or team_type == BATTLE_TEAM_TYPE.TOWER_MISSILE
    then
        local isOpen = TowerManager:GetTodayIsOpenByKind(self.battleParam.tower_type)
        if not isOpen then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000206))
            return
        end
        self:StartTowerBattle(team_type)
    elseif team_type == BATTLE_TEAM_TYPE.UNION_BOSS then
        if self.unionBossId == nil then
            Log.Error("bossID为空")
            return
        end
        self:StartUnionBossBattle(self.unionBossId)
    elseif team_type == BATTLE_TEAM_TYPE.ARENA_SELF then
        if self.enemyPlayerId == nil then
            return
        end
        self:StartArenaBattle()
    elseif team_type == BATTLE_TEAM_TYPE.TRADE_CAR_LOOT then
        self:StartPlunderWagonBattle()
    elseif team_type == BATTLE_TEAM_TYPE.WORLD_BOSS then
        if WorldBossManager.boss_id == 0 then
            Log.Error("bossID为0")
            return
        end
        self:StartWorldBossBattle(WorldBossManager.boss_id)
    end
end

function UI_SlgChooseBattleView:OneKeyChooseHero()
    local heroList = HeroManager:GetHeroVoList(self.selectTogIndex,true)
    table.sort(heroList, function(a, b)
        if a.power == b.power then
            if a:GetHeroQuality() == b:GetHeroQuality() then
                return a.heroId > b.heroId
            end
            return a:GetHeroQuality() > b:GetHeroQuality()
        end
        return a.power > b.power
    end)
    
    local battleList = {}
    
    for i = 1, 5 do
        local hero = self.curBattleList[i]
        if not IsTableEmpty(hero) then
            self:SetHeroItemSelected(hero.code,false)
            BattleSceneManager:RemoveTeam(i)
        end
        self.curBattleList[i] = {}
    end
    local count = 0
    local sortCount = 0
    for i, heroVo in ipairs(heroList) do
        local isCanInsert = self:GetIsCanInsertBattleList(heroVo.heroId)
        if count < 5 then
            if isCanInsert then
                count = count + 1
                local sortIndex = i
                if heroVo:GetHeroCareer() == HERO_CAREER.DEF and sortCount < 2 then
                    sortIndex = 1
                    sortCount = sortCount + 1
                elseif heroVo:GetHeroCareer() == HERO_CAREER.ADC then
                    sortIndex = 3
                else
                    sortIndex = 2
                end
                table.insert(battleList, { hero = heroVo,sort = sortIndex })
            end
            --self:RefreshBattleHero(i,heroVo)
        else
            break
        end
    end

    if sortCount < 2 then
        for i, item in ipairs(battleList) do
            local heroVo = item.hero
            if heroVo:GetHeroCareer() == HERO_CAREER.SUP then
                item.sort = 1
                sortCount = sortCount + 1
                if sortCount >= 2 then
                    break
                end
            end
        end
    end
    
    table.sort(battleList, function(a, b)
        local a_hero = a.hero
        local b_hero = b.hero
        if a.sort == b.sort then
            if a_hero:GetHeroCareer() == b_hero:GetHeroCareer() then
                return a_hero.power > b_hero.power
            end
            return a_hero:GetHeroCareer() > b_hero:GetHeroCareer()
        end
        return a.sort < b.sort
    end)
    for i, heroItem in ipairs(battleList) do
        local teamHero = {}
        teamHero.code = heroItem.hero.heroId
        teamHero.pos = i
        self.curBattleList[i] = teamHero
        self:CreateTeam(i,heroItem.hero.heroId,heroItem.hero)
        self:SetHeroItemSelected(heroItem.hero.heroId,true)
    end

    self:CheckTempIsNeedAutoSaveByType()
    --self:RefreshHeroList()
	self:RefreshHeroPower()
    self:RefreshMainBuffIcon()
    self.battleScene:ShowAllBlood(false)
end

function UI_SlgChooseBattleView:CreateTeam(pos,heroId,heroVo)
    BattleSceneManager:CreateTeam(pos,heroId,heroVo)
    self.battleScene:ShowAllBlood(false)
end

function UI_SlgChooseBattleView:GetIsCanInsertBattleList(heroId)
    local isCanInsert = true
    local isTradeType = TradeWagonsManager:GetIsTradeCarType(self.team_type)
    local isInTrainDefend = TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type)
    local isInTrainRob = TradeWagonsManager:GetIsTradeTrainRobType(self.team_type)
    local isTopFight = TopFightManager:GetIsTopFightBattleType(self.team_type)
    if isTradeType then
        local isInTruck = TradeWagonsManager:IsHeroInWagon(heroId)
        if isInTruck then
            isCanInsert = false
        end
    elseif isInTrainRob then
        local teamIndex = self:GetIsInRobTeam(heroId)
        if teamIndex ~= 0 then
            isCanInsert = false
        end
    elseif isInTrainDefend then
        local teamIndex = self:GetIsInDefendTeam(heroId)
        if teamIndex ~= 0 then
            isCanInsert = false
        end
    elseif isTopFight then
        local teamIndex = self:GetIsInTopFightTeam(heroId)
        if teamIndex ~= 0 then
            isCanInsert = false
        end
    end
    return isCanInsert
end

function UI_SlgChooseBattleView:GetTempBattleListIsEmpty()
    local isEmpty = true
    if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
        local empty1 = self:CheckTempBattleListIsEmpty(self.curTrainDefendList_1)
        local empty2 = self:CheckTempBattleListIsEmpty(self.curTrainDefendList_2)
        local empty3 = self:CheckTempBattleListIsEmpty(self.curTrainDefendList_3)
        isEmpty = empty1 or empty2 or empty3
    elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
        local empty1 = self:CheckTempBattleListIsEmpty(self.curTrainRobList_1)
        local empty2 = self:CheckTempBattleListIsEmpty(self.curTrainRobList_2)
        local empty3 = self:CheckTempBattleListIsEmpty(self.curTrainRobList_3)
        isEmpty = empty1 or empty2 or empty3
    else
        isEmpty = self:CheckTempBattleListIsEmpty(self.curBattleList)
    end

    return isEmpty
end

function UI_SlgChooseBattleView:CheckTempBattleListIsEmpty(curBattleList)
    for i, v in ipairs(curBattleList) do
        if not IsTableEmpty(v) then
            return false
        end
    end
    return true
end

function UI_SlgChooseBattleView:ClearBattleList()
    for i = 1, 5 do
        local hero = self.curBattleList[i]
        if not IsTableEmpty(hero) then
            self:SetHeroItemSelected(hero.code,false)
            BattleSceneManager:RemoveTeam(i)
        end
    end
end

function UI_SlgChooseBattleView:ClearEnemyBattleList()
    for i = 11, 15 do
        BattleSceneManager:RemoveTeam(i)
    end
end

function UI_SlgChooseBattleView:RefreshBattleList()
    for i, heroVo in ipairs(self.curBattleList) do
        if not IsTableEmpty(heroVo) then
            local heroModule = HeroManager:GetHeroVoById(heroVo.code)
            self:CreateTeam(heroVo.pos,heroVo.code,heroModule)
            self:SetHeroItemSelected(heroVo.code,true)
        end
    end
    self.battleScene:ShowAllBlood(false)
end

function UI_SlgChooseBattleView:ChangeTradeTrainList(index)
    if self.selectTeamBtnIndex == index then
        return
    end
    local typeList = trainDefendList[self.trainIndex]
    self.team_type = typeList[index]
    self:ClearBattleList()
    self.selectTeamBtnIndex = index
    self.curBattleList = self:GetInBattleList()
    self:RefreshBtnTradeTrain()
    self:RefreshBattleList()
    self:RefreshMainBuffIcon()
    self:RefreshHeroList()
    self:RefreshHeroPower()
end

function UI_SlgChooseBattleView:ChangeTrainRobList(index)
    if self.selectTeamBtnIndex == index then
        return
    end
    if index == 1 then
        self.team_type = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_1
    elseif index == 2 then
        self.team_type = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_2
    elseif index == 3 then
        self.team_type = BATTLE_TEAM_TYPE.TRADE_TRAIN_ROB_3
    end
    
    self:ClearBattleList()
    self.selectTeamBtnIndex = index
    self.curBattleList = self:GetInBattleList()
    self:RefreshBtnTradeTrain()
    self:RefreshBattleList()
    self:RefreshMainBuffIcon()
    self:RefreshHeroList()
    self:ClearEnemyBattleList()
    self:FreshTrainRobEnemyTeamsInfo()
    self:RefreshHeroPower()
end

function UI_SlgChooseBattleView:ChangeTopFightList(index)
    if self.selectTeamBtnIndex == index then
        return
    end
    if index == 1 then
        self.team_type = BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM1
    elseif index == 2 then
        self.team_type = BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM2
    elseif index == 3 then
        self.team_type = BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM3
    end
    
    self:ClearBattleList()
    self.selectTeamBtnIndex = index
    self.curBattleList = self:GetInBattleList()
    self:RefreshBtnTradeTrain()
    self:RefreshBattleList()
    self:RefreshMainBuffIcon()
    self:RefreshHeroList()
    self:RefreshHeroPower()
end

function UI_SlgChooseBattleView:OnClickEditTeamPage(index)
    if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) then
        self:ChangeTradeTrainList(index)
    elseif TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) then
        self:ChangeTrainRobList(index)
    elseif TopFightManager:GetIsTopFightBattleType(self.team_type) then
        self:ChangeTopFightList(index)
    end
end

function UI_SlgChooseBattleView:OnClickSave()
    local isEmpty = self:GetTempBattleListIsEmpty()
    if isEmpty then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000237))
        return
    end
    if self:IsNeedManualSave() then
        local function syncCallBack()
            EventMgr:Dispatch(EventID.BATTLE_MANUAL_SAVE)
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000593))
        end
        if TradeWagonsManager:GetIsTradeTrainDefendType(self.team_type) or TradeWagonsManager:GetIsTradeTrainRobType(self.team_type) 
                or TopFightManager:GetIsTopFightBattleType(self.team_type) then
            self:SyncAllViewTrainDefendOrRobTempBattleList(syncCallBack)
        else
            self:SyncViewTempBattleList(syncCallBack)
        end
    elseif self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_1 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_2
            or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_3 or self.team_type == BATTLE_TEAM_TYPE.TRADE_CAR_4 then
        HeroManager:BattleListSync(self.team_type,DeepCopy(self.curBattleList))
        EventMgr:Dispatch(EventID.BATTLE_TRADEWAGON_SAVE)
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000593))
    end
    if self.isEdit then
        self.isEdit = false
    end
end

function UI_SlgChooseBattleView:CheckClose()
    local function funOK()
        self:OnClickSave()
        BattleSceneManager:CloseScene()
    end
    local function funClose()
        BattleSceneManager:CloseScene()
    end
    if TradeWagonsManager:GetIsTradeTrainType(self.team_type) or TopFightManager:GetIsTopFightBattleType(self.team_type) then
        if self.isEdit then
            local titleStr = LangMgr:GetLang(8121)
            local contentStr = LangMgr:GetLangFormat(70001068)
            local params = {
                title = titleStr,
                content = contentStr,
                confirmCallBack = funOK,
                closeCallBack = funClose,
            }
            UI_SHOW(UIDefine.UI_TradeWagonsShareConfirm, 3, params)
        else
            funClose()
        end
    else
        funClose()
    end
end

function UI_SlgChooseBattleView:onUIEventClick(go,param)
    local name = go.name
    local togFind = string.find(name, "m_tog")
    local warFindA = string.find(name, "m_btnWarPosA")
    local warFindB = string.find(name, "m_btnWarPosB")
    local addFindA = string.find(name, "m_btnAddWarPosA")
    local addFindB = string.find(name, "m_btnAddWarPosB")

    if name == "m_btnClose" then
        self:CheckClose()
    elseif name == "m_btnOnClickUp" or name == "m_btnEditOnClickUp" then
        local isOpen = DungeonManager:GetOneKeyChooseHeroIsopen(self.team_type)
        if not isOpen then
            local limitValue = DungeonManager:GetOneKeyChooseHeroConfig()
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70000018,limitValue))
            return    
        end
        --Log.Error("xxxxxx一键上阵")
        self:OneKeyChooseHero()
	elseif name == "m_btnCloseBuff" then
        SetActive(self.ui.m_goBuffTips,false)
	elseif name == "m_btnBuff2" then
        local battleList = self:GetEnemyBattleList()
        self:RefreshBuffTips(battleList,true,self.ui.m_btnBuff2)
        SetActive(self.ui.m_goBuffTips,true)
	elseif name == "m_btnBuff1" then
        self:RefreshBuffTips(nil,false,self.ui.m_btnBuff1)
        SetActive(self.ui.m_goBuffTips,true)
    elseif name == "m_btnBuffEdit" then
        self:RefreshBuffTips(self.curBattleList,false,self.ui.m_btnBuffEdit)
        SetActive(self.ui.m_goBuffTips,true)
    elseif name == "m_btnEditSave" then
        self:OnClickSave()
    elseif name == "m_btnBattle" then
		--local report = nil
		--self.battleScene:Battle(report)
        self:StartBattle()
    elseif name == "m_btnTruckEdit_1" then
        self:OnClickEditTeamPage(1)
    elseif name == "m_btnTruckEdit_2" then
        self:OnClickEditTeamPage(2)
    elseif name == "m_btnTruckEdit_3" then
        self:OnClickEditTeamPage(3)
    elseif togFind and togFind > 0 then
		if go and go.isOn == false then
			return
		end
        local index = tonumber(string.match(name, "m_tog(%d+)"))
        if index == self.selectTogIndex then
            return
        end
		
		local temp = self.toggleTab[index]
		if temp and not temp.isOpen then
			local old = self.toggleTab[self.selectTogIndex]
			if old and old.toggle then
				local kindNameId = HeroManager:GetHeroKindName(index)
				local kindName = LangMgr:GetLang(kindNameId)
				UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70000182,kindName))
				old.toggle.isOn = true		
			end
			return
		end
        if index == HERO_KIND.ALL then
            local _go = self.toggleTab[index].go
            local img2 = GetChild(_go,"Background/Checkmark/imgTog2",UEUI.Image)
            SetActive(img2,false)
        end
		self.selectTogIndex = index
		self:RefreshHeroList()
		
    elseif warFindA and warFindA > 0 then
        local index = tonumber(string.match(name, "m_btnWarPosA(%d+)"))
        --self:RefreshBattleHero(index,nil)
    end
end

function UI_SlgChooseBattleView:GetBossBattleType()
	if self.team_type == BATTLE_TEAM_TYPE.DUNGEON then
		if self.battleParam and self.battleParam.id then
			local dungeonId = self.battleParam.id
			local dungeonCfg = DungeonManager:GetDungeonConfigByID(dungeonId)
			if dungeonCfg then
				local type = dungeonCfg["dungeon_icon_type"]
				return type or DUNGEON_NODE_TYPE.Normal
			end
		end
	end
	return DUNGEON_NODE_TYPE.Normal
end

--region dragItem
function DragItem:Init(index,win,go)
	self.index = index
	self.win = win
	self.go = go
	self.battleScene = self.win.battleScene

	self.goRect = GetComponent(go,UE.RectTransform)
	self.dragItems = win.dragItems

	self.curDragHeroVo = nil
	self.curDragTeam = nil
	self.targetDragItem = nil

	self.isDragIn =false

	self.v2Temp = Vector2.New(0,0)
	local curBattleList = win.curBattleList
	
	local uiDrag = GetComponent(self.go,CS.UIDragXYDir)
	uiDrag.enabled = true
    uiDrag.onClick:RemoveAllListeners()
    uiDrag.onClick:AddListener(function ()
		Log.Info("click"..self.index)

		if self.curDragHeroVo then
			return
		end

		local curBattlePosData = self.win:GetBattleListDataByPos(index)
		if curBattlePosData then
			local heroId = curBattlePosData.code
			local heroVo = HeroManager:GetHeroVoById(heroId)
			win:SelectHeroBattle(heroVo)
			--for i, v in ipairs(win.heroItemList) do
			--	if v.heroVo and v.heroVo.heroId == heroId then
			--		v:ClickItem()
			--		break
			--	end
			--end
		end
    end)

	uiDrag.m_BeginDrag = function( eventData, go , x, y )
        Log.Info("m_BeginDrag"..self.index)
		local curBattlePosData = self.win:GetBattleListDataByPos(index)
		if curBattlePosData then
			self.curDragHeroVo = curBattlePosData
			self.curDragTeam = self:GetCurTeam()
		end
    end
	uiDrag.m_OnDrag = function( eventData, go , x, y )

		if not self.curDragTeam then
			return
		end

		local _,pos = self:SP2LP(x,y)
		self.curDragTeam:SetPosition(pos.x,pos.y,-500)

		local targetDragItem  = nil
		for i, dragItem in ipairs(self.dragItems) do
			if i ~= self.index then
				-- if not dragItem:IsEmpty() then
					local touchPos = Vector2.New(x,y)
					local isInRegion = UE.RectTransformUtility.RectangleContainsScreenPoint(dragItem.goRect,touchPos,UIMgr.uiCamera)
					if isInRegion then
						Log.Info("m_OnDrag:"..self.index.."==>"..dragItem.index)
						targetDragItem = dragItem
						break
					end
				-- end
			end
		end
		
		if targetDragItem then
			if self.targetDragItem then
				if self.targetDragItem == targetDragItem then
				else
					self.targetDragItem:OnDragExit(self)
					self.targetDragItem = targetDragItem
					self.targetDragItem:OnDragEnter(self)
				end
			else
				self.targetDragItem = targetDragItem
				self.targetDragItem:OnDragEnter(self)
			end
		else
			if self.targetDragItem then
				self.targetDragItem:OnDragExit(self)
				self.targetDragItem = nil
			else
				
			end
		end
    end

	uiDrag.m_EndDrag = function( eventData, go , x, y )
		Log.Info("m_EndDrag"..self.index)
		
		if self.curDragTeam then
			local team = self.curDragTeam
			local _,pos = self:SP2LP(x,y)
			
			local posBegin = Vector3.New(pos.x,pos.y,-500)
			local posEnd = BattleSceneManager:GetTeamPosByUid(team.teamUid)

			if self.targetDragItem then
				self.win:SwapHeroPos(self.index,self.targetDragItem.index)
			else
				self:MoveBack()
			end
		end

		self.curDragTeam = nil
		self.curDragHeroVo = nil
		if self.targetDragItem then
			self.targetDragItem:OnDragExit(self)
		end
    end

end
function DragItem:IsEmpty()
	if self.win then
		local item = self.win:GetBattleListDataByPos(self.index)
		if item then
			return false
		end
	end
	return true
end

function DragItem:SP2LP(x,y)
	self.v2Temp:Set(x,y)
	return UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(BattleSceneManager.parent,self.v2Temp,UIMgr.uiCamera)
end

function DragItem:OnDragEnter(dragItem)
	if self.isDragIn then return end
	if self:IsEmpty() then return end
	self.isDragIn = true

	local posA = self:GetCurTeam().battleDefaultPos
	local posB = dragItem:GetCurTeam().battleDefaultPos
	self:MoveTo(posA,posB)
end

function DragItem:OnDragExit(dragItem)
	if not self.isDragIn then return end
	if self:IsEmpty() then return end

	self.isDragIn = false
	self:MoveBack()
end

function DragItem:MoveTo(from,to,duration)

	if not duration then
		duration = 0.3
	end

	local team = self:GetCurTeam()
	if not team then
		return
	end

	AddDOTweenNumberDelay(0,1,duration,00,function (pct)
		local pos = Vector3.Lerp(from,to,pct)
		team:SetPosition(pos.x,pos.y,-500)
	end)
end

function DragItem:MoveBack(duration)
	local team = self:GetCurTeam()
	if not team then
		return
	end

	local endPos = BattleSceneManager:GetTeamPosByUid(team.teamUid)
	local beginPos = team:GetPosition()
	self:MoveTo(beginPos,endPos,duration)
end

function DragItem:GetCurTeam()
	local team = BattleSceneManager:GetTeamByUid(self.index)
	return team
end

--endregion

return UI_SlgChooseBattleView