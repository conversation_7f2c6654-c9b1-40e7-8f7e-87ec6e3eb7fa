local UI_UnionBossCall = Class(BaseView)

function UI_UnionBossCall:OnInit()
    EventMgr:Add(EventID.REFRESH_UNION_BOSS, self.OnUpdateInfo, self);
    self.selectIndex = 1;
    self.deltaTime = 0;
    self.time = 0;
    self.isCanSend = false;
    self.sendLimitTime = v2n(LeagueManager:GetConfigById(118));
end

function UI_UnionBossCall:OnCreate(bossId)
    self.bossId = bossId;
    
    local myDuty = LeagueManager:GetMyLeagueDuty()
    if myDuty ~= -1 then
        local pos_config = LeagueManager:GetUnionPosById(myDuty);
        if pos_config and pos_config.convene then
            self.isCanSend = pos_config.convene == 1;
        end
    end
    
    self:SetIsUpdateTick(true);
    self:OnUpdateInfo();
end

function UI_UnionBossCall:OnRefresh(param)
    
end

function UI_UnionBossCall:onDestroy()
    EventMgr:Remove(EventID.REFRESH_UNION_BOSS, self.OnUpdateInfo, self);
end

function UI_UnionBossCall:onUIEventClick(go,param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "m_imgSend" then
        if not self.isCanSend then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(9251));
            return;
        end

        local bossInfo = LeagueManager.curBossInfo;
        if bossInfo and bossInfo.hp <= 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000391));
            return ;
        end

        if LeagueManager:GetBossActEndTime() <= 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000388));
            return ;
        end
        
        if self.time <= 0 then
            LeagueManager:OnRequestBossInvite(function()
                local langId = self.selectIndex == 1 and 70000363 or 70000577;
                ChatManager:SendUniBossInvite(self.bossId, langId, LeagueManager.act_id, function()
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000385));
                end);
                self:OnUpdateInfo();
            end)
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70000626, self.time));
        end
    elseif string.startswith(name, "m_tog") then
        if self.ui[name].isOn == false then return end

        local index = string.gsub(name, "m_tog", "");
        index = v2n(index);
        if index then
            if self.selectIndex ~= index then
                self.selectIndex = index;
                self:OnUpdateSelect();
            end
        end
    end
end

function UI_UnionBossCall:TickUI(deltaTime)
    self.deltaTime = self.deltaTime + deltaTime;
    if self.deltaTime >= 1 then
        self.deltaTime = self.deltaTime - 1;

        if self.time > 0 then
            if self.time <= 1 then
                self.time = 0;
                self:OnUpdateInfo();
            else
                self.time = self.time - 1;
            end
            self.ui.m_txtSend.text = LangMgr:GetLangFormat(70000626, self.time);
        end
    end
end

function UI_UnionBossCall:OnUpdateInfo()
    local nowTime = TimeZoneMgr:GetServerStampWithServerZone();
    self.time = LeagueManager.invite_time + self.sendLimitTime - nowTime;
    if self.time <= 0 then
        self.ui.m_txtSend.text = LangMgr:GetLang(70000379);
        UnifyOutline(self.ui.m_txtSend, "424040");
        SetUIImage(self.ui.m_imgSend,"Sprite/ui_public/button3_huise.png", false);
    else
        if self.time > self.sendLimitTime then
            self.time = self.sendLimitTime;
        end
        self.ui.m_txtSend.text = LangMgr:GetLangFormat(70000626, self.time);
        UnifyOutline(self.ui.m_txtSend, "910C09");
        SetUIImage(self.ui.m_imgSend,"Sprite/ui_public/button3_yellow.png", false);
    end
    
    self:OnUpdateSelect();
end

function UI_UnionBossCall:OnUpdateSelect()
    local nameStr;
    local monsterHeroVo = LeagueManager:GetMonsterHeroVo(self.bossId);
    if monsterHeroVo then
        nameStr = monsterHeroVo:GetHeroName();
    end
    
    for i = 1, 2 do
        local item = self.ui["m_tog" .. i];
        local showSp = GetChild(item, "showSp", UEUI.Image);
        local showTxt = GetChild(item, "showTxt", UEUI.Text);
        
        local langId = i == 1 and 70000363 or 70000577;
        showTxt.text = LangMgr:GetLangFormat(langId, nameStr);
        
        if i == self.selectIndex then
            showTxt.color = GetColorByHex("DA551B");
            SetUIImage(showSp,"Sprite/ui_lianmeng/lianmengboss_zhaoji_laba2.png", false);
        else
            showTxt.color = GetColorByHex("3F77CC");
            SetUIImage(showSp,"Sprite/ui_lianmeng/lianmengboss_zhaoji_laba1.png", false);
        end
    end
end

return UI_UnionBossCall